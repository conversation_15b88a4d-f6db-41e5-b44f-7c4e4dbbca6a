stages:
  - build

workflow:
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'

startinstance:
   stage: .pre
   tags:
     - base
   before_script:
     - yum install awscli -y
     - aws configure set aws_access_key_id $ACCESS_KEY_ID; aws configure set aws_secret_access_key $SECRET_ACCESS_KEY
   script:
     - aws ec2 start-instances --instance-ids $INSTANCE_ID --region $AWS_RUNNER_REGION
     - aws ec2 wait system-status-ok --instance-ids $INSTANCE_ID --region $AWS_RUNNER_REGION
     - aws ec2 wait instance-status-ok --instance-ids $INSTANCE_ID --region $AWS_RUNNER_REGION
   timeout: 10 minutes

build_and_push_image:
   stage: build
   tags:
      - mainshell
   script:
      - cd docker_image
      - sudo docker build -t ${registry_url}/onboarding/mydb_mysql_health_report .
      - sudo mkdir -p /etc/docker/certs.d/${registry_url}/
      - openssl s_client -showcerts -connect ${registry_url}:443 < /dev/null | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTIFICATE-/p' | sudo tee /etc/docker/certs.d/${registry_url}/ca.crt > /dev/null
      - sudo docker login -u $robot_account_name -p $robot_token $registry_url
      - sudo docker push ${registry_url}/onboarding/mydb_mysql_health_report
      - sudo docker rmi ${registry_url}/onboarding/mydb_mysql_health_report

stopinstance:
   stage: .post
   tags:
     - base
   before_script:
     - yum install awscli -y
     - aws configure set aws_access_key_id $ACCESS_KEY_ID; aws configure set aws_secret_access_key $SECRET_ACCESS_KEY
   script:
     - aws ec2 stop-instances --instance-ids $INSTANCE_ID --region $AWS_RUNNER_REGION
   when: always
