
import os
import json
from util import response as resp
import util.Log as log
import util.LoadConfig as lc
from constants import constant as my_const

def write_report_file(details):
    try:
        with open('config/report_path.json', 'w', encoding='utf-8') as f:
            json.dump(details, f, ensure_ascii=False, indent=4, default=str)
            log.print_log("S","updated report path at local")
    except Exception as error_elog:
        return resp.MyResponse("E","update report_path",error_elog)

def create_report_file(file_name):
    try:
        with open(file_name, 'w') as outfile:  
            report = {"report_generate_by":my_const.global_settings.get('report_generate_by'),"file_names":{}}
            json.dump(report, outfile)
    except Exception as error_elog:
        return resp.MyResponse("E","tried to create new file, but failed",error_elog)

def update_report_files(new_file_name,report_for,created_date):
    return_list = []
    try:
        execution_file_name = 'config/report_path.json'
        is_exist = os.path.exists(execution_file_name)
        if is_exist == False:
            log.print_log("S","report_path.json does not exist ")
            err = create_report_file(execution_file_name)
            if err is not None:
                return return_list,err

        with open(execution_file_name, 'r') as openfile:
            file_details = json.load(openfile)
            if file_details == None or file_details != None and len(file_details.keys())==0:
                file_details = {"report_generate_by": my_const.global_settings.get('report_generate_by'), "file_names": {}}
            if file_details != None and len(file_details.keys())>1:
                report_generate_by = file_details.get("report_generate_by")
                file_names = file_details.get("file_names")
                if my_const.global_settings.get('report_generate_by') != None and report_generate_by != None and my_const.global_settings.get('report_generate_by') == report_generate_by:
                    report_list = file_names.get(report_for)
                    if report_list == None:
                        file_names[report_for] = []
                        report_list = file_names.get(report_for)
                    return_list = report_list.copy()
                    if len(report_list)>2:
                        report_list.pop(0)
                    report_list.append({created_date:new_file_name})
                    err = write_report_file(file_details)
                    if err is not None:
                        return return_list,err
                else:
                    err = create_report_file(execution_file_name)
                    if err is not None:
                        return return_list,err
                    _,err = update_report_files(new_file_name,report_for,created_date)
                    if err is not None:
                        return return_list,err
    except Exception as error_elog:
        return return_list,resp.MyResponse("E","update report_path failed",error_elog)
    return return_list,None
