[all]
#Default settigns starts here

#connection_type can be local / ssh
connection_type: local

#ssh_password [optional] : we can put password when we use password authentication
ssh_password:

db_type: mysql
# db_type: psql
# db_type: mongo

#aws_profile_name [optional] for RDS Instances.
aws_profile_name: default

#click host details
ch_host: pmm2-server
ch_port: 9000
ch_username: default
ch_password:
ch_db: pmm

#my_server [mandatory] , have to define ip address or RDS END Point here
my_server:
my_user: evergent_monitor
my_pass: g9IR5q6H7U1D#$BmoIR
#my_db [optional]
my_db:
my_port: 3306

#3 types of MYSQL Authentication simple/default/login_path
#1. simle : If set, have to define server,user,password and port in above
#2. default : If set, have to define mysql_login_file_path: /Users/<USER>/.my.cnf & mysql_login_section_name : client
#3. login_path : mysql_login_file_path: /Users/<USER>/.mylogin.cnf & mysql_login_section_name = login_name/login_path

db_login_type: simple
mysql_login_file_path: /Users/<USER>/.my.cnf
mysql_login_section_name: client

min_uptime_unused_index: 7

#new table statistics will only show, when recors are with in mentioned limit
max_tables_create_limit: 50
#ssh_name_filter=source

#default report type is monthly we can override this property on above mentioned levels
report_type: Monthly

pmm_base_url: https://**********:8443
pmm_user: mydbmonadmin
pmm_pass: j8ddQn0mlmBCNQtp
#pmm_dashboard: mydb_health_report
#pmm_dash_id: l7S7ngxMkv2
pmm_dashboard: mydbops_report
pmm_dash_id: l7S7ngxMkv2
graph_dir: graphs

# we can allocate different template_path for each group / instance, explained detaily on below section.
template_path: default.template
#template_path: postgres.template
#template_path: mongo.template

#above mentioned settings all can override from group wise settings.
#we can create N no groups here, please make sure create the group wise settings file(yaml file) for each group you were created. ex "weekly.yml"


#ssh_name_filter - if ssh name or rds identifier name doesnt has word "source" or "slave", we have to define explicitly using this parameter,this will be useful to restrict statistics execution
#ssh_name_filter: source

#graph_hosts, node_name and service_name can get from PMM Dashboard
# if  node_name and service_name are not defined will get default value as graph_hosts names
#region =  should mention if environment has instances/nodes in multiple regions
# if service_name and node_name were different, we have to define explicitly as below

#From here, only inventory details like group and instances with instance level arguments.
[monthly]
#Sample Config for EC2 or VPS Instances [can override all the above properties including group wise settings]
#ssh_name: my_server=ipaddress/mysql_server_ip graph_hosts=pmm2_host_id
prod-aurora-db: my_server=prod-aurora-db.cluster-cv87vflxcrgb.eu-west-1.rds.amazonaws.com graph_hosts=evergent_ireland_prod-aurora-db_writer_aurora_mysql node_name=evergent_ireland_prod-aurora-db_writer_aurora_mysql service_name=evergent_ireland_prod-aurora-db_writer_aurora_mysql ssh_name_filter=source

[slow_queries]
prod-aurora-db: my_server=prod-aurora-db.cluster-cv87vflxcrgb.eu-west-1.rds.amazonaws.com graph_hosts=evergent_ireland_prod-aurora-db_writer_aurora_mysql node_name=evergent_ireland_prod-aurora-db_writer_aurora_mysql service_name=evergent_ireland_prod-aurora-db_writer_aurora_mysql ssh_name_filter=source template_path=slow_query.template
#prod-aurora-db-ro: my_server=prod-aurora-db.cluster-cv87vflxcrgb.eu-west-1.rds.amazonaws.com graph_hosts=evergent_ireland_prod-aurora-db_writer_aurora_mysql node_name=evergent_ireland_prod-aurora-db_writer_aurora_mysql service_name=evergent_ireland_prod-aurora-db_writer_aurora_mysql ssh_name_filter=source template_path=slow_query.template
prod-aurora-db-ro: my_server=prod-aurora-db.cluster-ro-cv87vflxcrgb.eu-west-1.rds.amazonaws.com graph_hosts=evergent_ireland_prod-aurora-db_reader_aurora_mysql node_name=evergent_ireland_prod-aurora-db_reader_aurora_mysql service_name=evergent_ireland_prod-aurora-db_reader_aurora_mysql ssh_name_filter=replica template_path=slow_query.template