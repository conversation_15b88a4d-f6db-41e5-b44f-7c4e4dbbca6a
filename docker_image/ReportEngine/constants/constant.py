from constants import version
import string
import random
import time
from datetime import date, timedelta,datetime
# class MyConstants:
#GLOBAL APPLICATION SETTINGS

yes_no = ["yes","y",True]

aes_key = "Mydbops_LLP~Secure=Aes_Key"

execution_id = ""

template_name = ""

execution = {}

mydbops_report_path ="mydbops_report"

global_configur = {}

global_conf_list = [
    {"setting_name":"default_instance","default":"virtual","required":True},
    {"setting_name":"client_name","default":"","required":True},
    {"setting_name":"client_env_name","default":"","required":False},
    
    {"setting_name":"note_title","default":"","required":False},
    {"setting_name":"note_css_class","default":"","required":False},
    {"setting_name":"note_description","default":"","required":False},

    {"setting_name":"inventory_path","default":"./Config/server_inventory.cnf","required":True},
    {"setting_name":"inventory_default_group","default":"all","required":True},
    {"setting_name":"default_connection_type","default":"all","required":True},
    {"setting_name":"slow_query","default":"no"},
    # {"setting_name":"error_log","default":"yes","required":True},
    # {"setting_name":"error_log_path","default":"error.log","required":True},
    {"setting_name":"error_log_traceback","default":"yes","required":True},
    {"setting_name":"info_log","default":"yes","required":True},
    {"setting_name":"info_log_path","default":"info.log","required":True},
    {"setting_name":"ssh_path","default":"~/.ssh/config","required":True},
    {"setting_name":"report_generate_by","default":"group","required":True},
    {"setting_name":"report_generate_type","default":"pdf","required":True},
    {"setting_name":"report_store_path","default":"my_reports","required":True},
    {"setting_name":"client_key_server_token","default":"","required":True},
    {"setting_name":"client_key_server_hash","default":"","required":True},
    {"setting_name":"client_email","default":"","required":True},
    {"setting_name":"email_provider","default":"ses","required":True},
    {"setting_name":"event_post","default":"http://localhost:4031/events"},
    {"setting_name":"disable_ticket_stats","default":False,"required":True},
    {"setting_name":"disable_cloud_upload","default":False,"required":True},
    {"setting_name":"disable_send_mail","default":False,"required":True},
    {"setting_name":"email_content_template","default":"Hi \n Please click below file for monthly report \n <SIGNED_URL> \n Regards,\n Mydbops LLP","required":True}  
]

disable_ticket_stats = False

disable_cloud_upload = False

disable_send_mail = False

global_settings_required_empty = {}

global_settings ={}

__virtual_instance__ = ["all","","virtual",None]

can_modify_allowed_instance = True

bot_mode = False

group_only = False

inventory_list = {}

all_group_settings = {}

command_line_settings = {}

error_logger = None

info_logger = None

template_list = []

email_list = []

inventory_configuration = {
        "instance": "virtual",
        "client_name": "",
        "connection_type": "ssh",
        "db_type": "mysql",
        "my_lpath": "mydbopsd",
        "my_server": "localhost",
        "my_user": "",
        "my_pass": "",
        "my_db": "",
        "my_port": 3306,
        "base_dir": "/usr/local/mydbops/support-files/mydbops_report",
        "report_type": "Weekly",
        "pmm_version": "1",
        "pmm_base_url": "",
        "pmm_user": "pmmadmin",
        "pmm_pass": "MydbopsAdmin@17",
        "pmm_dashboard": "mydbops_report",
        "pmm_dash_id": "IJFlD5jiz2",
        "pmm_types": "mysql innodb",
        "graph_dir": "graphs",
        "graph_hosts": "",
        "client_mail": "",
        "db_access": "",
        "db_login_type": "default",#/default/login_path
        "mysql_login_file_path": "~/.my.cnf",
        "mysql_login_section_name": "client",
        "pmm_cookie": None
}



default_response = {
	"duration": "",
    "type": "Application",
    "tag": "report",
    "name": "mydbhealthreport",
    "executionID": "",
    "taskID": 0,
    "serverConfigID": 0,
    "templateName": "",
    "hostName": "",
    "taskStatus": "",
    "message": "",
    # "stackTrace": "",
    # "module": "",
    # "notifyBy": "",
	"startTime": "",
    "endTime": "",
    "output": {},
    # "version": "mydbmonthlyreport,enterprise edition, Version : "+version.VERSION +", Build Date : "+version.BUILD_DATE,
    "version": version.VERSION,
}

start_dt = None

end_dt = None
