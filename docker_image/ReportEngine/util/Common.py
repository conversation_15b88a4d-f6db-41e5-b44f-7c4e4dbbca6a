"""
Author : Vetrivel.N
Org : Mydbops
Date : 16-12-2021
"""

from numpy import double
import util.Log as log
import base64
from os.path import exists
from datetime import datetime as dt
from constants import constant as my_const
import string
import random
import time
from string import Formatter
import re
from json2html import *

yes_no = ["yes","y",True]
aes_key = "Mydbops_LLP~Secure=Aes_Key"

def bytes_to_readable(num, suffix="B"):
    if type(num) == str:
        num = int(num)
    for unit in ["", "Ki", "Mi", "Gi", "Ti", "Pi", "Ei", "Zi"]:
        if abs(num) < 1024.0:
            return f"{num:3.1f}{unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f}Yi{suffix}"

def convert_path_to_image(file_name):
    baseimage = ""
    with open(file_name, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('ascii')
        baseimage = encoded_string
        #html_str += '<img  src="data:image/png;base64,'+baseimage+'" />'
    return baseimage

def read_file(file_path):
    file_exists = exists(file_path)
    if file_exists:
        fd = open(file_path, 'r')
        Filedata = fd.read()
        fd.close()
        return Filedata
    else:
       raise Exception('Could not open file : '+ file_path)

def write_file(file_path,input):
    fd = open(file_path, 'w')
    Filedata = fd.write(input)
    fd.close()

def analyse_response(result_list,report_info=None):
    design_template = report_info.get("design_template")
    error_string = ""
    final_result = []
    for item in result_list:
        item = str(item)
        if item != "":
            if design_template is not None and  design_template.get("exclude") is not None:
                exclude = design_template.get("exclude")
                should_exclude = False
                for exclude_item in exclude:
                    if exclude_item == "starts_with":    
                        if item.startswith(tuple(exclude.get(exclude_item))):
                            # final_result.append(item)
                            should_exclude = True
                    elif exclude_item == "ends_with":    
                        if item.endswith(tuple(exclude.get(exclude_item))):
                            # final_result.append(item)
                            should_exclude = True
                    elif exclude_item == "contains" and exclude.get(exclude_item) != None and len(exclude.get(exclude_item))>0:
                        if any(x in item for x in exclude.get(exclude_item)):
                        # if item not in exclude.get(exclude_item):
                            # final_result.append(item)
                            should_exclude = True
                            break
                if should_exclude == True:
                    error_string += str(item)
                else:
                    final_result.append(item)    
            else:
                final_result.append(item)
    if error_string != "":
        log.print_log("W","Common.analyse_response, from node : ",error_string)    
    return final_result

def chech_for_eval(lst_script,header_text,value_text,last_header):
    out_header = None
    out_value = None
    removed_column_index = None
    if lst_script is not None:
        for script_item in lst_script:
            actual_header = script_item.get("text_match_header")
            new_header = script_item.get("replace_header_text")
            remove_from_chart = script_item.get("remove_from_chart")
            run_code = script_item.get("run_code")
            if actual_header is None:
                actual_header = ""
            if new_header is None:
                new_header = ""
            if run_code is True:
                py_code = script_item.get("py_code")
                if py_code is not None and py_code != "":
                    try:
                        op = {}
                        py_code = py_code.replace("in_put",header_text)
                        exec(py_code, globals(), op)
                        return_workaround = op.get("out_put")
                        if return_workaround != None and return_workaround != "":
                            out_header = return_workaround
                    except Exception as eval_err:
                        # out_value = value_text
                        # log.print_log("E","Common.chech_for_eval: ",eval_err)
                        raise eval_err
            elif header_text != None:
                if header_text in actual_header:
                    if new_header is not None and new_header != "":
                        out_header = new_header
                    if remove_from_chart != None and remove_from_chart != "":
                        removed_column_index = remove_from_chart
            elif (last_header != None and last_header != "" and value_text != None and (last_header in actual_header or last_header in new_header)):
                py_code = script_item.get("py_code")
                if py_code is not None and py_code != "":
                    X = value_text
                    try:
                        op = {}
                        if type(value_text) != str:
                            value_text = str(value_text)
                        py_code = py_code.replace("in_put",value_text)
                        exec(py_code, globals(), op)
                        return_workaround = op.get("out_put")
                        if return_workaround != None and return_workaround != "":
                            out_value = return_workaround
                    except Exception as eval_err:
                        # out_value = value_text
                        # log.print_log("E","Common.chech_for_eval: ",eval_err)
                        raise eval_err

    return out_header, out_value,removed_column_index

def item_generator(json_input, lookup_key):
    res = json_input.get(lookup_key)
    if res is not None:
        return res
    else:
        return {}

def parse_json_to_text(report_info,server,column_details,rows,delimiter,schema):
    if schema == "" or schema == None:
        schema = "N/A"
    result_list = []
    for idx, row in  enumerate(rows):
        if idx > 0 :
            tmp_html = ""
            for col_key in column_details:
                col_detail = column_details[col_key]
                path = col_detail.get("path")
                formula = col_detail.get("formula")
                if path != None:
                    pathlst = path.split(".")
                    tdata = ""
                    if len(pathlst)>1:
                        nex_row = row.copy()
                        for lpath in pathlst:
                            nex_row = item_generator(nex_row, lpath)
                        if nex_row != {}:
                            tdata = str(nex_row)
                        else:
                            tdata = ""
                    else:
                        tdata = row.get(path)
                    if formula != None and tdata != {} and tdata != "":
                        tdata = formula.replace("X",tdata)
                        tdata = eval(tdata)
                    else:
                        pass
                    if tdata == None:
                        tdata = ""
                    if type(tdata) == float or type(tdata) == double:
                        tdata = round(tdata, 2)
                    if tmp_html == "":
                        tmp_html += str(tdata)
                    else:
                        tmp_html += delimiter + str(tdata)
            if tmp_html != "":
                result_list.append(schema + delimiter + tmp_html)    
    return result_list


def initDefaultResponse():
    # global default_response,execution_id,bot_mode,start_dt
    my_const.start_dt = dt.today()
    my_const.execution_id = str(time.time_ns())+''.join(random.choices(string.ascii_letters, k=3))
    my_const.bot_mode = False
    my_const.default_response["tag"] = "report"
    my_const.default_response["executionID"] = my_const.execution_id
    my_const.default_response["startTime"] =  my_const.start_dt.strftime("%Y-%m-%d %H:%M:%S")

def strfdelta(tdelta, fmt='{D:02}d {H:02}h {M:02}m {S:02}s', inputtype='timedelta'):
    """Convert a datetime.timedelta object or a regular number to a custom-
    formatted string, just like the stftime() method does for datetime.datetime
    objects.

    The fmt argument allows custom formatting to be specified.  Fields can 
    include seconds, minutes, hours, days, and weeks.  Each field is optional.

    Some examples:
        '{D:02}d {H:02}h {M:02}m {S:02}s' --> '05d 08h 04m 02s' (default)
        '{W}w {D}d {H}:{M:02}:{S:02}'     --> '4w 5d 8:04:02'
        '{D:2}d {H:2}:{M:02}:{S:02}'      --> ' 5d  8:04:02'
        '{H}h {S}s'                       --> '72h 800s'

    The inputtype argument allows tdelta to be a regular number instead of the  
    default, which is a datetime.timedelta object.  Valid inputtype strings: 
        's', 'seconds', 
        'm', 'minutes', 
        'h', 'hours', 
        'd', 'days', 
        'w', 'weeks'
    """

    # Convert tdelta to integer seconds.
    if inputtype == 'timedelta':
        remainder = int(tdelta.total_seconds())
    elif inputtype in ['s', 'seconds']:
        remainder = int(tdelta)
    elif inputtype in ['m', 'minutes']:
        remainder = int(tdelta)*60
    elif inputtype in ['h', 'hours']:
        remainder = int(tdelta)*3600
    elif inputtype in ['d', 'days']:
        remainder = int(tdelta)*86400
    elif inputtype in ['w', 'weeks']:
        remainder = int(tdelta)*604800

    f = Formatter()
    desired_fields = [field_tuple[1] for field_tuple in f.parse(fmt)]
    possible_fields = ('W', 'D', 'H', 'M', 'S')
    constants = {'W': 604800, 'D': 86400, 'H': 3600, 'M': 60, 'S': 1}
    values = {}
    for field in possible_fields:
        if field in desired_fields and field in constants:
            values[field], remainder = divmod(remainder, constants[field])
    return f.format(fmt, **values)

def calculate_execution_duration():
    # global default_response,start_dt,end_dt
    my_const.end_dt = dt.today()
    duration = my_const.end_dt - my_const.start_dt
    my_const.default_response["endTime"] =  my_const.end_dt.strftime("%Y-%m-%d %H:%M:%S")
    my_const.default_response["duration"] =  duration.seconds

def get_index_of_string_from_list(x, iterable):
    for i, item in enumerate(iterable):
        if item == x:
            return i
    return None

def replace_tokens(input,search,server):
    for search_text in search:
        replace_value = server.get(search_text)
        if replace_value is not None:
            input = input.replace("{{"+search_text+"}}", replace_value)
    return input

def replace_command_tokens(command,server):
    pattern = '{{(.+?)}}'

    if type(command) == str:
        result = re.findall(pattern, command)
        if result:
            command = replace_tokens(command,result,server)
    elif type(command) == list:
        for i in range(0,len(command)):
            text = command[i]
            result = re.findall(pattern, text)
            if result:
                command[i] = replace_tokens(text,result,server)
    else:
        raise("Invalid command, command must be a list or a string")
    return command

def get_boolean(input_text):
    if input_text == "no":
        return False
    elif input_text == "yes":
        return True
    else:
        return None