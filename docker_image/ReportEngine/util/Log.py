"""
Author : Vetrivel.N
Org : Mydbops
Date : 24-12-2021

"""

import logging
import re
import traceback
from datetime import datetime
from logging import handlers
from constants import constant as my_const


info_logger = None
error_logger = None
info_path = ""
error_path = ""

def set_info(log_path):
    global info_logger,info_path
    log = logging.getLogger("InfoLog")
    log.handlers = []
    log.setLevel(logging.INFO)
    #handler = logging.FileHandler(log_path)
    handler = handlers.RotatingFileHandler(log_path, maxBytes=1024*1024*30, backupCount=10)

    handler.setFormatter(logging.Formatter('%(asctime)s %(levelname)s %(message)s'))
    log.addHandler(handler)
    info_logger = log
    info_path = log_path


def set_error(log_path):
    global error_logger,error_path
    log = logging.getLogger("ErrorLog")
    log.handlers = []
    log.setLevel(logging.ERROR)
    #handler = logging.FileHandler(log_path)
    handler = handlers.RotatingFileHandler(log_path, maxBytes=1024*1024*30, backupCount=10)
    handler.setFormatter(logging.Formatter('%(asctime)s %(levelname)s %(message)s'))
    log.addHandler(handler)
    error_logger = log
    error_path = log_path

def print_log(etype,info,error_module=""):
    # print(etype,info,error_module)

    if etype == "S" and info_logger != None:
        info_logger.info(my_const.execution_id +" "+ str(info)+" >>> "+str(error_module))

    elif etype == "W" and info_logger != None:
        info_logger.info(my_const.execution_id +" "+ str(info)+" >>> "+str(error_module))

    elif etype == "E" and info_logger != None:
        trace_back = my_const.global_settings.get("error_log_traceback") 
        if trace_back != None and trace_back =="yes":
            trace = traceback.format_exc()
            if trace != None and trace.strip() != "NoneType: None":
                # print(trace)
                info_logger.error("------------------------------------------------------------------------------------------------------------------------------------------------------------------------------")
                #info_logger.error(str(info)+" >>> "+str(error_module))
                info_logger.error(str(info))
                info_logger.error("execution id "+str(my_const.execution_id))
                info_logger.error(trace)
                info_logger.error("------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n")
            else:
                info_logger.error(my_const.execution_id +" "+ str(info)+" >>> "+str(error_module)+"\n")
        else:
            info_logger.error(my_const.execution_id +" "+ str(info)+" >>> "+str(error_module)+"\n")


def current_time():
    return str(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))+"  :  "
 
def print_log_file(etype,info,error_module=""):
    # print(etype,info,error_module)

    if etype == "S" and info_logger != None:
        file_handler = open(info_path,"a")
        file_handler.write(current_time()+" "+my_const.execution_id +" "+ str(info)+" >>> "+str(error_module)+"\n")
        file_handler.close()

    elif etype == "W" and error_logger != None:
        file_handler = open(info_path,"a")
        file_handler.write(current_time()+" "+my_const.execution_id +" "+ str(info)+" >>> "+str(error_module)+"\n")
        file_handler.close()

    elif etype == "E" and error_logger != None:
        file_handler = open(error_path,"a")

        trace_back = my_const.global_settings.get("error_log_traceback") 
        if trace_back != None and trace_back =="yes":
            trace = traceback.format_exc()
            if trace != None and trace.strip() != "NoneType: None":
                file_handler.write("------------------------------------------------------------------------------------------------------------------------------------------------------------------------------"+"\n")
                file_handler.write("execution id :" + my_const.execution_id)
                file_handler.write(current_time()+str(info)+" >>> "+str(error_module)+"\n")
                file_handler.write(current_time()+str(trace)+"\n")
                file_handler.write("------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n")
            else:
                file_handler.write(current_time()+" "+my_const.execution_id +" "+ str(info)+" >>> "+str(error_module)+"\n")
        else:
            file_handler.write(current_time()+" "+my_const.execution_id +" "+ str(info)+" >>> "+str(error_module)+"\n")
        file_handler.close()
