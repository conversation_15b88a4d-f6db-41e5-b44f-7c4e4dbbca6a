"""
Author : Vetrivel.N
Org : Mydbops
Date : 16-12-2021

"""

#from util.ConfigParser import ConfigParser
#import Common as common
from util import response as resp
from .ConfigParser import ConfigParser
import yaml
import sys
from datetime import date, timedelta,datetime
import util.Log as log
#from .Log import Log as log
import os
from . import Common as common
import requests
from . import Paging
from os.path import exists
import base64
import json
from constants import constant as my_const

class LoadConfig:

    def init(self):
        try:
            # self.my_const = my_const
            err = self.__load_templates__()
            if err is not None:
                return err
                
            err = self.__load_email_templates__()
            if err is not None:
                return err

            my_const.mydbops_report_path = './config/'+my_const.mydbops_report_path+'.ini'
            err = self.__process_global_settings__()
            if err is not None:
                return err

            err = self.__load_cmd_line_arguments__()
            if err is not None:
                return err

            if  my_const.disable_ticket_stats is False or  my_const.disable_cloud_upload is False or my_const.disable_send_mail is False:
                err = self.__get_aws_and_mail_details()
                if err is not None:
                    return err

            err = self.__process_config__()
            if err is not None:
                return err

        except Exception as err:
            return resp.MyResponse("E","LoadConfig.__init__ ",err)
    
    def __load_templates__(self):    
        template_list = [x for x in os.listdir("template/") if x.endswith(".template")]
        if len(template_list)>0:
            template_dict = {}
            for template in template_list:
                try:
                    with open("template/"+template) as f:
                        yml_data = yaml.load(f, Loader=yaml.FullLoader)
                        template_dict[template] = yml_data
                except Exception as error:
                    return resp.MyResponse("E","LoadConfig.__load_templates__",error)
            my_const.template_list = template_dict
        else:
            return resp.MyResponse("E","LoadConfig.__load_templates__ "+" : Templates Files not available")
            
    def __load_email_templates__(self):    
        template_list = [x for x in os.listdir("template/") if x.endswith(".email") or x.endswith(".html")]
        if len(template_list)>0:
            template_dict = {}
            for template in template_list:
                try:
                    with open("template/"+template) as f:
                        html_temp_email = f.read()
                        template_dict[template] = html_temp_email
                except Exception as error:
                    return resp.MyResponse("E","LoadConfig.__load_email_templates__",error)
            my_const.email_list = template_dict
        else:
            return resp.MyResponse("E","LoadConfig.__load_templates__ "+" : Email Templates not available")
        
    def __load_cmd_line_arguments__(self):
        my_const.default_response["templateName"] = "notification"
        cmd_arguments = sys.argv
        for cmd_key in cmd_arguments:
            key = ""
            value =""
            key_value = cmd_key.split('=', maxsplit=1)
            if len(key_value)>1:
                key = key_value[0].replace("--","")
                value = key_value[1]
            else:
                key = cmd_key.replace("--","")
            if 'report_type' == key:
                my_const.global_settings["report_execution_type"] = value
            if "-d" == key:
                my_const.bot_mode = True
            elif "template-name" == key:
                my_const.default_response["templateName"] = value
            elif "task-id" == key:
                my_const.default_response["taskID"] = value
            elif "server-config-id" == key:
                my_const.default_response["serverConfigID"] = value
            elif "group" in key:
                value = value.split(",")
                if value!= None and len(value)>0:
                    for each_group in value:
                        if each_group not in my_const.inventory_list and each_group!="":
                            my_const.inventory_list[each_group] = []
                    if len(my_const.inventory_list)>0:
                        my_const.group_only = True
            elif key in my_const.global_settings:
                my_const.global_settings[key] = value
            elif value!="":
                if key == "instance" or key == "connection_type" :
                    my_const.global_settings["filter_instance"] = self.__split_string_to_list__(",",value)
                    my_const.can_modify_allowed_instance = False
                else:
                    my_const.command_line_settings[key] = value
    
    def validata_global_config(self,gsection,goption,default_value="",required=False):
        try:
            value = my_const.global_configur.get(gsection,goption)
        except Exception as global_config_reader_error:
            return resp.MyResponse("E","File missing or not formed as per standard (error when read from file): mydbops_report.ini ",global_config_reader_error)

        if value!= None and value.strip() != "":
            my_const.global_settings[goption] = value
        else:
            if required == True:                
                my_const.global_settings[goption] = default_value
                my_const.global_settings_required_empty[goption] = ("Global settings should not be empty (REQUIRED.L2): "+str(goption))
                return resp.MyResponse("E","Global settings should not be empty: ",goption)
            else:    
                my_const.global_settings[goption] = default_value
                my_const.global_settings_required_empty[goption] = ("Global settings should not be empty (REQUIRED.L1): "+str(goption))
                return resp.MyResponse("E","Global settings should not be empty: ",goption)

    def is_valid(self,text):
        valid = False
        if text is not None and text != "":
            valid = True
        return valid


    def __process_global_settings__(self):
        my_const.global_configur = ConfigParser()
        try:
            my_const.global_configur.read(my_const.mydbops_report_path)
            
            for global_conf in my_const.global_conf_list:
                err = self.validata_global_config("Settings",global_conf.get("setting_name"),global_conf.get("default"),global_conf.get("required"))
                if err is not None:
                    return err
            
            # if my_const.global_settings["error_log"] == "yes" and my_const.global_settings["error_log_path"]!="":
            #     log.set_error(my_const.global_settings["error_log_path"])
            if my_const.global_settings["info_log"] == "yes" and my_const.global_settings["info_log_path"]!="":
                log.set_info(my_const.global_settings["info_log_path"])
           
            if my_const.global_settings["disable_ticket_stats"] == "yes":
                my_const.disable_ticket_stats = True
            if my_const.global_settings["disable_cloud_upload"] == "yes":
                my_const.disable_cloud_upload = True
            if my_const.global_settings["disable_send_mail"] == "yes":
                my_const.disable_send_mail = True

            if my_const.global_settings["ssh_path"] !="":
                err = self.read_ssh_config()
                if err is not None:
                    return err
            for item in my_const.global_settings_required_empty:
                return resp.MyResponse("E",str(my_const.global_settings_required_empty[item]))

            
            my_const.global_settings["index_ticket_content"] = ""
            my_const.global_settings["index_content"] = ""
            my_const.global_settings["index_subhead_content"] = ""
            my_const.global_settings["index_subhead_sub_content"] = ""

            my_const.global_settings["paging"] = Paging.Paging()
            
            my_const.global_settings["critical_stats"] = ""
            my_const.global_settings["ticket_handled"] = ""
            my_const.global_settings["graph"] = {}

            sections = my_const.global_configur._sections
            if sections != None :
                graph_section = sections.get("Graph")
                if graph_section != None and len(graph_section)>0:
                    for graph_sec_key in graph_section:
                        if graph_sec_key.startswith("p"):
                            #my_const.global_settings["graph"][]
                            my_const.global_settings["graph"][graph_sec_key] = graph_section.get(graph_sec_key)
                            pass

            report_path = my_const.global_settings.get("report_store_path")
            isdir = os.path.isdir(report_path)
            if not isdir:
                os.makedirs(report_path)

            graph_path = my_const.global_settings.get("report_store_path")+"/graphs/"
            isdir = os.path.isdir(graph_path)
            if not isdir:
                os.makedirs(graph_path)
            #if my_const.global_settings["ssh_key_file"] !="":
            #    my_const.global_settings["ssh_key_file"] = self.find_full_path(my_const.global_settings.get("ssh_key_file"))
            try:
                temp_email_content = my_const.email_list.get(my_const.global_settings["email_content_template"])
                if temp_email_content != None and temp_email_content != "":
                    my_const.global_settings["email_html_content"] = temp_email_content
                else:
                    return resp.MyResponse("E","LoadConfig.__could not read email content file_ : "+my_const.global_settings["email_content_template"])
            except Exception as error_email_file:
                return resp.MyResponse("E","LoadConfig.__could not read email content file__",error_email_file)
        except Exception as error_gs:
            return resp.MyResponse("E","File missing or not formed as per standard : mydbops_report.ini ",error_gs)

    def get_allowed_instance(self):
        return my_const.global_settings["default_instance"]

    def __split_string_to_list__(self,delimiter,input_str):
        new_list = []
        input_list = input_str.split(delimiter)
        for each_item in input_list:
            if each_item != "" and each_item not in new_list :
                new_list.append(each_item)
        return  new_list

    def __update_default_values_from_ini__(self,group_settings,group_key,global_key):
        if group_settings.get(group_key) == None:
            if my_const.global_settings[global_key] != None:
                group_settings[group_key] = my_const.global_settings[global_key]
            else:
                return None,resp.MyResponse("E","__process_config__","group wise & .ini files settings both are empty : "+group_key)
        return group_settings,None

    def __process_config__(self):    
        configur = ConfigParser()
        configur.read(my_const.global_settings.get("inventory_path"))
        sections = configur.sections()
        all_sections = configur._sections
        inventory_default_group = my_const.global_settings.get("inventory_default_group")
        temp_default_settings = all_sections.get(inventory_default_group)
        if temp_default_settings == None:
            return resp.MyResponse("E","__process_config__","Inventory may not available , check Inventory path in mydbops_report.ini OR Inventory config at : "+my_const.global_settings.get("inventory_path"))
        my_const.inventory_configuration.update(temp_default_settings)
        default_settings = my_const.inventory_configuration
        for section_items in all_sections:
            
            if section_items != inventory_default_group and (my_const.group_only == False or  (my_const.group_only == True and section_items in my_const.inventory_list)):
                #if section_items not in my_const.inventory_list:
                #    my_const.inventory_list[section_items]=[]
                server_list= all_sections.get(section_items)
                try:
                    current_default_settings = default_settings.copy()
                    current_default_settings,err = self.__update_default_values_from_ini__(current_default_settings,"instance","default_instance")
                    if err is not None:
                        return err
                    current_default_settings,err = self.__update_default_values_from_ini__(current_default_settings,"client_name","client_name")
                    if err is not None:
                        return err
                    current_default_settings,err = self.__update_default_values_from_ini__(current_default_settings,"connection_type","default_connection_type")
                    if err is not None:
                        return err
                    
                    server_settings = {}
                    final_group_settings = {}
                    group_file = './config/'+section_items+'.yml'
                    file_exists = exists(group_file)
                    if file_exists == True:
                        with open(group_file) as f:
                            group_info = yaml.load(f, Loader=yaml.FullLoader)
                            group_settings = group_info.get("Settings")
                            current_default_settings.update(group_settings)
                            final_group_settings = current_default_settings.copy()

                    for server in server_list:
                        individual_server_settings = {}
                        individual_settings = server_list.get(server)
                        is_server_details_valid = True
                        if individual_settings != None and individual_settings != "":
                            individual_settings_list = individual_settings.split(" ")
                            for each_parameter in individual_settings_list:
                                each_parameter_list = each_parameter.split("=",1)
                                if len(each_parameter_list)>1:
                                    individual_server_settings[each_parameter_list[0]] = each_parameter_list[1]
                                else:
                                    each_parameter_list = each_parameter.split(":",1)
                                    if len(each_parameter_list)>1 and each_parameter_list[1] != '':
                                        individual_server_settings[each_parameter_list[0]] = each_parameter_list[1]
                                    else:
                                        is_server_details_valid = False
                                        return resp.MyResponse("E","Loadconfig.__process_config__.misconfiguration at server level arguments/parameter : "+server)
                        
                        if is_server_details_valid == False:
                            continue
                        server_setting = current_default_settings.copy()
                        if len(individual_server_settings)>0:    
                            server_setting.update(individual_server_settings)    

                        if len(my_const.command_line_settings)>0:
                            server_setting.update(my_const.command_line_settings)
                        
                        # if server_setting.get("instance") !=None and my_const.global_settings.get("filter_instance") != None and server_setting.get("instance") not in my_const.global_settings["filter_instance"]:
                        #     del server_setting
                        #     continue
                        
                        # if server_setting.get("connection_type") !=None and my_const.global_settings.get("filter_connection_type") != None and server_setting.get("connection_type") not in my_const.global_settings["filter_connection_type"]:
                        #     del server_setting
                        #     continue
                        template_data = None
                        template_name = server_setting.get("template_path")
                        if template_name != None:
                            template_data = my_const.template_list.get(template_name)
                            if template_data is not None:
                                server_setting["template"] = template_data
                            else:
                                return resp.MyResponse("E","Loadconfig.__process_config__.template details empty : "+server)
                                # del server_settings[server]
                                continue
                        else:
                            return resp.MyResponse("E","Loadconfig.__process_config__.template not found : "+server)
                            # del server_settings[server]
                            continue
                        #if template exist proceed further
                        stats_order = template_data.get("stats_order")
                        if stats_order is None:
                            return resp.MyResponse("E","Loadconfig.__process_config__.template's stats_order is not found : "+server)
                            # del server_settings[server]
                            continue
                        
                        is_server_details_valid = True
                        enabled_stats = []
                        for stats_key in stats_order:
                            stats_details = stats_order.get(stats_key)
                            if stats_details is not None:
                                is_active = False
                                for detail in stats_details:
                                    if detail.get("active") is not None and detail.get("active") == True:
                                        is_active = True
                                
                                if my_const.disable_ticket_stats is False and (stats_key == "ticket_stats" or stats_key == "critical_stats") and is_active:
                                    enabled_stats.append(stats_key)
                                    api_aws_email = my_const.global_settings.get("api_aws_email")
                                    if api_aws_email is None or (api_aws_email is not None and (api_aws_email.get("tk_server") is None or api_aws_email.get("tk_user") is None or api_aws_email.get("tk_pass") is None or api_aws_email.get("tk_port") is None)):
                                        return resp.MyResponse("E","ticket_stat's db details are not found for  "+stats_key+" , server name : "+server)
                                       
                                elif stats_key == "server_stats" and is_active:
                                    enabled_stats.append(stats_key)
                                    if server_setting.get("my_server") != None and ".rds." in server_setting["my_server"] and (server_setting.get("aws_profile_name") == None or server_setting.get("aws_profile_name") == ""):
                                        return resp.MyResponse("E","AWS profile name empty at server_stats ")
                                       
                                elif stats_key == "sql_stats" and is_active:
                                    enabled_stats.append(stats_key)
                                    if server_setting.get("db_login_type") is not None and server_setting.get("db_login_type") == "simple":
                                        if self.is_valid(server_setting.get("my_server")) is False or self.is_valid(server_setting.get("my_user"))  is False or self.is_valid(server_setting.get("my_pass"))  is False or self.is_valid(server_setting.get("my_port")) is False :
                                            return resp.MyResponse("E","Server's DB details were missing simple: "+server)
                                           
                                    elif server_setting.get("db_login_type") is not None and (server_setting.get("db_login_type") == "default" or  server_setting.get("db_login_type") =="login_path"):
                                        if self.is_valid(server_setting.get("mysql_login_file_path")) is False or self.is_valid(server_setting.get("mysql_login_section_name")) is False:
                                            return resp.MyResponse("E","Server's DB details were missing "+server_setting.get("db_login_type")+" : "+server)
                                            
                                elif stats_key == "graph_stats" and is_active:
                                    enabled_stats.append(stats_key)
                                    if self.is_valid(server_setting.get("pmm_base_url")) is False or self.is_valid(server_setting.get("pmm_user"))  is False or self.is_valid(server_setting.get("pmm_pass"))  is False or self.is_valid(server_setting.get("pmm_dashboard")) is False or self.is_valid(server_setting.get("pmm_dash_id")) is False or self.is_valid(server_setting.get("graph_hosts"))  is False or self.is_valid(server_setting.get("service_name")) is False :
                                        return resp.MyResponse("E","Server's PMM details were missing graph_stats: "+server)
                                      
                                elif stats_key == "qan_analysis" and is_active:
                                    enabled_stats.append(stats_key)
                                    if self.is_valid(server_setting.get("pmm_base_url")) is False or self.is_valid(server_setting.get("pmm_user"))  is False or self.is_valid(server_setting.get("pmm_pass"))  is False or self.is_valid(server_setting.get("pmm_dashboard")) is False or self.is_valid(server_setting.get("pmm_dash_id")) is False or self.is_valid(server_setting.get("graph_hosts"))  is False:
                                        return resp.MyResponse("E","Server's PMM details were missing qan_analysiss: "+server)
                                       
                        server_setting["enabled_stats"] = enabled_stats
                        if is_server_details_valid == False:
                            continue
                        if server_setting.get("report_type") != None and str(server_setting.get("report_type")).lower() == "weekly":
                            current_date = date.today()
                            from_time = str(current_date - timedelta(8)) + " 00:00:00"
                            to_time = str(current_date - timedelta(1))+ " 23:59:59"
                            server_setting["from_time"] = from_time
                            server_setting["to_time"] = to_time

                            my_const.global_settings["from_time"] = str(current_date - timedelta(8))
                            my_const.global_settings["to_time"] = str(current_date - timedelta(1))

                        elif server_setting.get("report_type") != None and str(server_setting.get("report_type")).lower() == "monthly":
                            current_date = date.today()
                            to_time = date.today().replace(day=1) - timedelta(days=1)
                            from_time = date.today().replace(day=1) - timedelta(days=to_time.day)
                            server_setting["from_time"] = str(from_time) +" 00:00:00"
                            server_setting["to_time"] = str(to_time) + " 23:59:59"

                            if my_const.global_settings.get("from_time") == None and my_const.global_settings.get("to_time") == None:
                                my_const.global_settings["from_time"] = str(from_time)
                                my_const.global_settings["to_time"] = str(to_time)

                        elif server_setting.get("report_type") != None and str(server_setting.get("report_type")).lower() == "custom":
                            try:
                                from_time = server_setting["from_time"].split(" ")
                                if len(from_time) > 0 and from_time[0].count('-')==2:
                                    if len(from_time)==1 or server_setting["from_time"].count(':')!=2:
                                        from_time[0] = datetime.strptime(from_time[0], "%Y-%m-%d").strftime('%Y-%m-%d')
                                        server_setting["from_time"] = from_time[0]+" 00:00:00"
                                    else:
                                        server_setting["from_time"] = datetime.strptime(server_setting["from_time"], "%Y-%m-%d %H:%M:%S").strftime('%Y-%m-%d %H:%M:%S')

                                else:
                                    return resp.MyResponse("E","From_Time not in correct format"+str(server_setting["from_time"]))
                                    # del server_settings[server]
                                    continue

                                to_time = server_setting["to_time"].split(" ")
                                if len(to_time) > 0 and to_time[0].count('-')==2:    
                                    if len(to_time)==1 or server_setting["to_time"].count(':')!=2:
                                        to_time[0] = datetime.strptime(to_time[0], "%Y-%m-%d").strftime('%Y-%m-%d')
                                        server_setting["to_time"] = to_time[0]+ " 23:59:59"
                                    else:
                                        server_setting["to_time"] = datetime.strptime(server_setting["to_time"], "%Y-%m-%d %H:%M:%S").strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    return resp.MyResponse("E","To_Time not in correct format"+str(server_setting["to_time"]))
                                    # del server_settings[server]
                                    continue

                                my_const.global_settings["from_time"] = from_time[0]
                                my_const.global_settings["to_time"] = to_time[0]
                            except Exception as edt:
                                return resp.MyResponse("E","From_Time or To_Time not in correct format : "+str(server_setting["from_time"])+" , "+str(server_setting["to_time"]),edt)
                                # del server_settings[server]
                                continue
                        else:
                            return resp.MyResponse("E","LoadConfig report_type is not valid : "+str(server_setting.get("report_type")))
                            continue
                        if server_setting.get("instance") not in my_const.__virtual_instance__ :
                            server_setting["db_access"] = "direct"
                        
                        if server_setting.get("ssh_name") == None or ( server_setting.get("ssh_name") != None and server_setting.get("ssh_name") == ""):
                            server_setting["ssh_name"] = server
                        
                        if server_setting.get("graph_hosts") == None or server_setting.get("graph_hosts") =="":
                            server_setting["graph_hosts"] = server_setting["ssh_name"]

                        server_setting["mysql_filter"] = {}

                        if server_setting.get("db_type") != None and server_setting.get("db_type") == "mysql":

                            if "sql_stats" in server_setting["enabled_stats"]:
                                if server_setting.get("db_login_type") != None and server_setting.get("db_login_type") == "simple":
                                    server_setting["mysql_details"] = ["mysql"]
                                    if server_setting.get("my_server") != None and server_setting.get("my_server") != "":
                                        server_setting["mysql_details"].extend(["-h",str(server_setting.get("my_server"))])
                                        
                                        if server_setting.get("my_user") != None and server_setting.get("my_user") != "":
                                            server_setting["mysql_details"].extend(["-u",str(server_setting.get("my_user"))])

                                            if server_setting.get("my_pass") != None and server_setting.get("my_pass") != "":
                                                server_setting["mysql_details"].extend(["-p'"+str(server_setting.get("my_pass"))+"'"])
                                                # server_setting["mysql_details"].extend(["-p"+str(server_setting.get("my_pass"))])
                                                
                                            if server_setting.get("my_port") != None and server_setting.get("my_port") != "":
                                                server_setting["mysql_details"].extend(["-P",str(server_setting.get("my_port"))])
                                                
                                            if server_setting.get("my_db") != None and server_setting.get("my_db") != "":
                                                server_setting["mysql_details"].extend([str(server_setting.get("my_db"))])
                                                
                                if server_setting.get("db_login_type") != None and server_setting.get("db_login_type") == "default":
                                    if server_setting.get("mysql_login_file_path") != None and server_setting.get("mysql_login_file_path") != "" and server_setting.get("mysql_login_section_name") != None and server_setting.get("mysql_login_section_name") != "" :
                                        server_setting["mysql_details"] = ["mysql","--defaults-file=" + str(server_setting.get("mysql_login_file_path"))]
                                
                                if server_setting.get("db_login_type") != None and server_setting.get("db_login_type") == "login_path":
                                    if server_setting.get("mysql_login_file_path") != None and server_setting.get("mysql_login_file_path") != "" and server_setting.get("mysql_login_section_name") != None and server_setting.get("mysql_login_section_name") != "" :
                                        server_setting["mysql_details"] = ["mysql","--login-path=" + str(server_setting.get("mysql_login_section_name"))]
                        
                        elif server_setting.get("db_type") != None and server_setting.get("db_type") == "psql":
                            if "sql_stats" in server_setting["enabled_stats"]:
                                if server_setting.get("db_login_type") != None and server_setting.get("db_login_type") == "simple":
                                    server_setting["mysql_details"] = []
                                    if server_setting.get("my_server") != None and server_setting.get("my_server") != "":
                                        server_setting["mysql_details"].extend(["-h",str(server_setting.get("my_server"))])
                                        
                                        if server_setting.get("my_user") != None and server_setting.get("my_user") != "":
                                            server_setting["mysql_details"].extend(["-U",str(server_setting.get("my_user"))])
    
                                            if server_setting.get("my_port") != None and server_setting.get("my_port") != "":
                                                server_setting["mysql_details"].extend(["-p",str(server_setting.get("my_port"))])
                                                
                                            if server_setting.get("my_db") != None and server_setting.get("my_db") != "":
                                                server_setting["mysql_details"].extend(["-d",str(server_setting.get("my_db"))])

                                            if server_setting.get("my_pass") != None and server_setting.get("my_pass") != "":
                                                # tmp = ["PGPASSWORD='"+str(server_setting.get("my_pass"))+"'","psql"].append(server_setting["mysql_details"])
                                                server_setting["mysql_details"].insert(0, "psql")
                                                server_setting["mysql_details"].insert(0, "&&")
                                                server_setting["mysql_details"].insert(0, "PGPASSWORD='"+str(server_setting.get("my_pass")+"'"))
                                                server_setting["mysql_details"].insert(0, "export")
                                                # server_setting["mysql_details"].insert(0, "postgres")
                                                # server_setting["mysql_details"].insert(0, "-it")
                                                # server_setting["mysql_details"].insert(0, "exec")
                                                # server_setting["mysql_details"].insert(0, "docker")
                                                # server_setting["mysql_details"].insert(0, "sudo")
                                                
                                                # server_setting["mysql_details"].insert(1, "PGPASSWORD='"+str(server_setting.get("my_pass"))+"'")
                                                # server_setting["mysql_details"].insert(2, "psql")
                                else:
                                    return resp.MyResponse("E","invalid db login type : "+str(server_setting.get("db_login_type")+" for server : "+server_setting["ssh_name"]))

                        elif server_setting.get("db_type") != None and server_setting.get("db_type") == "mongo":
                            
                            pass
                        else:
                            return resp.MyResponse("E","LoadConfig invalid db_type : "+str(server_setting.get("db_type")))
                            continue

                        if server_setting.get("client_name") != None and server_setting.get("client_name") == "":
                            server_setting["client_name"] = my_const.global_settings.get("client_name")
                        
                        # if (server_setting.get("my_server") == None or server_setting.get("my_server") == "") and ".rds." in server_setting.get("ssh_name") :
                        #     server_setting["my_server"] = server_setting["ssh_name"]

                        server_setting["graph_dir"] = None
                        #if server_settings.get(server) != None:
                        
                        
                        
                        if server_setting != None and server_setting != {}:
                            server_settings[server] = server_setting

                    #print(server_settings)
                    if len(server_settings)>0:
                        my_const.all_group_settings[section_items] = final_group_settings
                        my_const.inventory_list[section_items] = server_settings
                        
                except FileNotFoundError as f404 :
                    return resp.MyResponse("E","Loadconfig.__process_config__.File Not Found",f404)
                except Exception as error_all:
                    return resp.MyResponse("E","Loadconfig.__process_config__ALLexception",error_all)

    def read_ssh_config(self):
        try:
            data = {}
            #ssh_conf = os.path.expanduser(my_const.global_settings.get("ssh_path"))
            my_const.global_settings["ssh_path"] = self.find_full_path(my_const.global_settings.get("ssh_path"))
            ssh_conf = self.find_full_path(my_const.global_settings.get("ssh_path"))
            line = common.read_file(ssh_conf)
            if line != None:
                text = line.split('\n')
                last_host = ""
                for row in text:
                    if row.find("Host ") != -1:
                        host = row.replace("Host ","")
                        data[host] = []
                        last_host = host
                    elif len(data)>0:
                        key_val = row.strip().split(" ")
                        if len(key_val)>1:
                            key = key_val[0].strip()
                            val = key_val[1].strip()
                            if key.lower() == "identityfile":
                                val = os.path.expanduser(val).strip()
                            
                            if len(data[last_host])>0:
                                data[last_host].update({key:val})
                            else:
                                data[last_host] = {key:val}
            else:
                return resp.MyResponse("E","Loadconfig.__process_config__. SSH config file not found")
                # exit(0)
            my_const.global_settings["ssh_host"] = data
        except Exception as error_file:
            return resp.MyResponse("E","Loadconfig.__process_config__.File Not Found",error_file)
            # exit(0)
    
    def get_global_settings(self):
        return my_const.global_settings

    def find_full_path(self,file_name):
        if str(file_name).startswith("~"):
            file_name = os.path.expanduser(file_name)
        else:
            file_name = file_name
        return file_name


    def get_actual_key_serverurl(self,url_base64):
        try:
            return base64.b64decode(base64.b64decode(url_base64)).decode("utf-8")
        except:
            return ""
        
    def __get_aws_and_mail_details(self):
        client_key_server_url = self.get_actual_key_serverurl(my_const.global_settings.get("client_key_server_hash"))
        client_key_server_token = my_const.global_settings.get("client_key_server_token")
        client_name = my_const.global_settings.get("client_name")
        if client_key_server_url != None and client_key_server_url != "" and client_key_server_token != None and client_key_server_token !="":
            try:
                response = requests.get(client_key_server_url+"user_token="+client_key_server_token+"&client_name="+client_name,timeout=30)
                # response = requests.get("http://localhost:8081/get_details_for_send_mail?user_token=b4ea417ae67954048578d0d4f1724df5",timeout=30)
                if response != None:
                    response_text = json.loads(response.text)
                    if response_text.get("type") == True:
                        credentials_for_aws_and_email = response_text.get("details")

                        my_const.global_settings["api_aws_email"] = credentials_for_aws_and_email

                        if credentials_for_aws_and_email.get("tk_server") != None and credentials_for_aws_and_email.get("tk_port") != None and credentials_for_aws_and_email.get("tk_user") != None and credentials_for_aws_and_email.get("tk_pass") != None :
                            dbconfig = {"host":credentials_for_aws_and_email.get("tk_server"),"port":credentials_for_aws_and_email.get("tk_port"),"user":credentials_for_aws_and_email.get("tk_user"),"password":credentials_for_aws_and_email.get("tk_pass")}
                            my_const.global_settings["api_tk"] = dbconfig
                            my_const.global_settings["api_tk_db"] = credentials_for_aws_and_email.get("tk_db")
                    else:
                        return resp.MyResponse("E","Loadconfig error in keyserver response : ",str(response.text))
                else:
                    return resp.MyResponse("E","Loadconfig error in keyserver response ","")
            except Exception as e:
                return resp.MyResponse("E","Loadconfig error in keyserver request ",e)
        else:
            return resp.MyResponse("E","Loadconfig CHECK arguments: *** client_key_server_url & client_key_server_token *** ","")

