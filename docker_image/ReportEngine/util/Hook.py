
import util.Log as log
import instance.Local as Local
from instance.VirtualInstance import VirtualInstance as vi
import requests
from util import response as resp,Common as common
import json

def load_pre_hooks(server_setting,server):
    #Load pmm graph detail
    if "graph_stats" in server_setting["enabled_stats"]:
        try:
            if  server_setting["pmm_base_url"] != None and server_setting["pmm_base_url"] != "" and server_setting["pmm_user"] != None and server_setting["pmm_pass"] != None and server_setting["pmm_dashboard"] != None and server_setting["pmm_dash_id"] != None :
                if (server_setting["pmm_base_url"]).endswith("/"):
                    server_setting["pmm_base_url"] = (server_setting["pmm_base_url"])[:-1]

                server_setting["pmm_url_login"] = (server_setting["pmm_base_url"])+"/graph/login"
                server_setting["pmm_url_graph"] = (server_setting["pmm_base_url"])+"/graph/render/d-solo/"+str(server_setting["pmm_dash_id"])+"/"+str(server_setting["pmm_dashboard"])+"?"
                log.print_log("S","Going TO PMM Server Login ")
              
                if server_setting.get("pmm_api_key") is not None and server_setting.get("pmm_api_key") != "":
                    log.print_log("S","PMM API KEY Used : "+server+" , "+str(server_setting.get("pmm_api_key"))) 
                else:
                    data = {"user":server_setting["pmm_user"],"password":server_setting["pmm_pass"]}
                    response = requests.post(server_setting["pmm_url_login"],json=data,verify=False,timeout=10)
                    server_setting["pmm_cookie"] = response.cookies
                    log.print_log("S","PMM Response text : "+server+" , "+str(response.text))
                    log.print_log("S","PMM Response cookies : "+server+" , "+str(response.cookies))
                    if response.status_code != 200:
                        return server_setting,resp.MyResponse("E","PMM login failed, http status code : "+str(response.status_code)+" , ressponse :" + str(response.text))


                period_start_from = server_setting["from_time"].replace(" ","T")+"+00:00"
                period_start_to   = server_setting["to_time"].replace(" ","T")+"+00:00"
                server_setting["period_start_from"] = period_start_from
                server_setting["period_start_to"] = period_start_to
                # try:
                #     server_setting["pmm_url_qan_filters"] =  (server_setting["pmm_base_url"])+"/v0/qan/Filters/Get"
                #     server_setting["pmm_url_qan_report"] =  (server_setting["pmm_base_url"])+"/v0/qan/GetReport"
                #     period_start_from = server_setting["from_time"].replace(" ","T")+"+00:00"
                #     period_start_to   = server_setting["to_time"].replace(" ","T")+"+00:00"
                #     server_setting["period_start_from"] = period_start_from
                #     server_setting["period_start_to"] = period_start_to
                # except Exception as qanerr:
                #     return server_setting, resp.MyResponse("E","Loadconfig.__process_config__.QAN GET FILTERS failed2: "+server,qanerr)
                    
            else:
                return server_setting, resp.MyResponse("E","PMM validation failed at internal hook : "+server)
                
        except Exception as pmm_error:
            return server_setting, resp.MyResponse("E","exception when try to PMM login at internal hook",pmm_error)
            
    if "sql_stats" in server_setting["enabled_stats"]:
        if server_setting.get("min_uptime_unused_index") != None and server_setting.get("min_uptime_unused_index") != "" and server_setting.get("mysql_details") != None:
            try:
                min_uptime_unused_index = int(server_setting["min_uptime_unused_index"])
                if server_setting.get("db_type") == "mysql": 
                    mysql_uptime_qry = common.read_file("sql_query/mysql/uptime_days.sql")
                    if mysql_uptime_qry != None and mysql_uptime_qry != "":
                        mysql_details = server_setting["mysql_details"].copy()
                        mysql_uptime_response = None
                        if server_setting["connection_type"] == "local":
                            mysql_details.extend(["-e",mysql_uptime_qry])
                            for ix,ite in enumerate(mysql_details):
                                if ite.startswith("-p") and "'" in ite:
                                    mysql_details[ix] = ite.replace("'","")
                            # mysql_details = " ".join(mysql_details)+""" -e \" """+mysql_uptime_qry+""" \" """
                            mysql_uptime_response,err = Local.send_command(mysql_details)
                            if err is not None:
                                return server_setting,err

                        elif server_setting["connection_type"] == "ssh":
                            execute_command = " ".join(mysql_details)+""" -e \" """+mysql_uptime_qry+""" \" """
                            try:
                                ov = vi(server_setting)
                                mysql_uptime_response,err = ov.send_command(execute_command)
                                if err is not None:
                                    return server_setting,err
                                    
                            except Exception as emysql:
                                return server_setting, resp.MyResponse("E","exception when try to get mysql uptime at internal hook e1 ",str(emysql.get("result").get("stderr")))

                        if mysql_uptime_response != None and len(mysql_uptime_response) > 1:
                            up_idx = common.get_index_of_string_from_list("uptime_days",mysql_uptime_response)
                            if up_idx is not None:
                                up_final_value = mysql_uptime_response[up_idx+1:]
                                if up_final_value != None and len(up_final_value) > 0:
                                    server_min_uptime_unused_index = int(up_final_value[0])
                                    if server_min_uptime_unused_index >= min_uptime_unused_index:
                                        server_setting["mysql_filter"]["min_uptime_unused_index"] = True
                            else:
                                return server_setting, resp.MyResponse("E","exception when try to get mysql uptime at internal hook e2 ",mysql_uptime_response)
                elif server_setting.get("db_type") == "psql":
                    mysql_uptime_qry = common.read_file("sql_query/psql/uptime_days.sql")
                    if mysql_uptime_qry != None and mysql_uptime_qry != "":
                        mysql_details = server_setting.get("mysql_details").copy()
                        if server_setting["connection_type"] == "local":
                            # mysql_details.extend(["-c",'"'+mysql_uptime_qry+'"'])
                            mysql_details = " ".join(mysql_details)+""" -c \" """+mysql_uptime_qry+""" \" """
                            sql_uptime_response,err = Local.send_command(mysql_details)
                            if err is not None:
                                return server_setting,err

                        elif server_setting["connection_type"] == "ssh":
                            execute_command = " ".join(mysql_details)+""" -c \" """+mysql_uptime_qry+""" \" """
                            try:
                                ov = vi(server_setting)
                                sql_uptime_response,err = ov.send_command(execute_command)
                                if err is not None:
                                    return server_setting,err
                                    
                            except Exception as emysql:
                                return server_setting, resp.MyResponse("E","exception when try to get mysql uptime at internal hook e3 ",str(emysql.get("result").get("stderr")))

                        if sql_uptime_response != None and len(sql_uptime_response) > 1:
                            up_idx = common.get_index_of_string_from_list(" uptime_days ",sql_uptime_response)
                            up_final_value = sql_uptime_response[up_idx+1:]
                            if up_final_value != None and len(up_final_value) > 0:
                                server_min_uptime_unused_index = int(up_final_value[1].strip())
                                if server_min_uptime_unused_index >= min_uptime_unused_index:
                                    server_setting["mysql_filter"]["min_uptime_unused_index"] = True
                    pass 
            except Exception as all_mysql_uptime_errors:
                return server_setting, resp.MyResponse("E","exception when try to parsing uptime filter at internal hook e4 ",all_mysql_uptime_errors)

    if server_setting.get("max_tables_create_limit") != None and server_setting.get("max_tables_create_limit") != "":
        try:
            server_setting["mysql_filter"]["max_tables_create_limit"] = int(server_setting.get("max_tables_create_limit"))
        except Exception as max_tables_create_limit:
            return server_setting,resp.MyResponse("E","exception when try to get mysql uptime at internal hook e5 ",max_tables_create_limit)
    return server_setting,None