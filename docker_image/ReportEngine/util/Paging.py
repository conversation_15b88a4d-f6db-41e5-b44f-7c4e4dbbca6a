"""
Author : Vetrivel.N
Org : Mydbops
Date : 10-2-2022
"""

class Paging:
    h2 = 0
    h3 = 0
    h4 = 0
    h5 = 0
    h6 = 0
    
    def __init__(self):
        self.clear_counter()

    def clear_counter(self):
        self.h2 = 0
        self.h3 = 0
        self.h3 = 0
        self.h4 = 0
        self.h5 = 0
        self.h6 = 0

    def add_h2(self):
        self.h2 += 1
        self.h3 = 0
        self.h4 = 0
        self.h5 = 0
        self.h6 = 0
        return self.header_to_text()

    def add_h3(self):
        self.h3 += 1
        self.h4 = 0
        self.h5 = 0
        self.h6 = 0
        return self.header_to_text()

    def add_h4(self):
        self.h4 += 1
        self.h5 = 0
        self.h6 = 0
        return self.header_to_text()

    def add_h5(self):
        self.h5 += 1
        self.h6 = 0
        return self.header_to_text()

    def add_h6(self):
        self.h6 += 1
        return self.header_to_text()

    def header_to_text(self):
        try:
            header_text = ""
            header_text += str(self.h2)+"." if self.h2 != 0 else ""
            header_text += str(self.h3)+"." if self.h3 != 0 else ""
            header_text += str(self.h4)+"." if self.h4 != 0 else ""
            header_text += str(self.h5)+"." if self.h5 != 0 else ""
            header_text += str(self.h6)+"." if self.h6 != 0 else ""
            if header_text != "":
                header_text += " " 
            return header_text
        except Exception as epage:
            raise epage

    def minus_h2(self):
        self.h2 -= 1
        self.h3 = 0
        self.h4 = 0
        self.h5 = 0
        self.h6 = 0
        return self.header_to_text()
    
    def minus_h3(self):
        self.h3 -= 1
        self.h4 = 0
        self.h5 = 0
        self.h6 = 0
        return self.header_to_text()

    def minus_h4(self):
        self.h4 -= 1
        self.h5 = 0
        self.h6 = 0
        return self.header_to_text()

    def minus_h5(self):
        self.h5 -= 1
        self.h6 = 0
        return self.header_to_text()

    def minus_h6(self):
        self.h6 -= 1
        return self.header_to_text()