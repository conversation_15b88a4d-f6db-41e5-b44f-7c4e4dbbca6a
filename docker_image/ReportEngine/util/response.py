import requests
from constants import constant as my_const
from . import Common as common
import traceback
import pprint
import util.Log as log
import json

class MyResponse:
    type = ""
    message = ""
    data = {}
    err_mode = None



    def __init__(self,type,message = "",err_mode = None,data = {}):
        self.type = type
        self.message = message
        self.err_mode = err_mode
        self.data = data

    def log_message(self,type,message = "",err_mode = None,data = {}):
        self.type = type
        self.message = message
        self.err_mode = err_mode
        self.data = data

    def post_event(self,my_event):
        try:
            uri = my_const.global_settings.get("event_post")
            if uri is not None:
                if my_event is None:
                    my_event = my_const.default_response
                template_name = ""
                if my_const.template_name != "" :
                    template_name = my_const.template_name
                else:
                    template_name = "notification"
                
                temp_event = json.dumps(my_event)
                event = {"check":{"status":0,"handlers":["mydbops-health-report-handler"],"output":temp_event,"metadata":{"name":template_name}}}
                # response = requests.post(url=self.url,data={'type':self.type, 'message':self.message, 'err_mode':self.err_mode, 'data':self.data},timeout=30)
                dump_event = json.dumps(event)
                response = requests.post(url=uri,data= dump_event,timeout=30)
                log.print_log("S",response.text)
            else:
                log.print_log("W","event url is empty")
        except Exception as e:
            log.print_log("W","error when posting event to berlin",e)

    def print_message(self,):
        common.calculate_execution_duration()
        my_event = my_const.default_response
        my_event["taskStatus"] = self.type
        report_type = ""
        if my_const.global_settings.get("report_execution_type") is not None:
            report_type = my_const.global_settings.get("report_execution_type")+"_report"
            my_event["tag"] = report_type

        if self.type in ["S","E"] and my_event["templateName"] == "notification":
            my_event["templateName"] = report_type
        
        try:
            #add output dict to my_event
            db_type = []
            connection_type = []
            server_names = []
            groups = []
            # ,"from_time","to_time"
            key_list =["connection_type","db_type","enabled_stats","ssh_name","node_name","mongo_clusters"]
            inventory = my_const.inventory_list.copy()
            for group in inventory:
                if group not in groups:
                    groups.append(group)
                server_list = inventory.get(group)
                if server_list is not None:
                    for server_id in server_list:
                        if server_id not in server_names:
                            server_names.append(server_id)
                        server = server_list.get(server_id)
                        if server is not None:
                            for key in server.copy():
                                if key == "db_type" and server.get(key) not in db_type:
                                    db_type.append(server.get(key))
                                if key == "connection_type" and server.get(key) not in connection_type:
                                    connection_type.append(server.get(key))
                                if key not in key_list:
                                    del server[key]
            output= {
                "client": my_const.global_settings.get("client_name"),
                "client_env": my_const.global_settings.get("client_env_name"),
                "client_email": my_const.global_settings.get("client_email"),
                "email_enabled": not common.get_boolean(my_const.global_settings.get("disable_send_mail")),
                "email_provider": my_const.global_settings.get("email_provider"),
                "report_execution_type": my_const.global_settings.get("report_execution_type"),
                "connection_type":connection_type,
                "db_type":db_type,
                "inventory": inventory,
                "from_time": my_const.global_settings.get("from_time"),
                "to_time": my_const.global_settings.get("to_time"),
                "server_names": server_names,
                "groups": groups,
            }

            my_event["output"] = output
        except Exception as eo:
            log.print_log("E","error when preparing output to create response",eo)

        if self.err_mode is None:
            my_event["message"] = self.message
        else:
            my_event["message"] = str(self.err_mode)
            my_event["error"] = self.message

        my_event["version"] = my_const.version.VERSION
        trace_back = my_const.global_settings.get("error_log_traceback") 
        if trace_back != None and trace_back =="yes" and self.err_mode is not None and type(self.err_mode) != str:
            trace = traceback.format_exc()
            str_trace = ""
            if type(self.err_mode) != list:
                trace = traceback.extract_tb(self.err_mode.__traceback__)
                for item in traceback.StackSummary.from_list(trace).format():
                    if str_trace == "":
                        str_trace =  item
                    else:
                        str_trace = "~ "+item
            else:
                str_trace = " ".join(self.err_mode)
            if str_trace != None and str_trace.strip() != "NoneType: None":
                my_event["trace"] = str_trace
        try:
            self.post_event(my_event)
        except Exception as e:
            log.print_log("E","error when posting event to berlin",e)

        log.print_log(self.type,self.message+" , data :"+str(self.data),self.err_mode)
        log.print_log(self.type,str(my_event))
        pp = pprint.PrettyPrinter(indent=4)
        pp.pprint(my_event)
