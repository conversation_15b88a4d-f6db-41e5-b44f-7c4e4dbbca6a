from util import Common as common, response as resp
from constants import constant as my_const
import requests
from datetime import datetime
import base64
from urllib.parse import urlencode

def analyze_graph_response(report_info,server):
    html_str = ""
    pmm_cookie = server.get("pmm_cookie")
    pmm_api_key = server.get("pmm_api_key")
    if pmm_cookie  is not None or pmm_api_key is not None:
        report_store_path = my_const.global_settings.get("report_store_path")
        global_settings = my_const.global_settings.get("graph")
        graph_settings = ""
        for graph in report_info.get("graphs"):
            css_class = report_info.get("design_template").get("css_class")
            graph_settings_dict = report_info.get("design_template").get("graph_settings")
            graph_settings = urlencode(graph_settings_dict)
            if graph_settings != None and graph_settings!= "":
                custom_python_function = None
                if type(graph) == dict:
                    panelid = next(iter(graph))
                    custom_python_function  = graph.get(panelid)
                    graph = panelid
                    panelid  = graph.replace("p","")
                else:
                    panelid  = graph.replace("p","")
                pmm_url_graph = server.get("pmm_url_graph")
                period_start_from = server.get("period_start_from")
                period_start_to = server.get("period_start_to")
                # print(period_start_from,period_start_to)
                period_start_from = str(round(datetime.fromisoformat(period_start_from).timestamp())*1000)
                period_start_to = str(round(datetime.fromisoformat(period_start_to).timestamp())*1000)
                if custom_python_function != None and custom_python_function != "":
                    if custom_python_function == "chage_from_dt_to_previous_day":
                        period_start_from = str(int(period_start_to) - 86400000)
                graph_host = server.get("graph_hosts")
                # pmm_dash_id = server.get("pmm_dash_id")
                # pmm_dashboard = server.get("pmm_dashboard")
                graph_name = ""
                graph_description = ""
                graph_name_ary = global_settings.get(graph)
                if graph_name_ary != None:
                    graph_name_ary = graph_name_ary.split(":")
                    if graph_name_ary != None and len(graph_name_ary)>1 :
                        graph_name = (graph_name_ary[0]).strip()
                        final_description = ""
                        tmp_description = graph_name_ary[1].strip()
                        tmp_description_list = tmp_description.split(". ")
                        for description_item in tmp_description_list:
                            if description_item !="":
                                final_description += '<li>'+ description_item.replace("<br>","") + '</li>'
                        graph_description = '<p class="description"><ul>'+ final_description +'</ul></p>'
                    else:
                        graph_name = (graph_name_ary[0]).strip()
                
                node_name = graph_host
                service_name = graph_host
                region = ""

                if server.get("node_name") != None and server.get("node_name") !="":
                    node_name = server.get("node_name")
                
                if server.get("service_name") != None and server.get("service_name") !="":
                    service_name = server.get("service_name")
                
                if server.get("region") != None and server.get("region") !="":
                    region = "&var-region="+server.get("region")

                graph_settings = graph_settings +"&panelId="+panelid+"&var-host="+graph_host+"&from="+period_start_from+"&to="+period_start_to+"&var-service_name="+service_name+"&var-node_name="+node_name+region
                pmm_final_req_url = pmm_url_graph + graph_settings
                if pmm_cookie is not None:
                    response = requests.get(pmm_final_req_url, cookies = pmm_cookie,verify=False)
                elif pmm_api_key is not None and pmm_api_key != "": 
                    headers = {"Authorization":"Bearer "+pmm_api_key}
                    response = requests.get(pmm_final_req_url, headers= headers,verify=False)
                if response.status_code == 200:
                    if css_class != "unite":
                        html_str += '<div class="dbreak">'
                    else:
                        html_str += '<div>'
                    if report_info.get("individual_graph_name"):
                        # graph_name = global_settings.get(graph)
                        header_no_text = my_const.global_settings["paging"].add_h5()
                        html_str += '<h5 id="'+header_no_text.strip()+'">'+header_no_text+graph_name.replace("_"," ")+'</h5>'
                        my_const.global_settings["index_subhead_sub_content"] += '<tr><td><h5 class="index_header"><a href="#'+header_no_text.strip()+'">'+header_no_text+str(graph_name.replace("_"," "))+"</a></h5></td></tr>"
                    encoded_string = base64.b64encode(response.content).decode()
                    baseimage = encoded_string
                    if report_info.get("description_after_chart") is not None and report_info.get("description_after_chart") == True:
                        html_str += '<img  src="data:image/png;base64,'+baseimage+'" />' + graph_description
                    else:
                        html_str += graph_description + '<img  src="data:image/png;base64,'+baseimage+'" />'
                    html_str += '</div>'
                else:
                    raise Exception("error accessing pmm graph status code: "+str(response.status_code)+" - "+pmm_final_req_url)
        if html_str !="":
            html_str = '<div class="'+css_class+'">' + html_str+ '</div>'
            pass
    return html_str
