from constants import constant as my_const
from util import response as resp
import util.Log as log
from . import tasks

def generate_report_from_template(server,server_idx):
    try:
        html_dict = {}
        enabled_stats = server.get("enabled_stats")
        template = server.get("template")
        report_template = template.get("stats_order")
        description = template.get("description")
        if description is None:
            description = {}
        node_counter = False
        header_no_text_node = ""
        header_no_sql = ""
        header_no_qan = ""
        header_graph_text = ""
        temp_index_content = ""
        temp_index_sql_content = ""
        temp_index_ticket_content = ""
        temp_ticket_handled = ""
        description_at_top = ""
        description_at_bottom = ""
        for report_cat in report_template:
            description_at_top = ""
            description_at_bottom = ""
            statistics_description = description.get(report_cat)
            if statistics_description != None:
                if statistics_description.get("top") is not None and statistics_description.get("top") !="":
                    description_at_top = "<br>" + statistics_description.get("top") + "<br>"
                if statistics_description.get("bottom") is not None and statistics_description.get("bottom") !="":
                    description_at_bottom = "<br>" + statistics_description.get("bottom") + "<br>"
            log.print_log("S","Common.process_report_template, entered stats -> " +report_cat)
            report_list = report_template[report_cat]
            if (report_cat != "ticket_stats" and report_cat != "critical_stats" ) and node_counter == False:
                header_no_text_node = my_const.global_settings["paging"].add_h2()
                node_counter = True
            if report_cat == "sql_stats":
                header_no_sql = my_const.global_settings["paging"].add_h3()
                my_const.global_settings["index_subhead_content"] = ""
            if report_cat == "qan_analysis":
                header_no_qan = my_const.global_settings["paging"].add_h3()
                my_const.global_settings["index_subhead_content"] = ""
            if report_cat == "graph_stats":
                header_graph_text = my_const.global_settings["paging"].add_h3()
                my_const.global_settings["index_subhead_content"] = ""
                my_const.global_settings["index_subhead_sub_content"] = ""
            try:
                mongo_clusters = server.get("mongo_clusters")
                is_4plus_cluster = server.get("is_4plus_cluster")
                db_type = server.get("db_type")
                if db_type is not None and db_type == "mongo" and is_4plus_cluster is not None and is_4plus_cluster == "yes" and report_cat == "server_stats" and mongo_clusters is not None and mongo_clusters != "":
                    modify_report_list(mongo_clusters,report_list)

                for report_item in report_list:
                    if report_cat != "sql_stats" and report_cat != "graph_stats" and report_cat != "qan_analysis":
                        my_const.global_settings["index_subhead_content"] = ""
                        my_const.global_settings["index_subhead_sub_content"] = ""
                        
                    report_item["report_cat"] =report_cat
                    if report_cat in enabled_stats and my_const.disable_ticket_stats is False and (report_cat == "ticket_stats" or report_cat == "critical_stats" ) and (report_item.get("active") in my_const.yes_no):
                        if my_const.global_settings.get("ticket_handled")  != "":
                            break

                        dbconfig = my_const.global_settings.get("api_tk")
                        if dbconfig is None:
                            return "",resp.MyResponse("E","Common.process_report_template,credentials not avail for 'ticket server'")
                            

                        if report_item.get("ssh_name_filter") is not None:
                            if ((any(elem in server.get("ssh_name") for elem in report_item.get("ssh_name_filter")) == True) or ((server.get("ssh_name_filter") != None) and (any(elem in server.get("ssh_name_filter") for elem in report_item.get("ssh_name_filter"))  == True))) == False:
                                continue

                        if report_cat == "ticket_stats":
                            if report_item.get("design_template") is None:
                                if template.get("ticket_stats_template") is not None:
                                    report_item["design_template"] = template.get("ticket_stats_template")
                                else:
                                    return "",resp.MyResponse("E","Common.process_report_template,template not avail for 'ticket_stats'")
                        else:
                            if report_item.get("design_template") is None:
                                if template.get("critical_stats_template") is not None:
                                    report_item["design_template"] = template.get("critical_stats_template")
                                else:
                                    return "",resp.MyResponse("E","Common.process_report_template,template not avail for 'critical_stats'")
                        
                        # dbconfig = {"host":server.get("tk_server"),"port":server.get("tk_port"),"user":server.get("tk_user"),"password":server.get("tk_pass")}
                        
                        qdata,errct = tasks.execute_task(report_item,server,dbconfig= dbconfig)
                        if errct is not None:
                            return "",errct
                        if qdata !=None and qdata != "":
                            
                            header_no_text = my_const.global_settings["paging"].add_h2()
                            str_description = ""
                            if report_item.get("description") != None and report_item.get("description") != "":
                                str_description = '<p class="description">'+report_item["description"]+'</p>'
                            html_content = '<h2 id="'+header_no_text.strip().replace(".","_")+'">'+header_no_text+str(report_item.get("report_name"))+'</h2>'+str_description
                            index_title = '<h2 class="index_header" ><a href="#'+header_no_text.strip().replace(".","_")+'">'+header_no_text+str(report_item.get("report_name"))+'</a></h2>'
                            temp_index_ticket_content += "<tr><td>"+index_title+"</td></tr>"
                            html_content += qdata
                            # html_content += '<div class="break"></div>'
                            # if report_cat == "ticket_stats":
                            #     html_content += '<div class="break"></div>'
                            

                            temp_ticket_handled += html_content
                            
                    elif report_cat in enabled_stats and (report_cat == "server_stats" and (report_item.get("active") in my_const.yes_no)):#report_item.get("command") is not None or
                        if report_item.get("design_template") is None:
                            if template.get("server_stats_template") is not None:
                                report_item["design_template"] = template.get("server_stats_template")
                            else:
                                return "",resp.MyResponse("E","Common.process_report_template,template not avail for 'server_stats'")

                        if report_item.get("ssh_name_filter") is not None:
                            if ((any(elem in server.get("ssh_name") for elem in report_item.get("ssh_name_filter")) == True) or ((server.get("ssh_name_filter") != None) and (any(elem in server.get("ssh_name_filter") for elem in report_item.get("ssh_name_filter"))  == True))) == False:
                                continue

                        header_no_text = my_const.global_settings["paging"].add_h3()
                        data,err_server = tasks.execute_task(report_item,server)
                        if err_server is not None:
                            return "",err_server
                        if data !=None and data != "":
                            html_content = '<h3 id="'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</h3>' + description_at_top
                            index_title = '<h3 class="index_header" ><a href="#'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</a></h3>'
                            temp_index_content += "<tr><td>"+index_title+"</td></tr>"+my_const.global_settings["index_subhead_content"]
                            str_description = ""
                            if report_item.get("description") != None and report_item.get("description") != "":
                                str_description = '<p class="description">'+report_item["description"]+'</p>'

                            html_content += str_description + data + description_at_bottom+'<div class="break"></div>'
                            html_content += ""
                            if report_item.get("report_cat") not in html_dict:
                                html_dict[report_item.get("report_cat")] = html_content
                            else:
                                html_dict[report_item.get("report_cat")] += html_content
                        else:
                            my_const.global_settings["paging"].minus_h3()

                    elif report_cat in enabled_stats and (report_cat == "sql_stats" ) and (report_item.get("active") in my_const.yes_no):
                        if report_item.get("design_template") is None:
                            if template.get("sql_stats_template") is not None:
                                report_item["design_template"] = template.get("sql_stats_template")
                            else:
                                return "",resp.MyResponse("E","Common.process_report_template,template not avail for 'sql_stats'")

                        if report_item.get("ssh_name_filter") is not None:
                            if ((any(elem in server.get("ssh_name") for elem in report_item.get("ssh_name_filter")) == True) or ((server.get("ssh_name_filter") != None) and (any(elem in server.get("ssh_name_filter") for elem in report_item.get("ssh_name_filter"))  == True))) == False:
                                continue
                        
                        header_no_text = my_const.global_settings["paging"].add_h4()
                        qdata,err_sql = tasks.execute_task(report_item,server,None)
                        if err_sql is not None:
                            return "",err_sql
                        if qdata !=None and qdata != "":
                            # header_no_text = my_const.global_settings["paging"].add_h4()
                            html_content = '<h4 id="'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</h4>'
                            index_title = '<h4 class="index_header" ><a href="#'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</a></h4>'
                            temp_index_sql_content += "<tr><td>"+index_title+"</td></tr>"+my_const.global_settings["index_subhead_content"]

                            # my_const.global_settings["index_subhead_content"] += '<tr><td>'+index_title+"</td></tr>"
                            str_description = ""
                            if report_item.get("description") != None and report_item.get("description") != "":
                                str_description = '<p class="description">'+report_item["description"]+'</p>'
                            html_content += str_description + qdata
                            if report_item.get("report_cat") not in html_dict:
                                html_content = '<div class="dbreak_auto">'+html_content+'</div>'
                                html_dict[report_item.get("report_cat")] = html_content
                            else:
                                html_content = '<div class="dbreak">'+html_content+'</div>'
                                html_dict[report_item.get("report_cat")] += html_content
                        else:
                            my_const.global_settings["paging"].minus_h4()
                            
                    elif report_cat in enabled_stats and (report_cat == "qan_analysis" ) and (report_item.get("active") in my_const.yes_no): 
                        if report_item.get("design_template") is None:
                            if template.get("qan_stats_template") is not None:
                                report_item["design_template"] = template.get("qan_stats_template")
                            else:
                                return "",resp.MyResponse("E","Common.process_report_template,template not avail for 'qan_analysis'")

                        if report_item.get("ssh_name_filter") is not None:
                            if ((any(elem in server.get("ssh_name") for elem in report_item.get("ssh_name_filter")) == True) or ((server.get("ssh_name_filter") != None) and (any(elem in server.get("ssh_name_filter") for elem in report_item.get("ssh_name_filter"))  == True))) == False:
                                continue

                        qdata,err_qan = tasks.execute_task(report_item,server,None)
                        if err_qan is not None:
                            return "",err_qan
                        if qdata !=None and qdata != "":
                            header_no_text = my_const.global_settings["paging"].add_h4()
                            html_content = '<h4 id="'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</h4>'
                            index_title = '<h4 class="index_header" ><a href="#'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</a></h4>'
                            
                            my_const.global_settings["index_subhead_content"] += '<tr><td>'+index_title+"</td></tr>"
                            str_description = ""
                            if report_item.get("description") != None and report_item.get("description") != "":
                                str_description = '<p class="description">'+report_item["description"]+'</p>'
                            html_content += str_description + qdata
                            if report_item.get("report_cat") not in html_dict:
                                html_content = '<div class="dbreak_auto">'+html_content+'</div>'
                                html_dict[report_item.get("report_cat")] = html_content
                            else:
                                html_content = '<div class="dbreak">'+html_content+'</div>'
                                html_dict[report_item.get("report_cat")] += html_content

                    elif report_cat in enabled_stats and report_cat == "graph_stats" and (report_item.get("active") in my_const.yes_no):
                        if report_item.get("design_template") is None:
                            if template.get("graph_stats_template") is not None:
                                report_item["design_template"] = template.get("graph_stats_template")
                            else:
                                return "",resp.MyResponse("E","Common.process_report_template,graph design [template] not avail")
                        
                        if report_item.get("ssh_name_filter") is not None:
                            if ((any(elem in server.get("ssh_name") for elem in report_item.get("ssh_name_filter")) == True) or ((server.get("ssh_name_filter") != None) and (any(elem in server.get("ssh_name_filter") for elem in report_item.get("ssh_name_filter"))  == True))) == False:
                                continue

                        my_const.global_settings["index_subhead_sub_content"] = ""
                        header_no_text = my_const.global_settings["paging"].add_h4()
                        gdata,err_graph = tasks.execute_task(report_item,server,None)
                        if err_graph is not None:
                            return "",err_graph
                        if gdata !=None and gdata != "":
                            put_break = ""
                            if len(html_dict.keys())>0:
                                put_break = 'class="dbreak"'
                            html_content = '<div '+put_break+'> <h4 id="'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</h4>'
                            graph_title= '<h4 class="index_header" ><a href="#'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+"</a></h4>"
                            design_template = report_item.get("design_template")
                            if design_template != None:
                                css_class = design_template.get("css_class")
                                if css_class != None and "unite" in css_class :
                                    html_content = '<div class="dbreak"> <h4 id="'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+'</h4>'
                                    graph_title= '<h4  class="index_header" ><a href="#'+header_no_text.strip()+'">'+header_no_text+str(report_item.get("report_name"))+"</a></h4>"

                            my_const.global_settings["index_subhead_content"] += "<tr><td>"+str(graph_title)+"</td></tr>"+my_const.global_settings["index_subhead_sub_content"]
                            str_description = ""
                            if report_item.get("description") != None and report_item.get("description") != "":
                                str_description = '<p class="description">'+report_item["description"]+'</p>'
                            html_content += str_description + gdata
                            html_content += "</div>"
                            if report_item.get("report_cat") not in html_dict:
                                html_dict[report_item.get("report_cat")] = html_content
                            else:
                                html_dict[report_item.get("report_cat")] += html_content
                        else:
                            my_const.global_settings["paging"].minus_h3()
                            my_const.global_settings["paging"].minus_h4()

                if report_cat == "sql_stats":
                    html_content = html_dict.get(report_item.get("report_cat"))
                    if html_content != None and html_content != "":
                        html_content = '<h3 id="'+header_no_sql.strip()+'">'+header_no_sql+'Database Statistics</h3>' + description_at_top + html_content + description_at_bottom +'<div class="break"></div>'
                        html_dict[report_item.get("report_cat")] =  html_content
                        temp_index_content += '<tr><td><h3 class="index_header" ><a href="#'+header_no_sql.strip()+'">'+header_no_sql+"Database Statistics</a></h3></td></tr>"+ temp_index_sql_content #+my_const.global_settings["index_subhead_content"]
                    else:
                        my_const.global_settings["paging"].minus_h3()
                
                if report_cat == "qan_analysis":
                    html_content = html_dict.get(report_item.get("report_cat"))
                    if html_content != None and html_content != "":
                        html_content = '<h3 id="'+header_no_qan.strip()+'">'+header_no_qan+'Query Analytics</h3>' + description_at_top + html_content + description_at_bottom + '<div class="break"></div>'
                        html_dict[report_item.get("report_cat")] =  html_content
                        temp_index_content += '<tr><td><h3 class="index_header" ><a href="#'+header_no_qan.strip()+'">'+header_no_qan+"Query Analytics</a></h3></td></tr>"+my_const.global_settings["index_subhead_content"]
                    else:
                        my_const.global_settings["paging"].minus_h3()
                
                if report_cat == "graph_stats":
                    html_content = html_dict.get(report_item.get("report_cat"))
                    if html_content != None and html_content != "":
                        html_content = '<h3 id="'+header_graph_text.strip()+'">'+header_graph_text+'PMM Graphs</h3>' + description_at_top + html_content + description_at_bottom +'<div class="break"></div>'
                        html_dict[report_item.get("report_cat")] =  html_content
                        temp_index_content += '<tr><td><h3 class="index_header" ><a href="#'+header_graph_text.strip()+'">'+header_graph_text+"PMM Graphs</a></h3></td></tr>"+my_const.global_settings["index_subhead_content"]
                    else:
                        my_const.global_settings["paging"].minus_h3()
                
                if  report_cat == "ticket_stats" or report_cat == "critical_stats":
                    temp_ticket_handled += '<div class="break"></div>'
                    
            except Exception as error_process_report:
                return "",resp.MyResponse("E","exception when try to process report template",error_process_report)

        final_html = ""
        for html_item in html_dict:
            html_content = html_dict[html_item]
            final_html += html_content
            
        if final_html !="":
            
            final_html = '<div ><h2 id="'+header_no_text_node.strip().replace(".","_")+'" class="title_node">'+header_no_text_node+'NODE - <span>'+str(server.get("ssh_name"))+'</span></h2>' + final_html +'</div><div ></div>'
            my_const.global_settings["index_content"] += '<tr><td><h2 class="index_header"><a href="#'+header_no_text_node.strip().replace(".","_")+'">'+header_no_text_node+'NODE - <span>'+str(server.get("ssh_name"))+'</span></a></h2></td></tr>' + temp_index_content
        else:
            my_const.global_settings["paging"].minus_h2()
        
        if my_const.global_settings.get("index_ticket_content")  == "" and temp_index_ticket_content != "":
            my_const.global_settings["index_ticket_content"] = temp_index_ticket_content

        if my_const.global_settings.get("ticket_handled")  == "" and temp_ticket_handled != "":
            my_const.global_settings["ticket_handled"] = temp_ticket_handled
        
        return final_html,None
    except Exception as error_common_report:
        return "",resp.MyResponse("E","process_report_template final",error_common_report)


def get__server_stats_dict(idx,ip_port):
    node_dict = {'report_name': 'Node : '+ip_port, 
    'command': "", 
    'active': True, 
    'description': None, 
    'ssh_name_filter': None, 
    'get_from_store': 'mongo_stats', 
    'extract_info': 
    {
        'system_stats': {'report_name': 'System Statistics', 'active': True, 'description': None, 'ssh_name_filter': None, 'design_template': {'data_type': 'vertical_table', 'css_class': 'table header_green_text ', 'delimiter': '|', 'output': 'json', 'content_start': ['myDbHealthMetrics', 'systemStats',idx]}}, 
        'system_recommendations': {'report_name': 'System Recommendations', 'active': True, 'description': None, 'ssh_name_filter': None, 'design_template': {'data_type': 'vertical_table', 'css_class': 'table header_green_text', 'delimiter': '|', 'output': 'sysRecommendations', 'content_start': ['myDbHealthMetrics', 'Recommendations',idx,"sysRecommendations"]}},

        'instance_stats': {'report_name': 'Instance Statistics', 'active': True, 'description': None, 'ssh_name_filter': None, 'design_template': {'data_type': 'vertical_table', 'css_class': 'table header_green_text ', 'delimiter': '|', 'output': 'json', 'content_start': ['myDbHealthMetrics', 'instanceLevelStats',idx]}}, 
        'instance_recommendations': {'report_name': 'Instance Recommendations', 'active': True, 'description': None, 'ssh_name_filter': None, 'design_template': {'data_type': 'vertical_table', 'css_class': 'table header_green_text', 'delimiter': '|', 'output': 'instanceLevelRecommendations', 'content_start': ['myDbHealthMetrics', 'Recommendations',idx,"instanceLevelRecommendations"]}}
    }
    }
    return node_dict

def get__mongo_stats_dict(idx,ip_port):
    node_dict = {'report_name': 'Mongo Informations : '+ip_port, 
    'command': "", 
    'active': True, 
    'description': None, 
    'ssh_name_filter': None, 
    'get_from_store': 'mongo_stats', 
    'extract_info': 
    {
        'system_stats': {'report_name': 'Mongo Metrics', 'active': True, 'description': None, 'ssh_name_filter': None, 'design_template': {'data_type': 'vertical_table', 'css_class': 'table header_green_text ', 'delimiter': '|', 'output': 'json', 'content_start': ['myDbHealthMetrics', 'mongoMetrics',idx]}}, 
        'system_recommendations': {'report_name': 'Mongo Recommendations', 'active': True, 'description': None, 'ssh_name_filter': None, 'design_template': {'data_type': 'vertical_table', 'css_class': 'table header_green_text', 'delimiter': '|', 'output': 'mongoRecommendations', 'content_start': ['myDbHealthMetrics', 'Recommendations',idx,"mongoRecommendations"]}},
    }
    }
    return node_dict

def modify_report_list(mongo_clusters,report_list):
    temp_list = []
    mongo_list = []
    nodes = mongo_clusters.split(",")
    if nodes is not None and len(nodes) > 0:
        for idx,node in enumerate(nodes):
            ip_port_list = node.split("|")
            if ip_port_list is not None and len(ip_port_list)>2:
                # ip_port = ip_port_list[1]+":"+ip_port_list[2]
                ip_port = ip_port_list[0]
                node_dict  = get__server_stats_dict(idx,ip_port,)
                temp_list.append(node_dict)
                mongo_metric_dict  = get__mongo_stats_dict(idx,ip_port,)
                mongo_list.append(mongo_metric_dict)

    if len(mongo_list)>0:
        temp_list.extend(mongo_list)
    for idx,temp_item in enumerate(temp_list):
        report_list.insert(idx+1,temp_item)
    return report_list