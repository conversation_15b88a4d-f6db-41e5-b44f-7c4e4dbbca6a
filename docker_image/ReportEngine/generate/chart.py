from numpy import double
from util import response as resp
import util.Log as log
from constants import constant as my_const
import base64
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff

def create_chart(df_result,chart_settings):
    try:
        df = pd.DataFrame(df_result)
        chart_html =""
        for chart_item in chart_settings:
            chart_type = chart_item.get("chart_type")
            style_dict = chart_item.get("style")
            tmp_chart_colors = chart_item.get("chart_colors")
            sorting = chart_item.get("sorting")
            style_str = ""
            if style_dict is not None:
                for style_item in style_dict:
                    style_item_value = style_dict[style_item]
                    style_item = style_item.replace("_","-")
                    style_str += style_item+':'+style_item_value+';'
                style_str = 'style="margin: auto;vertical-align: middle;'+style_str+'"'
            tmp_pie_color = px.colors.qualitative.Pastel
            #color=["#62D266",tmp_pie_color[0],tmp_pie_color[3]]
            #color.extend(tmp_pie_color[5:])
            tmp_pie_color[0]= "#62D266"
            if tmp_chart_colors is not None and len(tmp_chart_colors)>0:
                tmp_pie_color = tmp_chart_colors
            if sorting is not None and sorting.get("key") is not None:
                if sorting.get("order_by") is not None and sorting.get("order_by") == "asc":
                    df = df.sort_values(by=sorting.get("key"),ascending=True)
                elif sorting.get("order_by") is not None and sorting.get("order_by") == "desc":
                    df = df.sort_values(by=sorting.get("key"),ascending=False)
                else:
                    df = df.sort_values(by=sorting.get("key"))
            if chart_item.get("group_by"):
                if chart_item.get("value") :
                    if chart_item.get("group_by_type") == "datetime":
                        df[chart_item.get("group_by")] = pd.to_datetime(df[chart_item.get("group_by")]).dt.date
                    group_by = df.groupby(chart_item.get("group_by"))[chart_item.get("value")].count().reset_index(name=chart_item.get("value"))
                else:
                    group_by = df.groupby(chart_item.get("group_by")).count()
                
                if type(chart_item.get("value")) == list and len(chart_item.get("value"))>1:
                    fig = make_subplots(specs=[[{"secondary_y": True}]])
                else:
                    fig = go.Figure()

                if chart_type == "Pie":
                    group_by.sort_values(by=[chart_item.get("group_by")], ascending=True)
                    fig = px.pie(group_by,values=chart_item.get("value"),names=chart_item.get("group_by"),title = chart_item.get('title'),color_discrete_sequence = tmp_pie_color)
                    fig.update_traces(textinfo=chart_item.get("info"))
                    fig.update_layout(legend=dict(
                        yanchor="top",
                        y=0.60,
                        xanchor="right",
                        x=1.69
                    ))
                elif chart_type == "Donut":
                    hole = chart_item.get("hole") if chart_item.get("hole") is not None else 0.2
                    fig = px.pie(group_by,values=chart_item.get("value"),names=chart_item.get("group_by"),title = chart_item.get('title'),hole=hole,color_discrete_sequence = tmp_pie_color)
                    fig.update_traces(textinfo=chart_item.get("info"))
                elif chart_type == "Bar":
                    #color=chart_item.get("group_by") ,
                    fig = px.bar(group_by,y=chart_item.get("value"),x=chart_item.get("group_by"),title = chart_item.get('title'),text=chart_item.get("value"),color_discrete_sequence=tmp_pie_color,template="simple_white")
                    fig.update_yaxes(tickfont_size=8)
                    fig.update_xaxes(color="#727472",linecolor="#62D266",ticklen=0,tickangle=0,rangeselector_font_size=8,tickfont_size=8,ticklabelposition="outside")
                    fig.update_yaxes(color="#727472",linecolor="#62D266",ticklen=0)
                    fig.update_layout(uniformtext_minsize=8, uniformtext_mode='hide')
                    fig.update_traces(textposition='inside',textfont_size=8,textfont_color="#036507")

                elif chart_type == "Line":
                    fig = px.line(group_by,y=chart_item.get("value"),x=chart_item.get("group_by"),title = chart_item.get('title'),text=chart_item.get("value"),color_discrete_sequence=["#62D266"],template="simple_white")
                elif chart_type == "Scatter":
                    fig = px.scatter(group_by,y=chart_item.get("value"),x=chart_item.get("group_by"),title = chart_item.get('title'),text=chart_item.get("value"))
                    #fig.update_traces(textposition='inside')
            else:
                if chart_item.get("group_by_type") == "datetime":
                    df[chart_item.get("x_axis")] = pd.to_datetime(df[chart_item.get("x_axis")]).dt.date

                if chart_type == "Pie":
                    fig =px.pie(df,values=chart_item.get('value'),names= chart_item.get('name'),title = chart_item.get('title'),color_discrete_sequence = tmp_pie_color)
                    fig.update_traces(textinfo=chart_item.get("info"))
                    fig.update_layout(autosize=True , legend=dict(
                        yanchor="top",
                        y=0.60,
                        xanchor="right",
                        x=1.69
                    ),
                    )
                elif chart_type == "Donut":
                    hole = chart_item.get("hole") if chart_item.get("hole") is not None else 0.2
                    fig =px.pie(df,values=chart_item.get('y_axis'),names= chart_item.get('x_axis'),title = chart_item.get('title'),hole=hole,color_discrete_sequence = tmp_pie_color)
                    fig.update_traces(textinfo=chart_item.get("info"))
                elif chart_type == "Bar":
                    #df[chart_item.get("x_axis")][0]= "information_schema.RDS_EVENTS_THREADS_WAITS_CURRENT "
                    color_bar = None
                    if chart_item.get("color_bar") ==True:
                        color_bar=chart_item.get("x_axis")
                    fig = px.bar(df,y=chart_item.get("y_axis"),x=chart_item.get("x_axis"),title = chart_item.get('title'),color_discrete_sequence=tmp_pie_color,template="simple_white",color=color_bar,text=chart_item.get("y_axis"))
                    fig.update_yaxes(tickfont_size=12)
                    fig.update_xaxes(color="rgb(42, 42, 42)",linecolor="#62D266",ticklen=0,tickangle=0,rangeselector_font_size=8,tickfont_size=9,ticklabelposition="outside")
                    fig.update_yaxes(color="rgb(42, 42, 42)",linecolor="#62D266",ticklen=0)
                    fig.update_layout(uniformtext_minsize=8, uniformtext_mode='hide')
                    fig.update_traces(textfont_size=12,textfont_color="#036507")
                    fig.update_traces(textposition='outside')
                    

                elif chart_type == "Line":
                    fig = px.line(df,labels=chart_item.get("y_axis"),y=chart_item.get("y_axis"),x=chart_item.get("x_axis"),title = chart_item.get('title'),text=chart_item.get("y_axis"),color_discrete_sequence=["#62D266"],template="simple_white")
                    fig.update_xaxes(tickmode = 'array')
                    fig.update_traces(textposition='top right')

                elif chart_type == "Scatter":
                    fig = px.scatter(df,labels=chart_item.get("y_axis"),y=chart_item.get("y_axis"),x=chart_item.get("x_axis"),title = chart_item.get('title'),text=chart_item.get("y_axis"))
                    fig.update_xaxes(tickmode = 'array')
                elif chart_type == "Custom":
                    df["Table"][0] = "information_schema.RDS_EVENTS_THREADS_WAITS_CURRENT"
                    final_df = df.sort_values(by=['Table'], ascending=True)
                    
                    trace1 = go.Pie(
                        values=final_df[chart_item.get("y1_axis")],
                        labels=final_df[chart_item.get("x_axis")],
                        domain=dict(x=[0, 0.5]),
                        title="Current Table Size",
                        hoverinfo="label+percent+name",
                    )
                    trace2 = go.Pie(
                        values=final_df[chart_item.get("y2_axis")],
                        labels=final_df[chart_item.get("x_axis")],
                        domain=dict(x=[0.5, 1.0]),
                        title="Fragmentation Size",
                        hoverinfo="label+percent+name",
                    )
                    layout = go.Layout(title="Global Emissions 1990-2011",)
                    data = [trace1, trace2]
                    fig = go.Figure(data=data, layout=layout)
                    
                    fig.update_traces(textinfo=chart_item.get("info"))
                    fig.update_layout(legend=dict(
                        yanchor="top",
                        y=0.60,
                        xanchor="left",
                        x=1.01
                    ))

                    # fig.update_traces(showlegend=True)
                    # import plotly.offline as py
                    # py.plot(fig, filename='simple-pie-subplot')
                    #fig.write_image("fig1.png")
                elif chart_type == "StackedBar":
                    stack_group = pd.pivot_table(df,sort=True, values=chart_item.get("stacked"), index=chart_item.get("x_axis"), columns=chart_item.get("y_axis"),aggfunc='count',fill_value=0).reset_index()
                    dfc = df.copy()
                    y_axis_column_tmp = dfc.groupby(chart_item.get("y_axis"))[chart_item.get("x_axis")].count().reset_index(name="count").sort_values(by="count", ascending=False)
                    y_axis_column = list(y_axis_column_tmp[chart_item.get("y_axis")])
                    
                    fig = px.bar(stack_group,text_auto=True,y=y_axis_column,x=chart_item.get("x_axis"),title = chart_item.get('title'),color_discrete_sequence=tmp_pie_color,template="simple_white")                    
                    fig.update_layout(
                    xaxis_title = chart_item.get("y_axis"),
                    yaxis_title = chart_item.get("stacked"),
                    legend_title = chart_item.get("y_axis"),
                    font=dict(size=12,color="rgb(42, 42, 42)")
                    )    
                    fig.update_yaxes(tickfont_size=10)
                    fig.update_xaxes(color="rgb(42, 42, 42)",linecolor="#62D266",ticklen=0,tickangle=0,rangeselector_font_size=10,tickfont_size=10,ticklabelposition="outside")
                    fig.update_yaxes(color="rgb(42, 42, 42)",linecolor="#62D266",ticklen=0)
                    fig.update_layout(uniformtext_minsize=10, uniformtext_mode='hide')
                    fig.update_traces(textposition='inside',textfont_size=10,textfont_color="#036507")
                elif chart_type == "Histogram":
                    
                    col = list(df.columns)[1:]
                    hosts = df_result.get("host")
                    go_plt_list = []
                    for id,host in enumerate(hosts):
                        yval = []
                        for col_item in col:
                            tmp_val = df_result.get(col_item)
                            if tmp_val is not None:
                                tmp_val = tmp_val[id]
                                yval.append(tmp_val)
                        plot_item = go.Bar(name=host, x=col, y=yval,text=yval,textposition='outside')
                        go_plt_list.append(plot_item)

                    fig = go.Figure(data=go_plt_list)
                    fig.update_layout(barmode='group', title=dict(text=chart_item.get('title'), font=dict(size=25), yref='paper'))
                    # fig.update_traces(texttemplate='%{text:.2s}', textposition='outside')
                    # fig.show()

            #custom settings
            if chart_item.get("legend_title") != None:
                fig.update_layout(legend_title=chart_item.get("legend_title"))

            if chart_item.get("x_title") != None:
                fig.update_layout(xaxis_title=chart_item.get("x_title"))

            if chart_item.get("y_title") != None:
                fig.update_layout(yaxis_title=chart_item.get("y_title"))

            if chart_item.get("x_axis_ticks") != None:
                if chart_item.get("x_axis_ticks") == "D1":
                    fig.update_xaxes(dtick="D1",tickformat="%d\n\n%b<br>%y")
                elif chart_item.get("x_axis_ticks") == "category":
                    fig.update_xaxes(type='category')

            if chart_item.get("x_axis_ticks_angle") != None:
                fig.update_xaxes(tickangle=chart_item.get("x_axis_ticks_angle"))
            
            if chart_item.get("y_axis_ticks_angle") != None:
                fig.update_yaxes(tickangle=chart_item.get("y_axis_ticks_angle"))

            if chart_item.get("x_showticklabels") != None:
                fig.update_xaxes(showticklabels=chart_item.get("x_showticklabels"))

            if chart_item.get("legend_orientation") != None:
                fig.update_layout(legend=dict( orientation=chart_item.get("legend_orientation")))
                       
            if chart_item.get("legend") == True:
                fig.update_traces(showlegend=True)
            else:
                fig.update_traces(showlegend=False)
            

            fig.update_layout(plot_bgcolor='rgba(0,0,0,0)')
            report_store_path = my_const.global_settings.get("report_store_path")
            final_path = report_store_path+"/graphs/"+chart_item.get("chart_type")+".svg"
            if chart_item.get("img_width") != None and chart_item.get("img_height") != None:
                img_bytes = fig.to_image(format="png",scale=1.5,width=chart_item.get("img_width"), height=chart_item.get("img_height"))
            else:
                img_bytes = fig.to_image(format="png",scale=1.5)

            encoding = base64.b64encode(img_bytes).decode()
            
            chart_html += '<div '+style_str+'><img  src="data:image/png;base64,'+encoding+'" /> </div>'
            #chart_html += '<div '+style_str+'><img src="'+final_path.replace(report_store_path+"/","")+'" /> </div>'
        chart_html = '<div class="unite">'+chart_html+'</div><div style="clear:both"></div>'
    except Exception as err:
        chart_html = ""
        return chart_html,resp.MyResponse("E","Common.create_chart: ",err)
    return chart_html,None
