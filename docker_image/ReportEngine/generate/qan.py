import requests
import json
from util import Common as common
from util import response as resp
import clickhouse_driver
from datetime import datetime,timedelta
from typing import Iterable
from operator import itemgetter

def sort_tuples(tuples):
    return sorted(tuples, key=itemgetter(5),reverse=True)

def date_groups( 
    start_at: datetime, 
    end_at: datetime,
    max_capacity_minutes: float) -> Iterable[datetime]:
    
    capacity = timedelta(minutes=max_capacity_minutes)
    interval = int( (end_at  - start_at ) / capacity) + 1
    for i in range(interval):
        yield (start_at + capacity * i)
    yield end_at

qan_conn = None

def init_connection(server):
    global qan_conn
    conn = clickhouse_driver.connect(
        host = server.get("ch_host"),
        port = server.get("ch_port"),
        user = server.get("ch_username"),
        password = server.get("ch_password"),
        database = server.get("ch_db")
    )
    qan_conn = conn
    return conn

def parse_qan_result(result,delimiter='||||'):
    op = [] 
    try:
        for line in result:
            op_str = ""
            for column in line:
                op_str += str(column) + delimiter
            op_str = op_str[:-len(delimiter)]
            op.append(op_str)
    except Exception as e:
        raise e
    return op

def get_qan_details_old(report_info,server):
    html = []
    global qan_conn
    try:
        query_file = report_info.get("query_file")
        query_data_base = report_info.get("data_base")
        if query_data_base == None:
            query_data_base = ""
        if query_file != None and query_file!="":
            design_template = report_info.get("design_template")
            all_query = common.read_file("sql_query/qan/"+query_file)
            if all_query != None and all_query != "":
                all_query = common.replace_command_tokens(all_query,server)

                if qan_conn is None or (qan_conn is not None and qan_conn.is_closed is True):
                    conn = init_connection(server)
                    cursor = conn.cursor()
                else:
                    conn = qan_conn 
                    cursor = conn.cursor()

                cursor.execute(all_query)
                data = cursor.fetchall()
                column_names = cursor._columns
                data.insert(0,column_names)
                html = parse_qan_result(data,design_template.get("delimiter"))

            else:
                raise Exception("qan query files is not valid"+query_file)
    except Exception as e:
        raise e
    return html


def get_qan_details(report_info,server):
    html = []
    global qan_conn
    try:
        query_file = report_info.get("query_file")
        limit = report_info.get("limit")
        period = report_info.get("period")
        if period is None:
            period = 1440
        if limit is None:
            limit = 5
        if query_file != None and query_file!="":
            design_template = report_info.get("design_template")
            all_query = common.read_file("sql_query/qan/"+query_file)
            column_names = []
            if all_query != None and all_query != "":
                server_tmp = server.copy() #'%m/%d/%y %H:%M:%S'
                from_time = datetime.strptime(server.get("from_time"), '%Y-%m-%d %H:%M:%S')
                to_time = datetime.strptime(server.get("to_time"), '%Y-%m-%d %H:%M:%S')
                dg = date_groups((from_time), (to_time), period)
                dates = list(dg)
                data = []
                for start_at, end_at in zip(dates[:-1],dates[1:]):
                    server_tmp["from_time"] = start_at.strftime("%Y-%m-%d %H:%M:%S")
                    server_tmp["to_time"]   = end_at.strftime("%Y-%m-%d %H:%M:%S")
                    server_tmp["qan_limit"] = str(limit)
                    if server_tmp["from_time"] != server_tmp["to_time"]:
                        tmp_query = common.replace_command_tokens(all_query,server_tmp)
                        # print("query : ",server_tmp["from_time"],server_tmp["to_time"],tmp_query)
                        if qan_conn is None or (qan_conn is not None and qan_conn.is_closed is True):
                            conn = init_connection(server)
                            cursor = conn.cursor()
                        else:
                            conn = qan_conn 
                            cursor = conn.cursor()

                        cursor.execute(tmp_query)
                        tmp_data = cursor.fetchall()
                        if len(tmp_data)>0:
                            tmp_data = sort_tuples(tmp_data)
                            tmp_data = tmp_data[:limit]
                            data.extend(tmp_data)
                            tmp_data = []
                        if column_names not in cursor._columns:
                            column_names = cursor._columns
                        cursor.close()
                
                data = sort_tuples(data)
                data = data[:limit]
                data.insert(0,column_names)
                html = parse_qan_result(data,design_template.get("delimiter"))
                data = []
            else:
                raise Exception("qan query files is not valid"+query_file)
    except Exception as e:
        raise e
    return html