from constants import constant as my_const
from util import Common as common
from . import chart
import util.Log as log
from json2html import *
from util import response as resp

def server_stats_to_html(extract_info_type,result_list,report_info):
    html_str = ""
    coulmn_names = []
    ds_result = {}
    df_result = {}
    chart_str = ""
    is_table_empty = True
    try:
        design_template = report_info.get("design_template")
        chart_settings = report_info.get("chart_settings")
        data_type = design_template.get("data_type")
        delimiter = design_template.get("delimiter")
        href_info = design_template.get("href")
        data_separator = design_template.get("data_separator")
        lst_script = report_info.get("script")
        href = ""
        if href_info is not None and href_info.get("column") is not None and href_info.get("url") is not None:
            href = href_info.get("column")
        if extract_info_type == "network_connections":
            remote_ip = []
            local_ip = []
            remote_ip_processed = False
            local_ip_processed = False
            for idx,row in enumerate(result_list):
                if row == "":
                    continue
                if row.strip() == "Connections to local IP addresses":
                    remote_ip_processed = False
                    local_ip_processed = True
                elif row.strip() == "Connections from remote IP addresses":
                    remote_ip_processed = True
                    local_ip_processed = False
                elif "Connections" in row.strip():
                    local_ip_processed = False
                    remote_ip_processed = False
                elif local_ip_processed == True:
                    ip_count = row.strip().split(" ")
                    if len(ip_count)>1:
                        local_ip.append({"ip":ip_count[0],"count":ip_count[-1]})
                        remote_ip = list(filter(lambda i: i['ip'] != ip_count[0], remote_ip))
                elif remote_ip_processed == True:
                    remote_ip_processed = True
                    ip_count = row.strip().split(" ")
                    if len(ip_count)>1:
                        remote_ip.append({"ip":ip_count[0],"count":ip_count[-1]})
                else:
                    remote_ip_processed = False
                    local_ip_processed = False
            html_remote = ""
            if len(remote_ip)>0:
                remote_ip = sorted(remote_ip, key=lambda d: int(d['count']), reverse=True)
                is_table_empty = False
                html_remote += "<tr><th>IP</th><th>Count</th></tr>"
                for ip_item in remote_ip:
                    html_remote += "<tr>"
                    html_remote += "<td>"+ip_item.get("ip")+"</td><td>"+str(ip_item.get("count"))+"</td>"
                    html_remote += "</tr>"
                html_remote = '<div style="clear: both;"><h5>Connection from Remote host </h5><div class="'+design_template.get("css_class")+'"> <table >'+html_remote+'</table></div></div>'    
            html_local = ""
            if len(local_ip)>0:
                local_ip = sorted(local_ip, key=lambda d: int(d['count']), reverse=True)
                is_table_empty = False
                html_local += "<tr><th>IP</th><th>Count</th></tr>"
                for ip_item in local_ip:
                    html_local += "<tr>"
                    html_local += "<td>"+ip_item.get("ip")+"</td><td>"+str(ip_item.get("count"))+"</td>"
                    html_local += "</tr>"
                html_local = '<div style="clear: both;"><h5>Connection to Local host </h5>  <div class="'+design_template.get("css_class")+'"><table >'+html_local+'</table></div></div>'    
            html_str = html_remote + html_local
        elif "rds" in extract_info_type:
            is_table_empty = False
            html_str = " ".join(result_list)
        else:
            href_index = -1
            column_names_htov = []
            remove_column_list = {}
            removed_column_index_list = {}
            for idx,row in enumerate(result_list):
                if row == "":
                    continue
                if data_type == "vertical_table":
                    information = row.split(delimiter)
                    html_str += "<tr>"
                    last_header = ""
                    for col_idx,information_item in enumerate(information):
                        if information_item.strip() == "":
                            continue
                        information_item = information_item.strip()
                        information_item = information_item if not information_item.isnumeric() else float(information_item) if "." in information_item else int(information_item)
                        if information_item == href:
                            href_index = col_idx
                        if col_idx == 0:
                            last_header = information_item
                            resp_header,resp_value,_ = common.chech_for_eval(lst_script,information_item,None,None)
                            if resp_header != None and resp_header != "":
                                information_item = resp_header
                            html_str += "<th>"+str(information_item)+"</th>"
                            if information_item not in df_result:
                                ds_result[information_item] = []
                                df_result[information_item] = []
                                coulmn_names.append(information_item)
                        else:
                            is_table_empty = False
                            tmp_value = information_item
                            resp_header,resp_value,_ = common.chech_for_eval(lst_script,None,information_item,last_header)
                            last_header = ""
                            if resp_value != None and resp_value != "":
                                information_item = resp_value
                            if href_index == col_idx and href_info != None:
                                information_item = '<a href="'+href_info.get("url")+str(information_item)+'">'+str(information_item)+'</a>'
                            html_str += "<td>"+str(information_item)+"</td>"
                            column_name = coulmn_names[-1]
                            ds_result[column_name].append(tmp_value)
                            df_result[column_name].append(information_item)
                    html_str += "</tr>"
                elif data_type in ["table","horizontal_table"]:
                    information = row.split(delimiter)
                    if idx == 0:
                        html_str += "<tr>"
                        for coulmn_idx,information_item in enumerate(information):
                            information_item = information_item.strip()
                            if information_item == "" and coulmn_idx not in remove_column_list:
                                remove_column_list[coulmn_idx] = coulmn_idx
                                continue

                            information_item = information_item if not information_item.isnumeric() else float(information_item) if "." in information_item else int(information_item)
                            if information_item == href:
                                href_index = coulmn_idx
                            resp_header,_,removed_column_index = common.chech_for_eval(lst_script,information_item,None,None)
                            
                            if resp_header != None and resp_header != "":
                                information_item = resp_header
                            html_str += "<th>"+str(information_item)+"</th>"
                            if information_item not in df_result:
                                if removed_column_index != None and removed_column_index != "":
                                    removed_column_index_list[coulmn_idx] = removed_column_index
                                
                                ds_result[information_item] = []
                                df_result[information_item] = []
                                coulmn_names.append(information_item)
                        html_str += "</tr>"
                    else:
                        is_table_empty = False
                        html_str += "<tr>"
                        chart_skip = False
                        actual_idx = -1
                        for coulmn_idx,information_item in enumerate(information):
                            
                            actual_idx += 1
                            is_remove_column = remove_column_list.get(coulmn_idx)
                            if (is_remove_column != None and is_remove_column == coulmn_idx):
                                actual_idx = actual_idx -1
                                continue

                            information_item = information_item.strip()
                            information_item = information_item if not information_item.isnumeric() else float(information_item) if "." in information_item else int(information_item)
                            if href_index == coulmn_idx and href_info != None:
                                information_item = '<a href="'+href_info.get("url")+str(information_item)+'">'+str(information_item)+'</a>'
                            column_name = coulmn_names[actual_idx]
                            tmp_value = information_item
                            resp_header,resp_value,_ = common.chech_for_eval(lst_script,None,information_item,column_name)
                            if resp_value != None and resp_value != "":
                                information_item = resp_value
                            html_str += "<td>"+str(information_item)+"</td>"
                            df_result[column_name].append(information_item)
                            is_removed_column_index = removed_column_index_list.get(coulmn_idx)
                            if (is_removed_column_index != None and is_removed_column_index == information_item) or chart_skip == True:
                                chart_skip = True
                            else:
                                ds_result[column_name].append(tmp_value)
                        html_str += "</tr>"
                elif data_type == "htov_table":
                    information = row.split(delimiter)
                    temp_row = ""
                    for col_idx,information_item in enumerate(information):
                        information_item = information_item.strip()
                        information_item = information_item if not information_item.isnumeric() else float(information_item) if "." in information_item else int(information_item)
                        if information_item == href:
                            href_index = col_idx
                        if idx == 0:
                            # html_str += "<th>"+str(information_item)+"</th>"
                            if information_item not in df_result:
                                resp_header,_,_ = common.chech_for_eval(lst_script,information_item,None,None)
                                if resp_header != None and resp_header != "":
                                    information_item = resp_header
                                df_result[information_item] = []
                                ds_result[information_item] = []
                                column_names_htov.append("<th>"+str(information_item)+"</th>")
                                coulmn_names.append(str(information_item))
                        else:
                            if len(information) == len(coulmn_names):
                                is_table_empty = False
                                if href_index == col_idx and href_info != None:
                                    information_item = '<a href="'+href_info.get("url")+str(information_item)+'">'+str(information_item)+'</a>'
                                # html_str += "<td>"+str(information_item)+"</td>"
                                column_name = coulmn_names[col_idx]
                                ds_result[column_name].append(information_item)
                                df_result[column_name].append(information_item)
                                quey_class = ""
                                if "query" in column_names_htov[col_idx].lower():
                                    quey_class = ' class="td_query" '
                                resp_header,resp_value,_ = common.chech_for_eval(lst_script,None,information_item,column_name)
                                if resp_value != None and resp_value != "":
                                    information_item = resp_value
                                temp_row += "<tr>"+column_names_htov[col_idx]+"<td "+ quey_class +">"+str(information_item)+"</td>"+"</tr>"
                    allow_empty_row = design_template.get("allow_empty_row")
                    limit = design_template.get("limit")
                    if allow_empty_row is not None and allow_empty_row is True:
                        is_table_empty = False
                        if limit is None:
                            limit = 30
                        for k,col in enumerate(column_names_htov):
                            if k < limit:
                                temp_row += "<tr>"+col+"</tr>"
                    if temp_row != "":
                        temp_data_separator_str = ""
                        if data_separator != None and data_separator != "":
                            temp_data_separator_str = '<tr class="data_separator"><td colspan="2">'+data_separator+ str(idx)+'</td></tr>'
                        html_str += temp_data_separator_str+temp_row + "<tr class='htov_tr'><td  colspan='2' ><div class='htov_td_div'></div></td></tr>"
                elif data_type in "text":
                    html_str += row.strip()+"<br>"
                else:
                    html_str += row.strip()
            if chart_settings:
                if len(df_result) > 0:
                    is_table_empty = False
                    #pd.options.plotting.backend = "plotly"
                    chart_str,err = chart.create_chart(ds_result,chart_settings)
                    if err is not None:
                        # raise err
                        return html_str,ds_result,err
            if html_str != "":
                str_description = ""
                if report_info.get("description") != None and report_info.get("description") != "":
                    str_description = '<p class="description">'+report_info["description"]+'</p>'
                if "table" in data_type:
                    if report_info.get("show_table") is not None and report_info.get("show_table") == False and chart_str !="": 
                        html_str = '<div class="'+design_template.get("css_class")+'">'+str_description+chart_str+'</div>'
                    elif chart_str!="" or html_str !="":
                        chart_after_tables = report_info.get("chart_after_tables")
                        if chart_after_tables is not None and chart_after_tables == True:
                            html_str = '<div class="'+design_template.get("css_class")+'">'+str_description+'<table >'+html_str+'</table>'+chart_str+'</div>'
                        else:
                            bottom_description = report_info.get("bottom_description")
                            if bottom_description is not None and bottom_description == True :
                                html_str = '<div class="'+design_template.get("css_class")+'">'+chart_str+'<table >'+html_str+'</table>'+str_description+'</div>'
                            else:
                                html_str = '<div class="'+design_template.get("css_class")+'">'+str_description+chart_str+'<table >'+html_str+'</table></div>'
                elif data_type == "text" and (chart_str!="" or html_str !=""):
                    chart_after_tables = report_info.get("chart_after_tables")
                    if chart_after_tables is not None and chart_after_tables == True:
                        html_str = '<div class="'+design_template.get("css_class")+'">'+str_description+'<p>'+html_str+'</p>'+chart_str+'</div>'
                    else:
                        html_str = '<div class="'+design_template.get("css_class")+'">'+str_description+chart_str+'<p>'+html_str+'</p></div>'
                elif (chart_str!="" or html_str !=""):
                    chart_after_tables = report_info.get("chart_after_tables")
                    if chart_after_tables is not None and chart_after_tables == True:
                        html_str = '<div class="'+design_template.get("css_class")+'">'+str_description+''+html_str+chart_str+'</div>'
                    else:
                        html_str = '<div class="'+design_template.get("css_class")+'">'+str_description+chart_str+''+html_str+'</div>'
    except Exception as error:
        # log.print_log("E","Common.server_stats_to_html: ",error)
        # raise error
        return html_str,ds_result,resp.MyResponse("E","exception when stats to html conversion",error)
    if is_table_empty:
        return "",ds_result,None
    return html_str,ds_result,None

def loop_iterate(result_list,cs):
    temp_result = None
    for csitem in cs:
        if type(csitem) != list :
            if temp_result == None:
                if type(result_list) != list :
                    temp_result = result_list.get(csitem)
                elif type(temp_result) == list :
                    temp_result = result_list[csitem]
            else:
                if type(temp_result) != list :
                    temp_result = temp_result.get(csitem)
                elif type(temp_result) == list :
                    temp_result = temp_result[csitem]
                else:
                    pass
        else:
            temp_result = loop_iterate(temp_result,csitem)
    return temp_result

def analyse_server_stats_response(result_list,extract_info=None,design_template=None,server = None,prefix_title=""):
    html_str = ""
    try:
        error_string = ""
        output_format = "text"
        if design_template != None:
            output_format = design_template.get("output")
        for extract_item in extract_info:
            item_data = extract_info.get(extract_item)
            if item_data.get("active") is False:
                continue

            if item_data.get("ssh_name_filter") is not None and server is not None and not any(elem in server.get("ssh_name") for elem in item_data.get("ssh_name_filter")):
                continue

            item_design = item_data.get("design_template")
            delimiter = ":"
            if item_design.get("delimiter") is not None:
                delimiter = item_design.get("delimiter")
            
            if item_design.get("output") is not None:
                output_format = item_design.get("output")

            final_result = []
            rpt_start = False
            json_type = "dict"
            h5_headers_index_sub = ""

            if output_format == "json" and result_list!= None and len(result_list)>0:
                if item_design is None:
                    cs = design_template.get("content_start")
                else:
                    cs = item_design.get("content_start")
                if cs != None:
                    
                    temp_result = loop_iterate(result_list,cs)
                    if temp_result != None:
                        final_result = temp_result
                    temp_result = []
                    table_attributes = {"style":"border: none"}
                    if type(final_result) == dict:
                        if "rds_instance_system_summary" in extract_info:
                            formatted_table = json2html.convert(json = final_result,table_attributes='class="'+item_design.get("css_class")+'"')
                            temp_result.append(formatted_table)
                        else:
                            for each_result_item in final_result:
                                #,table_attributes=table_attributes
                                json_val = final_result.get(each_result_item)
                                if json_val is True or json_val is False:
                                    json_val = str(json_val)
                                formatted_table = json2html.convert(json = json_val,table_attributes=table_attributes)
                                temp_result.append(each_result_item+" "+delimiter+" "+formatted_table)
                    else:
                        # json_type = "list"
                        if len(final_result)>0:
                            column_headers = list(final_result[0].keys())
                            header_str = ""
                            if item_design.get("data_type") in ["table","horizontal_table","htov_table"]:
                                for header in column_headers:
                                    header_str += str(header) +" "+ delimiter
                                temp_result.append(header_str)
                                for each_result_item in final_result:
                                    row = ""
                                    for key in each_result_item:
                                        row += str(each_result_item.get(key))+" "+delimiter
                                    temp_result.append(row)
                            elif item_design.get("data_type") in ["jsontable"]:
                                json_type = "list"
                                formatted_table = json2html.convert(json = final_result,table_attributes=table_attributes)
                                str_description = ""
                                if item_data.get("description") != None and item_data.get("description") != "":
                                    str_description = '<p class="description">'+item_data["description"]+'</p>'
                                bottom_description = item_data.get("bottom_description")
                                if bottom_description is not None and bottom_description == True :
                                    formatted_table = '<div class="'+item_design.get("css_class")+'">'+formatted_table + str_description+'</div>'
                                else:
                                    formatted_table = '<div class="'+item_design.get("css_class")+'">'+str_description + formatted_table+'</div>'
                                # temp_result.append(formatted_table+" "+delimiter)
                                temp_result.append(formatted_table)
                            else:
                                for header in column_headers:
                                    temp_result.append(header)
                                for each_result_item in final_result:
                                    row = ""
                                    for idx,key in enumerate(each_result_item):
                                        # row += str(each_result_item.get(key))+" "+delimiter
                                        temp_result[idx] +=  delimiter +" "+ str(each_result_item.get(key))
                                pass
                            # temp_result.append(formatted_table)
                            # formatted_table = json2html.convert(json = final_result,table_attributes=table_attributes,clubbing=False)
                            # formatted_table = '<div class="'+item_design.get("css_class")+'">'+formatted_table+'</div>'
                            # temp_result.append(formatted_table)
                        else:
                            temp_result = []
                    if len(temp_result)>0:
                        final_result = temp_result
            elif item_design is not None and output_format != "" and output_format in ["sysRecommendations","instanceLevelRecommendations","mongoRecommendations"] and result_list!= None and len(result_list)>0:
                json_type = "list"
                cs = item_design.get("content_start")
                h5_headers = ""
                host_name_in_recommendation = True
                if cs != None:
                    temp_result = loop_iterate(result_list,cs)
                    if temp_result is None:
                        continue
                    if type(temp_result) == dict:
                        host_name_in_recommendation = False
                        temp_result = [{"host":"",output_format:temp_result}]
                    my_const.global_settings["paging"].add_h4()
                    for i in range(len(temp_result)):
                        resdata = temp_result[i]
                        host = resdata.get("host")
                        recommendations = resdata.get(output_format)
                        html_temp = ""
                        h6_headers = ""
                        h6_headers_index_sub = ""
                        header_no_text_h5 = my_const.global_settings["paging"].add_h5()
                        if recommendations is not None:
                            for recommendation in recommendations:
                                recommendation_dict = recommendations.get(recommendation)
                                if recommendation_dict is not None:
                                    text = ""
                                    reason_list = recommendation_dict.get("Reason")
                                    for reason in reason_list:
                                        text += '<li>'+reason+'</li>'
                                    if text !="":
                                        text = '<p class="recommendation_tilte">Info: </p><ul>'+text+'</ul>'
                                    short_recommendation = recommendation_dict.get("Recommendation")
                                    actual_recommendation = ""
                                    if short_recommendation is not None and short_recommendation != "":
                                        actual_recommendation = '<p class="recommendation_tilte">Recommendations: </p><p>'+short_recommendation+'</p>'
                                    header_no_text = my_const.global_settings["paging"].add_h6()
                                    h6_headers = '<h6 id="'+header_no_text.strip()+'">'+header_no_text+recommendation+'</h6>'
                                    h6_headers_index_sub += '<tr><td><h6 class="index_header"><a href="#'+header_no_text.strip()+'">'+header_no_text+str(recommendation)+"</a></h6></td></tr>"    
                                    html_temp += h6_headers+actual_recommendation+"<p>"+ text + "</p>"

                        if html_temp != "" and h6_headers != "":
                            h5_headers = ""
                            if host_name_in_recommendation:
                                h5_headers = '<h5 id="'+header_no_text_h5.strip()+'">'+header_no_text_h5+" Host Name "+host+'</h5>'
                                h5_headers_index_sub += '<tr><td><h5 class="index_header"><a href="#'+header_no_text_h5.strip()+'">'+header_no_text_h5+str(host)+"</a></h5></td></tr>" + h6_headers_index_sub
                            else:
                                h5_headers_index_sub += h6_headers_index_sub
                            final_result.append(h5_headers + html_temp)
                        else:
                            my_const.global_settings["paging"].minus_h5()    
                    
                    my_const.global_settings["paging"].minus_h4()
            elif item_design is not None and output_format != "" and output_format == "rating" and result_list!= None and len(result_list)>0:
                json_type = "dict"
                cs = item_design.get("content_start")
                if cs != None:
                    temp_result = loop_iterate(result_list,cs)
                    if temp_result is not None:
                        temp_result = temp_result.replace(" ","").replace("%","")
                        final_result.append("Health|Value")
                        final_result.append("Healthy|"+str(temp_result))
                        final_result.append("Need Improvements|"+str(100 - float(temp_result)))
                    else:
                        return html_str,resp.MyResponse("E","key not exist",str(cs))
            else:
                if item_design.get("content_start") != None:
                    for item in result_list:
                        if item != "" and item.startswith(item_design.get("content_start")):
                            rpt_start = True
                        elif rpt_start == True and item_design.get("content_end") != None and (item_design.get("content_end") != "" and item.strip().startswith(item_design.get("content_end")) or item == item_design.get("content_end")):
                            rpt_start = False
                            break
                        elif item != "" and rpt_start == True:
                            final_result.append(item)
                elif item_design.get("content_contains_start") != None:
                    start_loc = -1#this code block for postgres AI increment
                    end_loc =-1
                    for item in result_list:
                        if item != "" and item_design.get("content_contains_start") in item:
                            rpt_start = True
                            start_loc = item.find(item_design.get("content_contains_start"))
                        if rpt_start == True and item_design.get("content_contains_end") != None and (item_design.get("content_contains_end") != "" and item_design.get("content_contains_end") in item.strip() or item == item_design.get("content_contains_end")):
                            rpt_start = False
                            end_loc = item.find(item_design.get("content_contains_end"))
                        if item != "" and start_loc != -1:
                            final_result.append(item[start_loc:end_loc])
                            break
                    
                else:
                    final_result = result_list
            if json_type != "list":
                # to process CSV formated data
                final_result = common.analyse_response(final_result,item_data)
                html_op,_,err = server_stats_to_html(extract_item,final_result,item_data)
                if err is not None:
                    return html_str,err
            else:
                html_op = " ".join(final_result)
                # final_result = common.analyse_response(final_result,item_data)
                # html_op,_ = server_stats_to_html(extract_item,final_result,item_data)

            if html_op != "":
                header_no_text = my_const.global_settings["paging"].add_h4()
                # str_description = ""
                # if item_data.get("description") != None and item_data.get("description") != "":
                #     str_description = '<p class="description">'+item_data["description"]+'</p>'
                html_op = '<div class="dbreak"><h4 id="'+header_no_text.strip()+'">'+header_no_text+prefix_title+item_data.get("report_name")+"</h4>"+html_op+"</div>"
                my_const.global_settings["index_subhead_content"] += '<tr><td><h4 class="index_header"><a href="#'+header_no_text.strip()+'">'+header_no_text+prefix_title+str(item_data.get("report_name"))+"</a></h4></td></tr>" + h5_headers_index_sub
                html_str += html_op
        if error_string != "":
            log.print_log("W","Common.analyse_server_stats_response, from node : "+error_string)
    except Exception as e:
        log.print_log("W","exception when analyse statistics response : ",e)
        # raise e
        return html_str,resp.MyResponse("E","eexception when analyse statistics response",e)
    return html_str,None

def analyse_stored_stats_response(result_list,extract_info=None,design_template=None,server = None):
    html_str = ""
    try:
        error_string = ""
        output_format = "text"
        if design_template is not None:
            output_format = design_template.get("output")
            json_list = design_template.get("list")
            if output_format == "json" and json_list is True:
                cs = design_template.get("content_start")
                op = loop_iterate(result_list,cs)
                for op_item in op:
                    prefix_title = op_item.get("host") +" - "
                    html_temp,err = analyse_server_stats_response(op_item,extract_info,design_template,server,prefix_title)
                    if err is not None:
                        return html_str,err
                    if html_temp != "":
                        html_str += html_temp
            else:
                html_str,err = analyse_server_stats_response(result_list,extract_info,design_template,server)
                if err is not None:
                    return html_str,err
        else:
            html_str,err = analyse_server_stats_response(result_list,extract_info,design_template,server)
            if err is not None:
                    return html_str,err
    except Exception as e:
        log.print_log("W","exception when analyse stored statistics response : ",e)
        # raise e
        return html_str,resp.MyResponse("E","exception when analyse stored statistics response",e)
    return html_str,None
