from constants import constant as my_const
import util.Log as log
import instance.Local as Local
from instance.VirtualInstance import VirtualInstance as vi
from util import Common as common, response as resp
from . import qan,pmm,response_to_html


def execute_task(report_info,server,dbconfig={}):
    log.print_log("S","Common.process_task, entered report -> " +str(report_info.get("report_name")))
    design_template = report_info.get("design_template")
    html_str = ""
    try:
        client_name = server.get("client_name")
        if (report_info.get("report_cat") == "ticket_stats" or report_info.get("report_cat") == "critical_stats") and dbconfig is not None:
            query_file = report_info.get("query_file")
            query_data_base = report_info.get("data_base")
            if query_data_base == None:
                query_data_base = ""
            if query_file!= None and query_file!= "":
                all_query = common.read_file("sql_query/"+server.get("db_type")+"/"+query_file)
                if all_query != None and all_query != "":
                    from_time = server.get("from_time")
                    to_time = server.get("to_time")
                    all_query = common.replace_command_tokens(all_query,server)
                    # all_query = all_query.replace("<FROM_TIME>",from_time).replace("<TO_TIME>",to_time).replace("<CLIENT_NAME>",client_name)
                    if query_data_base !="":
                        dbconfig["database"] = query_data_base
                    ticket_cmd_response,err_tc = Local.send_command(dbconfig,all_query)
                    if err_tc is not None:
                        return html_str,err_tc
                    result = common.analyse_response(ticket_cmd_response,report_info)
                    html_str,_,err = response_to_html.server_stats_to_html("",result,report_info)
                    if err is not None:
                        return html_str,err
        elif report_info.get("report_cat") == "server_stats":
            command = report_info.get("command")
            get_from_store = report_info.get("get_from_store")
            design_template = report_info.get("design_template")
            extract_info = report_info.get("extract_info")
            if command is not None and get_from_store is None:
                cmd_wise_connection_type = report_info.get("connection_type")
                command = common.replace_command_tokens(command,server)
                if type(command) is list:
                    tmp_command = command.copy()
                else:
                    tmp_command = command
                store = report_info.get("store")
                output_format = "text"
                result = None
                if design_template != None:
                    output_format = design_template.get("output")
                    if output_format != None and output_format == "json" and server.get("my_server") != None and ".rds." in server.get("my_server") and server.get("ssh_name") != None and type(tmp_command) == list:
                        tmp_command.extend([server.get("ssh_name"),"--profile",server.get("aws_profile_name")]) #"--db-instance-identifier",
                if (cmd_wise_connection_type is not None and cmd_wise_connection_type=="local") or (server.get("instance") != None and server.get("connection_type") != None and server.get("instance") in ["aws","azure","gcloud","do","ibm","sas","rds"] or (server.get("instance") == "virtual" and server.get("connection_type") == "local" )):

                    result,err_server = Local.send_command(tmp_command,output_format=output_format)
                    if err_server is not None:
                        return html_str,err_server
                elif (cmd_wise_connection_type is not None and cmd_wise_connection_type=="ssh") or (server.get("instance") != None and server.get("connection_type") != None and server.get("connection_type") == "ssh" and server.get("instance") == "virtual"):
                    global_settings = my_const.global_settings
                    if global_settings.get("ssh_path") != "":
                        ov = vi(server)
                        if type(tmp_command) == list:
                            execute_command = " ".join(tmp_command)
                        else:
                            execute_command = tmp_command
                        result,err_server = ov.send_command(execute_command,output_format=output_format)
                        if err_server is not None:
                            return html_str,err_server
                if result is not None:
                    if store is not None:
                        my_const.global_settings[store] = result
                    html_str,err = response_to_html.analyse_server_stats_response(result,extract_info,design_template,server)
                    if err is not None:
                        return html_str,err
            elif get_from_store is not None and my_const.global_settings.get(get_from_store) is not None:
                result = my_const.global_settings.get(get_from_store)
                html_str,err = response_to_html.analyse_stored_stats_response(result,extract_info,design_template,server)
                if err is not None:
                    return html_str,err
        elif report_info.get("report_cat") == "qan_analysis":
            result = qan.get_qan_details(report_info,server)
            result = common.analyse_response(result,report_info)
            html_str,_,err = response_to_html.server_stats_to_html("",result,report_info)
            if err is not None:
                return html_str,err

        elif report_info.get("report_cat") == "graph_stats":

            html_str += pmm.analyze_graph_response(report_info,server)

        elif report_info.get("report_cat") == "sql_stats":
            filter_by_result_from_query = {}

            mysql_filter = report_info.get("mysql_filter")
            server_mysql_filter = server.get("mysql_filter")
            if mysql_filter != None and mysql_filter != "" and server_mysql_filter != {}:
                is_enabled_server_mysql_filter = server_mysql_filter.get(mysql_filter)
                if is_enabled_server_mysql_filter == False:
                    return "",None
                elif type(is_enabled_server_mysql_filter) == int:
                    filter_by_result_from_query = {"type":"int","value":is_enabled_server_mysql_filter}
    
            query_file = report_info.get("query_file")
            query_data_base = report_info.get("data_base")
            extract_info = report_info.get("extract_info")
            if query_data_base == None:
                query_data_base = ""
            if query_file != None and query_file!="":
                data_set_result = {}
                all_query = common.read_file("sql_query/"+server.get("db_type")+"/"+query_file)
                if all_query != None and all_query != "":
                    from_time = server.get("from_time")
                    to_time = server.get("to_time")
                    all_query = common.replace_command_tokens(all_query,server)
                    # all_query = all_query.replace("<FROM_TIME>",from_time).replace("<TO_TIME>",to_time).replace("<CLIENT_NAME>",client_name)
                    if server.get("instance") != None and server.get("connection_type") != None and server.get("instance") in ["aws","azure","gcloud","do","ibm","sas"] or (server.get("instance") == "virtual" and server.get("connection_type") == "local" ):
                        mysql_details = server.get("mysql_details").copy()

                        if server.get("db_type") == "mysql":
                            if query_data_base != "":
                                mysql_details.extend([" "+query_data_base])
                            mysql_details.extend(["-e",all_query])
                            for ix,ite in enumerate(mysql_details):
                                if ite.startswith("-p") and "'" in ite:
                                    mysql_details[ix] = ite.replace("'","")
                        elif server.get("db_type") == "mango":
                            html_str = ""
                        elif server.get("db_type") == "psql":
                            if query_data_base != "":
                                if "-d" not in mysql_details:
                                    mysql_details.extend(["-d",query_data_base])
                                elif "-d" in mysql_details and query_data_base not in mysql_details:
                                    idx = common.get_index_of_string_from_list("-d",mysql_details)
                                    if idx is not None:
                                        mysql_details[idx+1] = query_data_base
                            mysql_details = " ".join(mysql_details)+""" -c \" """+all_query+""" \" """

                        query_response,err_query = Local.send_command(mysql_details)
                        if err_query is not None:
                            return html_str,err_query
                        result = common.analyse_response(query_response,report_info)
                        if result != None and filter_by_result_from_query != {} and filter_by_result_from_query.get("type") == "int":
                            if len(result) > filter_by_result_from_query.get("value"):
                                return "",None
                        html_str,data_set_result,err = response_to_html.server_stats_to_html("",result,report_info)
                        if err is not None:
                            return html_str,err

                    elif server.get("instance") != None and server.get("connection_type") != None and server.get("connection_type") == "ssh" and server.get("instance") == "virtual":
                        global_settings = my_const.global_settings
                        if server.get("ssh_name") != None and global_settings.get("ssh_path") != "":
                            # ssh_host = global_settings.get("ssh_host")
                            # if ssh_host.get(server.get("ssh_name")) !=None and ssh_host.get(server.get("ssh_name")) != "":
                            mysql_details = server.get("mysql_details").copy()
                            if server.get("ssh_name") != "":
                                ov = vi(server)
                                if server.get("db_type") == "mysql":
                                    execute_command = " ".join(mysql_details)+""" """+query_data_base+""" -e \" """+all_query+""" \" """
                                elif server.get("db_type") == "mango":
                                    html_str = ""
                                elif server.get("db_type") == "psql":
                                    if query_data_base != "":
                                        if "-d" not in mysql_details:
                                            mysql_details.extend(["-d",query_data_base])
                                        elif "-d" in mysql_details and query_data_base not in mysql_details:
                                            idx = common.get_index_of_string_from_list("-d",mysql_details)
                                            if idx is not None:
                                                mysql_details[idx+1] = query_data_base
                                    execute_command = " ".join(mysql_details)+""" -c \" """+all_query+""" \" """
                                
                                try:
                                    query_response,err_query = ov.send_command(execute_command)
                                    if err_query is not None:
                                        return html_str,err_query
                                    html_result = common.analyse_response(query_response,report_info)
                                    if html_result != None and filter_by_result_from_query != {} and filter_by_result_from_query.get("type") == "int":
                                        if len(html_result) > filter_by_result_from_query.get("value"):
                                            return "",None
                                    html_str,data_set_result,err = response_to_html.server_stats_to_html("",html_result,report_info)
                                    if err is not None:
                                        return html_str,err
                                except Exception as error_send_cmd:
                                    return html_str,resp.MyResponse("E","process_query : "+str(error_send_cmd.get("result").get("stderr")))
                            else:
                                return html_str,resp.MyResponse("E","process_task","SSH DETAILS NOT FOUND : "+ server.get("ssh_name"))
                        else:
                            return html_str,resp.MyResponse("E","process_task","SSH DETAILS NOT FOUND : "+ server.get("ssh_name"))
                
                html_str_extact_info,error_extract = extract_sql_stats(design_template,report_info,server,dbconfig,data_set_result)
                if error_extract is not None:
                    return html_str,error_extract
                if html_str_extact_info != "":
                    html_str = html_str + html_str_extact_info

        return html_str,None
    except Exception as error_process_qry:
        return html_str,resp.MyResponse("E","exception when process_task",error_process_qry)


def extract_sql_stats(design_template,report_info,server,dbconfig,data_set_result):
    try:
        extract_info = report_info.get("extract_info")
        if extract_info is None:
            return "",None

        column_name = extract_info.get("column_name")
        child_stats = extract_info.get("child_stats")
        # flag_value = extract_info.get("flag_value")
        if column_name is None or child_stats is None or data_set_result is None:
            return "",None

        column_names = data_set_result.get(column_name)
        if column_names is None:
            return "",None

        html_str = ""
        output_format = "text"
        if design_template != None:
            output_format = design_template.get("output")
        
        
        for column_name_item in column_names:
            html_child = ""
            index_title = ""
            h5_text = my_const.global_settings["paging"].add_h5()
            for extract_item in child_stats:
                if extract_item.get("active") is False:
                    continue

                # if server.get("ssh_name") is not None and extract_item.get("ssh_name_filter") is not None:
                #     if ( (server.get("ssh_name") is not None and any(elem in server.get("ssh_name") for elem in extract_item.get("ssh_name_filter")) == True) or ((server.get("ssh_name") != None) and (any(elem in server.get("ssh_name_filter") for elem in extract_item.get("ssh_name_filter"))  == True))) == False:
                #         continue
                if extract_item.get("ssh_name_filter") is not None and server is not None and not any(elem in server.get("ssh_name") for elem in extract_item.get("ssh_name_filter")):
                    continue
                
                temp_server = server.copy()
                temp_mysql_details = temp_server.get("mysql_details")
                if "-d" not in temp_mysql_details and column_name_item not in temp_mysql_details:
                    temp_mysql_details.extend(["-d",column_name_item])
                elif "-d" in temp_mysql_details and column_name_item not in temp_mysql_details:
                    idx = common.get_index_of_string_from_list("-d",temp_mysql_details)
                    if idx is not None:
                        temp_mysql_details[idx+1] = column_name_item
                extract_item["report_cat"] = report_info["report_cat"]
                if extract_item.get("design_template") is None:
                    extract_item["design_template"] = report_info["design_template"]

                html_op , err = execute_task(extract_item,temp_server,dbconfig={})
                if err is not None:
                    return html_str,err

                if html_op != "":
                    header_no_text = my_const.global_settings["paging"].add_h6()
                    str_description = ""
                    if extract_item.get("description") != None and extract_item.get("description") != "":
                        str_description = '<p class="description">'+extract_item["description"]+'</p>'
                    html_op = '<div class="dbreak"><h6 id="'+header_no_text.strip()+'">'+header_no_text+extract_item.get("report_name")+"</h6>"+str_description+""+html_op+"</div>"
                    index_title += '<tr><td><h6 class="index_header"><a href="#'+header_no_text.strip()+'">'+header_no_text+str(extract_item.get("report_name"))+"</a></h6></td></tr>"
                    html_child += html_op
            if html_child != "":
                html_child = '<div class="dbreak"><h5 id="'+h5_text.strip()+'">'+h5_text+"Database Statistics for '"+ column_name_item.upper() +"'</h5>"+""+html_child+"</div>"
                my_const.global_settings["index_subhead_content"] += '<tr><td><h5 class="index_header"><a href="#'+h5_text.strip()+'">'+h5_text+"Database Statistics for '"+column_name_item.upper()+"'</a></h5></td></tr>"+index_title
                html_str += html_child
            else:
                my_const.global_settings["paging"].minus_h5()
        return html_str,None
    except Exception as err:
        return html_str,resp.MyResponse("E","exception when extract sql stats",err)