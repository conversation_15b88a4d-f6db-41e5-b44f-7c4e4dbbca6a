#you can exclude the outputs generated by commands or query
exclude: &exclude
  starts_with: ["mysql:","__","---------","==","     SELinux","  Locator","  ===","+-","    +-","-------+","    -------"]
  contains: ["ERROR 1054 ","ERROR 1356 "]
  ends_with: ["rows)", row)]

exclude_pg: &exclude_pg
  starts_with: ["  ===","+-","    +-","    |"]
  contains: []
  ends_with: ["rows)", row)]

exclude_super_privileges: &exclude_super_privileges
  starts_with: ["mysql:","__","--","==","     SELinux","  Locator","  ===","mysql.session"]

exclude_table_create: &exclude_table_create
  starts_with: ["ERROR:"]

excludeqan: &excludeqan
  starts_with: ["SHOW","SET","SELECT @@","BEGIN","USE"]
  contains: ["INFORMATION_SCHEMA","information_schema","MYSQL.","mysql.","`mysql`","PERFORMANCE_SCHEMA","performance_schema","SYS.","sys.","`sys`","pfs.","`pfs`","PFS.","start transaction","Commit","rollback","SELECT @@","||||SHOW","||||SET","||||BEGIN","||||USE"]

exclude_rds_json: &exclude_rds_json
  starts_with: ["DBSubnetGroup"]

# can filter output for server informations
content_start: &content_start "# "

content_end: &content_end "# "

pg_metrics_content_end: &pg_metrics_content_end ""

bytes_to_human_readable: &bytes_to_human_readable "out_put = bytes_to_readable('in_put')"

rds_instance_table_template: &rds_instance_table_template
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "~"
  output: "json"
  exclude: *exclude_rds_json
  content_start: ["DBInstances",0]
  #content_start should be ["DBClusterEndpoints",0] for --db-cluster-identifier command [Aurora cluster command]

text_template: &text_template
  data_type: text
  css_class: p
  exclude: *exclude

#PMM2 Graph - Will Group the all charts each row will contain 4 graphs

graph_template_unit: &graph_template_unit
  data_type: text
  css_class: unite text_center
  exclude: *exclude
  graph_settings:
    width: 300
    height: 125
    refresh: 1m
    orgId: 1

graph_template: &graph_template
  data_type: text
  css_class: separate
  exclude: *exclude
  graph_settings:
    width: 1000
    height: 500
    refresh: 1m
    orgId: 1


qan_analysis_template: &qan_analysis_template
  data_type: htov_table
  css_class: table header_green_text htov_table
  delimiter: "||||"
  exclude: *excludeqan
  data_separator: "Query   -   "

sum_sql_table_template: &sum_sql_table_template
  data_type: table
  css_class: table header_green_text sum
  delimiter: "\t"
  exclude: *exclude



table_template_super_privileges: &table_template_super_privileges
  data_type: table
  css_class: table header_green_text
  delimiter: "\t"
  exclude: *exclude_super_privileges

sql_table_template: &sql_table_template
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude

sql_table_template_htov: &sql_table_template_htov
  data_type: htov_table
  css_class: table header_green_text htov_table
  delimiter: "|"
  exclude: *exclude
  data_separator: "Result   -   "

ticket_sql_table_template: &ticket_sql_table_template
  data_type: table
  css_class: table header_green_text break_before_table
  delimiter: "\t"
  exclude: *exclude
  href: 
    column: "Ticket"
    url: "https://tickets.mydbops.com/a/tickets/"

ai_db1: &ai_db1
  data_type: htov_table
  css_class: table header_green_text
  delimiter: ")"
  exclude: *exclude
  content_contains_start: 'DB "test_db"'
  content_contains_end: 'DB "postgres"'
  allow_empty_row: yes
  limit: 30

ai_db2: &ai_db2
  data_type: htov_table
  css_class: table header_green_text
  delimiter: ")"
  exclude: *exclude
  content_contains_start: 'DB "postgres"'
  content_contains_end: 'template0'
  allow_empty_row: yes


ai_db3: &ai_db3
  data_type: htov_table
  css_class: table header_green_text
  delimiter: ")"
  exclude: *exclude
  content_contains_start: 'DB "template0"'
  content_contains_end: 'template1'
  allow_empty_row: yes

ai_db4: &ai_db4
  data_type: htov_table
  css_class: table header_green_text
  delimiter: ")"
  exclude: *exclude
  content_contains_start: 'DB "template1"'
  content_contains_end: '|'
  allow_empty_row: yes

pg_wal_info: &pg_wal_info
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude
  content_start: "WAL Files:"
  content_end: *pg_metrics_content_end

pg_vaccum_info: &pg_vaccum_info
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude
  content_start: "Vacuum Progress:"
  content_end: *pg_metrics_content_end

pg_db_user_info: &pg_db_user_info
  data_type: table 
  css_class: table role_table header_green_text fixed
  delimiter: "|"
  exclude: *exclude
  content_start: "Roles:"
  content_end: *pg_metrics_content_end

percona_table_template: &percona_table_template
  data_type: table
  css_class: table
  delimiter: "|"
  exclude: *exclude
  content_start: *content_start
  content_end: *content_end

percona_system_template: &percona_system_template
  data_type: vertical_table
  css_class: table header_green_text 
  delimiter: "|"
  exclude: *exclude
  content_start: "# Percona "
  content_end: *content_end

percona_cpu_template: &percona_cpu_template
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude
  content_start: "# Processor "
  content_end: *content_end

percona_memory_template: &percona_memory_template
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude
  content_start: "# Memory "
  content_end: *content_end
 
percona_network_template: &percona_network_template
  data_type: table
  css_class: table header_green_text 
  delimiter: "|"
  exclude: *exclude
  content_start: "# Network Connections"
  content_end: *content_end

### CHART SETTINGS
chart_settings_cs_count_by_host : &chart_settings_cs_count_by_host
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "host"
    x_axis_ticks: "category"
    y_axis: "alert count"
    x_showticklabels: no
    title: ""
    info: "label+value"
    legend: true
    legend_orientation: "h"
    hole: 0.4
    img_width: 1200
    img_height: 650
    x_title: "Host"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_count_by_service : &chart_settings_count_by_service
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "service"
    x_axis_ticks: "category"
    y_axis: "alert count"
    x_showticklabels: no
    title: ""
    legend: true
    legend_title: "Services"
    legend_orientation: "h"
    img_width: 1200
    img_height: 650
    x_title: "Service"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_alert_daywise : &chart_settings_alert_daywise
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "date"
    x_axis_ticks: "D1"
    y_axis: "alert count"
    title: ""
    legend: false
    # legend_title: "Daywise Alerts"
    # legend_orientation: "h"
    img_width: 1200
    img_height: 650
    x_title: "Date"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_ticket : &chart_settings_ticket
  - chart_type: "StackedBar"
    x_axis: "Created"
    x_axis_ticks: "D1"
    y_axis: "Status"
    stacked: "Ticket"
    title: ""
    legend: true
    x_title: "Created Date"
    y_title: "Ticket Count by Status"
    img_width: 900
    img_height: 500
    style: 
      width: "100%"
  - chart_type: "Pie"
    group_by: "Status"
    value: "Ticket"
    title: ""
    info: "label+value"
    legend: true
    legend_title: "Status"
    img_height: 500
    style: 
      width: "83%"
      padding-top: 15px;
  
chart_settings_new_table : &chart_settings_new_table
  - chart_type: "Bar"
    group_by: "Created On"
    group_by_type: "datetime"
    x_axis_ticks: "category"
    x_axis_ticks_angle: -90
    value: "Table"
    title: ""
    x_title: "Created Date"
    y_title: "Table Count"
    style: 
      width: "80%"

chart_settings_active_writes : &chart_settings_active_writes
  - chart_type: "Pie"
    name: "Table"
    value: "Writes"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Table"
    img_height: 500
    style: 
      width: "80%"

chart_settings_active_reads : &chart_settings_active_reads
  - chart_type: "Pie"
    name: "Table"
    value: "Reads"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Table"
    img_height: 500
    style: 
      width: "80%"

chart_settings_data_distribution : &chart_settings_data_distribution
  - chart_type: "Pie"
    name: "Database"
    value: "Size"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Database Name"
    img_height: 500
    style: 
      width: "80%"

#DEFAULT TEMPLATE FOR STATISTICS
critical_stats_template: *ticket_sql_table_template
ticket_stats_template: *ticket_sql_table_template
server_stats_template: *percona_table_template
sql_stats_template: *sql_table_template
graph_stats_template: *graph_template
qan_stats_template: *qan_analysis_template

stats_order:
  ticket_stats: 
  - report_name: Tickets Handled
    query_file: TICKET_HANDLED.sql
    data_base: reports
    description:
    active: no
    chart_settings: *chart_settings_ticket
    ssh_name_filter: ["primary"]
    script:
      - text_match_header: "created_On"
        replace_header_text: "Created"
  critical_stats:
  - report_name: Alert Count by Host Name
    description:
    query_file: TICKET_alert_by_host.sql
    data_base: whitewalker
    show_table: no
    active: no
    chart_settings: *chart_settings_cs_count_by_host
  - report_name: Alert Count for Top Services
    description:
    query_file: TICKET_alert_top_services.sql
    data_base: whitewalker
    show_table: no
    active: no
    chart_settings: *chart_settings_count_by_service
  - report_name: Daywise Alert Count
    description:
    query_file: TICKET_alert_daywise.sql
    data_base: whitewalker
    show_table: no
    active: no
    chart_settings: *chart_settings_alert_daywise
  server_stats: 
  - report_name: RDS Instance Statistics
    #In command we can add new parameters like "region=ap-south-1" and --db-instance-identifier or --db-cluster-identifier should define in last parameter in command
    # command: aws rds describe-db-instances --output=json --db-instance-identifier {{ssh_name}} --region {{aws-region}} --db-cluster-identifier {{cluster-identifier}}
    # command: ["aws", "rds", "describe-db-instances", "--output=json","--db-instance-identifier"]
    active: no
    description:
    design_template: *rds_instance_table_template
    ssh_name_filter:
    extract_info: 
      rds_instance_system_summary:
        report_name: System Information
        active: yes
        description: 
        design_template: *rds_instance_table_template

  - report_name: Server Information
    command: ["pt-summary"]
    active: yes
    description:
    ssh_name_filter:
    extract_info: 
      percona_toolkit_system_summary:
        report_name: System Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_system_template
        script:
          - text_match_header: "Uptime"
            replace_header_text: ""
            py_code: "up_time = 'in_put'\nut_ls = up_time.split(',')\nif len(ut_ls)>2:\n    out_put = (ut_ls[0]+', '+ut_ls[1])"
      processor:
        report_name: CPU Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_cpu_template
        script:
          - text_match_header: "Speeds"
            replace_header_text: "CPU Speed"
            py_code: "cpu_speed = 'in_put'.split('x')\nif len(cpu_speed)==2:\n    out_put = cpu_speed[0]+'x'+str(round(float(cpu_speed[1])/1000,2))+' GHz'"
      memory:
        report_name: Memory Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_memory_template
      network_connections:
        report_name: Network Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_network_template
  - report_name: Database/Postgres Metrics
    connection_type: local
    #command: PGPASSWORD='{{my_pass}}' /opt/Mydbops_ReportEngine_Source/ReportEngine/plugin/pgmetrics_1.14.1_linux_amd64/pgmetrics -h {{my_server}} -U {{my_user}} -p {{my_port}} 'postgres'
    command: PGPASSWORD='{{my_pass}}' /opt/Mydbops_ReportEngine_Source/ReportEngine/pgmetrics_1.14.1_linux_amd64/pgmetrics -h {{my_server}} -U {{my_user}} -p {{my_port}} 'postgres'
    active: yes
    description:
    ssh_name_filter:
    extract_info: 
      pg_wal_info:
        report_name: WAL Information
        active: yes
        description: Following are some of the WAL   details from the database server
        ssh_name_filter:
        design_template: *pg_wal_info
      pg_vaccum_info:
        report_name: Vacuum  Information
        active: yes
        description: Following are the Vacuum details in the database
        ssh_name_filter:
        design_template: *pg_vaccum_info
      pg_db_user_info:
        report_name: Database User information
        active: yes
        description: Following is the list of the database users 
        ssh_name_filter:
        design_template: *pg_db_user_info
  - report_name: Database Security
    command: PGPASSWORD='{{my_pass}}' psql -h {{my_server}} -U {{my_user}} -p {{my_port}} -d 'postgres' -c "SELECT usename AS role_name, CASE  WHEN usesuper AND usecreatedb THEN    CAST('superuser, create database' AS pg_catalog.text)  WHEN usesuper THEN    CAST('superuser' AS pg_catalog.text)  WHEN usecreatedb THEN    CAST('create database' AS pg_catalog.text)  ELSE    CAST('' AS pg_catalog.text) END role_attributes FROM pg_catalog.pg_user ORDER BY role_name desc;"
    active: yes
    description:
    ssh_name_filter:
    extract_info: 
      super_privilege_user:
        report_name: Superuser privilege users
        active: yes
        description: Following is the list of the DB users having super user privileges
        ssh_name_filter:
        design_template: *sql_table_template

  - report_name: Auto Increment
    # command: /opt/Mydbops_ReportEngine_Source/ReportEngine/plugin/mydbops_check_postgres --action=sequence --host={{my_server}} --dbuser={{my_user}} --dbname={{ai_db}} --warning=0% --perf=1 --dbpass={{my_pass}}
    #command: ["bash","-c",'"',"/opt/Mydbops_ReportEngine_Source/ReportEngine/plugin/mydbops_check_postgres","--action=sequence","--host={{my_server}}","--dbuser={{my_user}}","--dbname={{ai_db}}","--warning=0%","--perf=1","--dbpass={{my_pass}}",'"']
    #command: ["/opt/Mydbops_ReportEngine_Source/ReportEngine/plugin/mydbops_check_postgres","--action=sequence","--host={{my_server}}","--dbuser={{my_user}}","--dbname={{ai_db}}","--warning=0%","--perf=1","--dbpass='{{my_pass}}'"] #for debian
    command: ["/home/<USER>/mydbops_check_postgres --action=sequence --host={{my_server}} --dbuser={{my_user}} --dbname={{ai_db}} --warning=0% --perf=1  --dbpass={{my_pass}}"] #for debian
    active: yes
    description:
    ssh_name_filter:
    extract_info: 
      test_db_ai:
        report_name: Top 30 inavitas_access DB Auto Increment Details
        active: yes
        description:
        ssh_name_filter:
        design_template: *ai_db1
        script:
          - py_code: "out_put = ('in_put').replace(' (',' , ').replace('(','')"
            run_code: yes
      postgres_ai:
        report_name: Top 30 inavitas_alarm DB Auto Increment Details
        active: yes
        description:
        ssh_name_filter:
        design_template: *ai_db2
        script:
          - py_code: "out_put = ('in_put').replace(' (',' , ').replace('(','')"
            run_code: yes
      template0_ai:
        report_name: Top 30 inavitas_analytics DB Auto Increment Details
        active: yes
        description:
        ssh_name_filter:
        design_template: *ai_db3
        script:
          - py_code: "out_put = ('in_put').replace(' (',' , ').replace('(','')"
            run_code: yes
      template1_ai:
        report_name: Top 30 inavitas_command DB Auto Increment Details
        active: yes
        description:
        ssh_name_filter:
        design_template: *ai_db4
        script:
          - py_code: "out_put = ('in_put').replace(' (',' , ').replace('(','')"
            run_code: yes         

  sql_stats:
  - report_name: Database Engine uptime
    description:
    query_file: uptime_detail.sql
    data_base: postgres
    active: yes
    ssh_name_filter:
    design_template: *sql_table_template_htov
    script:
      - text_match_header: "uptime"
        replace_header_text: "Uptime"
  - report_name: Database Engine Version
    description:
    query_file: version.sql
    data_base:
    active: yes
    ssh_name_filter:
    design_template: *sql_table_template_htov
    script:
      - text_match_header: "version"
        replace_header_text: "Version & Architecture"
  - report_name: Data Distribution
    description: The below table lists the size of the production databases  (Excluding System Databases)
    query_file: data_distribution.sql
    data_base: postgres
    active: yes
    ssh_name_filter:
    chart_settings: *chart_settings_data_distribution
    script:
      - text_match_header: "size"
        py_code: *bytes_to_human_readable
        replace_header_text: "Size"
      - text_match_header: "database"
        replace_header_text: "Database"
    extract_info:
      column_name: Database
      child_stats:
        - report_name: Top 10 tables by size
          description: Here are the top 10 biggest tables by size in the database
          query_file: top10_table_by_size.sql
          active: yes
          ssh_name_filter: 
        - report_name: Top 10 bloated tables
          description: Here are the top 10 bloated  tables by size in the database
          query_file: top10_bloated_tables.sql
          active: yes
          ssh_name_filter:
        - report_name: Top 30 Unused Indexes
          description: Here are the top Unused Indexes by size in the database
          query_file: top_unused_index.sql
          active: yes
          ssh_name_filter:
        - report_name: Top 30 Duplicate  Indexes
          description: These are Multiple indexes that have the same set of columns, same opclass, expression and predicate , which make them equivalent. Usually it's safe to drop one of them 
          query_file: duplicate_index.sql
          active: yes
          ssh_name_filter: 
        - report_name: Top 30 Tables without Primary/Unique keys
          description:  Following is the list of the tables that are not having any primary and/or unique keys ,  It is suggested to have primary and unique keys , it improves the performance of the queries that perform a  search or sort on the basis of the key values .
          query_file: tables_with_out_primarykey.sql
          active: yes
          ssh_name_filter: 
        - report_name: Superuser privilege users
          description: Following is the list of the DB users having super user privileges
          query_file: super_privilege_users.sql
          active: no
          ssh_name_filter: 
        - report_name: Top 10 Active tables by write operations
          description: Following is the list of top 10  most active tables by write operations.
          query_file: active_writes.sql
          active: yes
          ssh_name_filter: 
        - report_name: Top 10 Active tables by read operations
          description: Following is the list of top 10  most active tables by read operations.
          query_file: active_reads.sql
          active: yes
          ssh_name_filter:
        - report_name: Open transactions Count
          description: Following is the count of idle transactions in the database that have been left open , closing of these transactions is important else these can hold up locks on the tables that are being accessed through the transactions.
          query_file: idle_transactions.sql
          active: yes
          ssh_name_filter:
          design_template: *sql_table_template_htov
        - report_name: List of extensions
          description: Following is the list of the installed postgresql extensions in the database.
          query_file: list_extension.sql
          active: yes
          ssh_name_filter:
          
  graph_stats:
  - report_name: System Usage Graphs
    description: 
    active: yes
    individual_graph_name: yes
    ssh_name_filter:
    graphs:
      - p68
      - p66
      - p70
      - p72
  - report_name: Postgres Statistics
    description: 
    active: yes
    individual_graph_name: yes
    ssh_name_filter:
    graphs:
      - p74
      - p76
      - p78
      - p80
      - p82
      - p84

  qan_analysis:
  - report_name: Top Slow Queries By Execution Count
    description:
    query_file: psql_by_count.sql
    active: yes
    limi: 10
    period: 1440
  - report_name: Top Slow Queries By Execution Time
    description:
    query_file: psql_by_execution_time.sql
    active: yes
    limi: 10
    period: 1440


#For ticket and critical stats we can only add description at top.
description:
  server_stats:
    top:
    bottom:
  sql_stats:
    top:
    bottom:
  graph_stats:
    top:
    bottom:
  qan_analysis: 
    top: The query analytics details are gathered from the performance_schema in MySQL, The max length of the sample digest query that can be stored is limited to 1024 bytes. Given this limitation, The query sample shown in the report can be a truncated query, In the event of analysing the truncated query, Kindly co-relate the digest pattern with the actual query in the application tier.
    bottom:
