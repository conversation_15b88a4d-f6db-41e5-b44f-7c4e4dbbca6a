#you can exclude the outputs generated by commands or query
exclude: &exclude
  starts_with: ["mysql:","__","--","==","     SELinux","  Locator","  ==="]
  contains: ["ERROR 1054 ","ERROR 1356 "]

exclude_super_privileges: &exclude_super_privileges
  starts_with: ["mysql:","__","--","==","     SELinux","  Locator","  ===","mysql.session"]

exclude_table_create: &exclude_table_create
  starts_with: ["ERROR:"]

excludeqan: &excludeqan
  starts_with: ["SHOW","SET","SELECT @@","BEGIN","USE"]
  contains: ["INFORMATION_SCHEMA","information_schema","MYSQL.","mysql.","`mysql`","PERFORMANCE_SCHEMA","performance_schema","SYS.","sys.","`sys`","pfs.","`pfs`","PFS.","start transaction","Commit","rollback","SELECT @@","||||SHOW","||||SET","||||BEGIN","||||USE"]

exclude_json: &exclude_json
  starts_with: ["DBSubnetGroup"]

# can filter output for server informations
content_start: &content_start "# "

content_end: &content_end "# "

bytes_to_human_readable: &bytes_to_human_readable "out_put = bytes_to_readable('in_put')"

json_to_table: &json_to_table 'out_put = json2html.convert(json = (in_put))'

percona_system_template: &percona_system_template
  data_type: vertical_table
  css_class: table header_green_text 
  delimiter: "|"
  exclude: *exclude
  content_start: "# Percona "
  content_end: *content_end

mongo_template: &mongo_template
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics"]
  content_end: *content_end

system_stats: &system_stats
  data_type: vertical_table
  css_class: table header_green_text mongo_vertical_header_160
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics","systemStats"]
  content_end: *content_end
  data_separator: "Host   -   "
instance_stats: &instance_stats
  data_type: vertical_table
  css_class: table header_green_text mongo_vertical_header_180
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics","instanceLevelStats"]
  content_end: *content_end
  data_separator: "Host   -   "
mongo_metrics: &mongo_metrics
  data_type: vertical_table
  css_class: table header_green_text mongo_vertical_header_160
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics","mongoMetrics"]
  content_end: *content_end
  data_separator: "Host   -   "

data_distribution: &data_distribution
  data_type: table
  css_class: table header_green_text sum
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbDistribution"]
  content_end: *content_end

top_collection: &top_collection
  data_type: htov_table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbConsiderableCollections","topCollections"]
  content_end: *content_end
  data_separator: "Collection   -   "
empty_collection: &empty_collection
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbConsiderableCollections","emptyColl"]
  content_end: *content_end

op_counters: &op_counters
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbOpCounters"]
  content_end: *content_end

indexes: &indexes
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbIndexes"]
  content_end: *content_end


root_users: &root_users
  data_type: jsontable
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["mydbUsers","rootUsers"]
drop_users: &drop_users
  data_type: jsontable
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["mydbUsers","dropUsers"]
dummy_users: &dummy_users
  data_type: jsontable
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["mydbUsers","dummyUsers"]
noAccessDBs: &noAccessDBs
  data_type: jsontable
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["mydbUsers","noAccessDBs"]

ttl_indexes: &ttl_indexes
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbIndexes","TtlIndexes"]
  content_end: *content_end
duplicate_indexes: &duplicate_indexes
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbIndexes","DuplicateIndexes"]
  content_end: *content_end
unused_indexes: &unused_indexes
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  content_start: ["myDbUnusedIndex"]
  content_end: *content_end


recommendations: &recommendations
  data_type: htov_table
  css_class: table header_green_text
  delimiter: "|"
  output: "json"
  exclude: *exclude_json
  list: yes
  content_start: ["myDbHealthMetrics","Recommendations"]

mongoCustom: &mongoCustom
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  output: "mongoCustom"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics"]

mongoCustom2: &mongoCustom2
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  output: "mongoCustom2"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics"]

system_recommendations: &system_recommendations
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  output: "sysRecommendations"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics","Recommendations"]
instance_recommendations: &instance_recommendations
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  output: "instanceLevelRecommendations"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics","Recommendations"]
mongo_recommendations: &mongo_recommendations
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  output: "mongoRecommendations"
  exclude: *exclude_json
  content_start: ["myDbHealthMetrics","Recommendations"]

chart_settings_op_counters_deleted: &chart_settings_op_counters_deleted
  - chart_type: "Histogram"
    color_bar: yes
    x_showticklabels: yes
    title: "OP Counters From Uptime"
    info: "label+value"
    legend: true
    legend_orientation: "h"
    hole: 0.4
    img_width: 1200
    img_height: 650
    x_title: "OP Counter"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_op_counters: &chart_settings_op_counters
  - chart_type: "Pie"
    name: "host"
    value: "Insert"
    title: "Insert Operations"
    info: "percent"
    legend: yes
    legend_title: "Insert"
    img_height: 500
    style: 
      width: "46%"
  - chart_type: "Pie"
    name: "host"
    value: "Query"
    title: "Query Operations"
    info: "percent"
    legend: yes
    legend_title: "Query"
    img_height: 500
    style: 
      width: "46%"
  - chart_type: "Pie"
    name: "host"
    value: "Update"
    title: "Update Operations"
    info: "percent"
    legend: yes
    legend_title: "Update"
    img_height: 500
    style: 
      width: "46%"
  - chart_type: "Pie"
    name: "host"
    value: "Delete"
    title: "Delete Operations"
    info: "percent"
    legend: yes
    legend_title: "Delete"
    img_height: 500
    style: 
      width: "46%"
  - chart_type: "Pie"
    name: "host"
    value: "Getmore"
    title: "Getmore Operations"
    info: "percent"
    legend: yes
    legend_title: "Getmore"
    img_height: 500
    style: 
      width: "46%"
  - chart_type: "Pie"
    name: "host"
    value: "Command"
    title: "Command Operations"
    info: "percent"
    legend: yes
    legend_title: "Command"
    img_height: 500
    style: 
      width: "46%"

chart_settings_rating : &chart_settings_rating
  - chart_type: "Pie"
    name: "Health"
    value: "Value"
    chart_colors: ["#3366CC","#990099"]
    title: ""
    info: "label+percent"
    legend: yes
    legend_title: "Health"
    img_height: 500
    style: 
      width: "80%"

mongo_health_rating: &mongo_health_rating
  data_type: table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude
  output: "rating"
  content_start: ["Rating"]
   
rds_instance_table_template: &rds_instance_table_template
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "~"
  output: "json"
  exclude: *exclude
  content_start: ["DBInstances",0]
  #content_start should be ["DBClusterEndpoints",0] for --db-cluster-identifier command [Aurora cluster command]

text_template: &text_template
  data_type: text
  css_class: p
  exclude: *exclude

#PMM2 Graph - Will Group the all charts each row will contain 4 graphs

graph_template_unit: &graph_template_unit
  data_type: text
  css_class: unite text_center
  exclude: *exclude
  graph_settings:
    width: 300
    height: 125
    refresh: 1m
    orgId: 1

graph_template: &graph_template
  data_type: text
  css_class: separate
  exclude: *exclude
  graph_settings:
    width: 1000
    height: 500
    refresh: 1m
    orgId: 1

qan_analysis_template: &qan_analysis_template
  data_type: htov_table
  css_class: table header_green_text htov_table
  delimiter: "||||"
  exclude: *excludeqan
  data_separator: "Query   -   "

sum_sql_table_template: &sum_sql_table_template
  data_type: table
  css_class: table header_green_text sum
  delimiter: "\t"
  exclude: *exclude

table_template_super_privileges: &table_template_super_privileges
  data_type: table
  css_class: table header_green_text
  delimiter: "\t"
  exclude: *exclude_super_privileges

sql_table_template: &sql_table_template
  data_type: table
  css_class: table header_green_text
  delimiter: "\t"
  exclude: *exclude

ticket_sql_table_template: &ticket_sql_table_template
  data_type: table
  css_class: table header_green_text break_before_table
  delimiter: "\t"
  exclude: *exclude
  href: 
    column: "Ticket"
    url: "https://tickets.mydbops.com/a/tickets/"




### CHART SETTINGS
chart_settings_cs_count_by_host : &chart_settings_cs_count_by_host
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "host"
    x_axis_ticks: "category"
    y_axis: "alert count"
    x_showticklabels: no
    title: ""
    info: "label+value"
    legend: true
    legend_orientation: "h"
    hole: 0.4
    img_width: 1200
    img_height: 650
    x_title: "Host"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_count_by_service : &chart_settings_count_by_service
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "service"
    x_axis_ticks: "category"
    y_axis: "alert count"
    x_showticklabels: no
    title: ""
    legend: true
    legend_title: "Services"
    legend_orientation: "h"
    img_width: 1200
    img_height: 650
    x_title: "Service"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_alert_daywise : &chart_settings_alert_daywise
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "date"
    x_axis_ticks: "D1"
    y_axis: "alert count"
    title: ""
    legend: false
    # legend_title: "Daywise Alerts"
    # legend_orientation: "h"
    img_width: 1200
    img_height: 650
    x_title: "Date"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_ticket : &chart_settings_ticket
  - chart_type: "StackedBar"
    x_axis: "Created"
    x_axis_ticks: "D1"
    y_axis: "Status"
    stacked: "Ticket"
    title: ""
    legend: true
    x_title: "Created Date"
    y_title: "Ticket Count by Status"
    img_width: 900
    img_height: 500
    style: 
      width: "100%"
  - chart_type: "Pie"
    group_by: "Status"
    value: "Ticket"
    title: ""
    info: "label+value"
    legend: true
    legend_title: "Status"
    img_height: 500
    style: 
      width: "83%"
      padding-top: 15px;
  
chart_settings_new_table : &chart_settings_new_table
  - chart_type: "Bar"
    group_by: "Created On"
    group_by_type: "datetime"
    x_axis_ticks: "category"
    x_axis_ticks_angle: -90
    value: "Table"
    title: ""
    x_title: "Created Date"
    y_title: "Table Count"
    style: 
      width: "80%"

chart_settings_active_writes : &chart_settings_active_writes
  - chart_type: "Pie"
    name: "Table"
    value: "Writes"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Table"
    img_height: 500
    style: 
      width: "80%"

chart_settings_active_reads : &chart_settings_active_reads
  - chart_type: "Pie"
    name: "Table"
    value: "Reads"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Table"
    img_height: 500
    style: 
      width: "80%"

chart_settings_data_distribution : &chart_settings_data_distribution
  - chart_type: "Pie"
    name: "Database"
    value: "Size"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Database Name"
    img_height: 500
    style: 
      width: "80%"

#DEFAULT TEMPLATE FOR STATISTICS
critical_stats_template: *ticket_sql_table_template
ticket_stats_template: *ticket_sql_table_template
server_stats_template: *mongo_template
graph_stats_template: *graph_template
qan_stats_template: *qan_analysis_template

stats_order:
  ticket_stats: 
  - report_name: Tickets Handled
    query_file: TICKET_HANDLED.sql
    data_base: reports
    description:
    active: yes
    chart_settings: *chart_settings_ticket
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "created_On"
        replace_header_text: "Created"
  critical_stats:
  - report_name: Alert Count by Host Name
    description:
    query_file: TICKET_alert_by_host.sql
    data_base: whitewalker
    show_table: no
    active: yes
    chart_settings: *chart_settings_cs_count_by_host
  - report_name: Alert Count for Top Services
    description:
    query_file: TICKET_alert_top_services.sql
    data_base: whitewalker
    show_table: no
    active: yes
    chart_settings: *chart_settings_count_by_service
  - report_name: Daywise Alert Count
    description:
    query_file: TICKET_alert_daywise.sql
    data_base: whitewalker
    show_table: no
    active: yes
    chart_settings: *chart_settings_alert_daywise
  
  server_stats:
  - report_name: Server Information
    command: bash /usr/local/mydbops/healthReport/MydbHealthReport.sh -u {{my_user}} -p {{my_pass}} -a {{my_db}} -m "{{mongo_clusters}}" 2>>/usr/local/mydbops/healthReport/log/error.log
    # command: cat logs/test.json
    active: yes
    description:
    ssh_name_filter:
    store: mongo_stats
    modify_template: yes
    extract_info: 
      mongoCustom:
        report_name: mongoCustom
        active: no
        description:
        ssh_name_filter:
        design_template: *mongoCustom
  - report_name: Rating
    command:
    active: yes
    description:
    ssh_name_filter:
    get_from_store: mongo_stats
    design_template:
    show_table: no
    extract_info: 
      mongo_health_rating:
        report_name: Mongo Health Rating
        active: yes
        description: As per the mydbops Mongodb team standards, we always recommend maintaining servers at a healthy state above 90%. To improve server health, please verify the above recommendations.
        ssh_name_filter:
        show_table: no
        design_template: *mongo_health_rating
        chart_settings: *chart_settings_rating
  - report_name: DataBase Statistics
    command:
    active: yes
    description:
    ssh_name_filter:
    get_from_store: mongo_stats
    extract_info: 
      data_distribution:
        report_name: Data Distribution
        active: yes
        description: '<strong>FYI:</strong> We have added the abbreviations for the each filed.<br><br><strong>collCnt:</strong> Number of collections present under the each database.<br><strong>idxCnt:</strong> Number of indexes present under the each database.<br><strong>avgDocSize:</strong>The average document size of each DB.<br><strong>strSize:</strong> Storage Size, Which indicates the compressed size of the database.'
        ssh_name_filter:
        design_template: *data_distribution
        bottom_description: yes
  - report_name: Considerable Collections
    command:
    active: yes
    description:
    ssh_name_filter:
    get_from_store: mongo_stats
    extract_info: 
      top_collection:
        report_name: Top Collections
        active: yes
        description: Below we are sharing the top 5 collections as per the collection compressed size in the db server. <br><br>If any collection containing <strong>archivalRecommendation</strong> is true, The mydbops team will approach you with a better suitable archival strategy.
        ssh_name_filter:
        design_template: *top_collection
      empty_collection:
        report_name: Empty Collections
        active: yes
        description: We have noticed below mentioned empty collections in the server. We recommend removing it from the server.
        ssh_name_filter:
        design_template: *empty_collection
  - report_name: Counters
    command:
    active: yes
    description:
    ssh_name_filter:
    get_from_store: mongo_stats
    extract_info: 
      op_counters:
        report_name: OP Counters
        active: yes
        description: The below mentioned stats are based on the uptime of the mongo.<br>we are always recommending distributing all asynchronous read (find, count and aggregate) requests to the secondaries. This will relieve the load on the primary server and effectively utilize the secondary nodes.
        ssh_name_filter:
        design_template: *op_counters
        chart_settings: *chart_settings_op_counters
        chart_after_tables: yes
  - report_name: Users
    command:
    active: yes
    description:
    ssh_name_filter:
    get_from_store: mongo_stats
    extract_info: 
      root_users:
        report_name: Root Users
        active: yes
        description: We have shared the below mentioned users with root privileges, Kindly validate from your end also. 
        ssh_name_filter:
        design_template: *root_users
      drop_users:
        report_name: Users With Removed Privileges
        active: yes
        description: We have noticed the below-mentioned users having the removed privileges. Kindly validate from your end.
        ssh_name_filter:
        design_template: *drop_users
      dummy_users:
        report_name: Dummy Users
        active: yes
        description: We have noticed below-mentioned users do not hold any roles. We recommend removing dummy users from the server.
        ssh_name_filter:
        design_template: *dummy_users
  - report_name: Indexes
    command:
    active: yes
    description: In mongodb, index plays a major role while accessing  the data. <br>But the indexes will occupy the wiredTiger cache and some storage in the disk. In some cases, the collections reaches max indexes limit (64). <br><br>So we are suggesting to drop the duplicate indexes and unused indexes to improve the query performance and maintain the host server configured resources in an effective manner. 
    ssh_name_filter:
    get_from_store: mongo_stats
    extract_info: 
      duplicate_indexes:
        report_name: Duplicate Indexes
        active: yes
        description: '<strong>FYI:</strong> We have added the abbreviations for the each filed.<br><br><strong>DBStrSize:</strong> Database storage Size<br><strong>CollStSize:</strong>  Collection storage Size<br><strong>IdxCnt:</strong> Index count in that respective collection<br><strong>DpIdx</strong>: Duplicate index<br><strong>AlIdxCnt:</strong> Alternate indexes count for the duplicate index.<br><strong>AlIdx:</strong> Alternate index for the duplicate index.'
        ssh_name_filter:
        design_template: *duplicate_indexes
        bottom_description: yes
      unused_indexes:
        report_name: Top Mongo Unused Indexes
        active: yes
        description:
        ssh_name_filter:
        design_template: *unused_indexes
      ttl_indexes:
        report_name: TTL Indexes
        active: yes
        description: 'We need to focus on the TTL indexes. <ul><li> TTL indexes are special single-field indexes that MongoDB can use to automatically remove documents from a collection after a certain amount of time or at a specific clock time.</li> <li>Data expiration is useful for certain types of information like machine-generated event data, logs, and session information that only need to persist in a database for a finite amount of time.</li> <li> If you are removing documents to save on storage costs.</li> <li> Note# For archival purposes, TTL indexes were recommendable only. But for regular intervals, we need to validate the data which is properly existing in the server else we can increase the clock time ( expireAfterSeconds ).</li> </ul><br>'
        ssh_name_filter:
        design_template: *ttl_indexes

  graph_stats1:
  - report_name: System Usage Graphs
    description: 
    description_after_chart: yes
    active: yes
    individual_graph_name: yes
    ssh_name_filter:
    graphs:
      - p1004
      - p1008
      - p1010
      - p1012
      - p1006
  - report_name: Mongo Metrics
    description: 
    description_after_chart: yes
    active: yes
    individual_graph_name: no
    # design_template: *graph_template_unit
    ssh_name_filter:
    graphs:
      - p1018
      - p1028
      - p1036
      - p1026
      - p1032
      - p1034
      - p1024
      - p1022
      - p1020

  qan_analysis1:
  - report_name: Top Slow Queries By Execution Count
    description:
    query_file: by_count.sql
    data_base:
    active: yes
  - report_name: Top Slow Queries By Execution Time
    description:
    query_file: by_execution_time.sql
    data_base:
    active: yes
  - report_name: Top Slow Queries By Disk Temp Table
    description:
    query_file: by_disk_tmp_table.sql
    data_base:
    active: yes
  - report_name: Top Slow Queries By Full Table Scan
    description:
    query_file: by_full_tbl_scan.sql
    data_base:
    active: yes

#For ticket and critical stats we can only add description at top.
description:
  server_stats:
    top:
    bottom:
  sql_stats:
    top:
    bottom:
  graph_stats:
    top: 1. The below PMM graphs are the metrics collected every 5 minutes only. So FYI, these graphs will project the server health with 80 % accuracy for an hour.<br>2. If we notice any abnormalities, Our Mydbops MongoDB team will analyze the issue accordingly and give a proper resolution for the same.<br>3. After checking the report If you have any concerns, kindly update the mydbops Mongodb team and discuss accordingly.
    bottom:
  qan_analysis: 
    top: The query analytics details are gathered from the performance_schema in MySQL, The max length of the sample digest query that can be stored is limited to 1024 bytes. Given this limitation, The query sample shown in the report can be a truncated query, In the event of analysing the truncated query, Kindly co-relate the digest pattern with the actual query in the application tier.
    bottom:
  final_note1:
    title: 'IMP Note :'
    css_class: description_box
    description: If we already have a ticket for any of the above recommendations, please ignore.