#you can exclude the outputs generated by commands or query
exclude: &exclude
  starts_with: ["mysql:","__","--","==","     SELinux","  Locator","  ==="]
  contains: ["ERROR 1054 ","ERROR 1356 "]

exclude_super_privileges: &exclude_super_privileges
  starts_with: ["mysql:","__","--","==","     SELinux","  Locator","  ===","mysql.session"]

exclude_table_create: &exclude_table_create
  starts_with: ["ERROR:"]

excludeqan: &excludeqan
  starts_with: ["SHOW","SET","SELECT @@","BEGIN","USE"]
  contains: ["INFORMATION_SCHEMA","information_schema","MYSQL.","mysql.","`mysql`","PERFORMANCE_SCHEMA","performance_schema","SYS.","sys.","`sys`","pfs.","`pfs`","PFS.","start transaction","Commit","rollback","SELECT @@","||||SHOW","||||SET","||||BEGIN","||||USE"]

exclude_rds_json: &exclude_rds_json
  starts_with: ["DBSubnetGroup"]

# can filter output for server informations
content_start: &content_start "# "

content_end: &content_end "# "

bytes_to_human_readable: &bytes_to_human_readable "out_put = bytes_to_readable('in_put')"

rds_instance_table_template: &rds_instance_table_template
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "~"
  output: "json"
  exclude: *exclude_rds_json
  content_start: ["DBInstances",0]
  #content_start should be ["DBClusterEndpoints",0] for --db-cluster-identifier command [Aurora cluster command]

text_template: &text_template
  data_type: text
  css_class: p
  exclude: *exclude

#PMM2 Graph - Will Group the all charts each row will contain 4 graphs

graph_template_unit: &graph_template_unit
  data_type: text
  css_class: unite text_center
  exclude: *exclude
  graph_settings:
    width: 300
    height: 125
    refresh: 1m
    orgId: 1

graph_template: &graph_template
  data_type: text
  css_class: separate
  exclude: *exclude
  graph_settings:
    width: 1000
    height: 500
    refresh: 1m
    orgId: 1

qan_analysis_template: &qan_analysis_template
  data_type: htov_table
  css_class: table header_green_text htov_table
  delimiter: "||||"
  exclude: *excludeqan
  data_separator: "Query   -   "

sum_sql_table_template: &sum_sql_table_template
  data_type: table
  css_class: table header_green_text sum
  delimiter: "\t"
  exclude: *exclude

table_template_super_privileges: &table_template_super_privileges
  data_type: table
  css_class: table header_green_text
  delimiter: "\t"
  exclude: *exclude_super_privileges

sql_table_template: &sql_table_template
  data_type: table
  css_class: table header_green_text
  delimiter: "\t"
  exclude: *exclude

ticket_sql_table_template: &ticket_sql_table_template
  data_type: table
  css_class: table header_green_text break_before_table
  delimiter: "\t"
  exclude: *exclude
  href: 
    column: "Ticket"
    url: "https://tickets.mydbops.com/a/tickets/"

percona_table_template: &percona_table_template
  data_type: table
  css_class: table
  delimiter: "|"
  exclude: *exclude
  content_start: *content_start
  content_end: *content_end

percona_system_template: &percona_system_template
  data_type: vertical_table
  css_class: table header_green_text 
  delimiter: "|"
  exclude: *exclude
  content_start: "# Percona "
  content_end: *content_end

percona_cpu_template: &percona_cpu_template
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude
  content_start: "# Processor "
  content_end: *content_end

percona_memory_template: &percona_memory_template
  data_type: vertical_table
  css_class: table header_green_text
  delimiter: "|"
  exclude: *exclude
  content_start: "# Memory "
  content_end: *content_end
 
percona_network_template: &percona_network_template
  data_type: table
  css_class: table header_green_text 
  delimiter: "|"
  exclude: *exclude
  content_start: "# Network Connections"
  content_end: *content_end

### CHART SETTINGS
chart_settings_cs_count_by_host : &chart_settings_cs_count_by_host
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "host"
    x_axis_ticks: "category"
    y_axis: "alert count"
    x_showticklabels: no
    title: ""
    info: "label+value"
    legend: true
    legend_orientation: "h"
    hole: 0.4
    img_width: 1200
    img_height: 650
    x_title: "Host"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_count_by_service : &chart_settings_count_by_service
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "service"
    x_axis_ticks: "category"
    y_axis: "alert count"
    x_showticklabels: no
    title: ""
    legend: true
    legend_title: "Services"
    legend_orientation: "h"
    img_width: 1200
    img_height: 650
    x_title: "Service"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_alert_daywise : &chart_settings_alert_daywise
  - chart_type: "Bar"
    color_bar: yes
    x_axis: "date"
    x_axis_ticks: "D1"
    y_axis: "alert count"
    title: ""
    legend: false
    # legend_title: "Daywise Alerts"
    # legend_orientation: "h"
    img_width: 1200
    img_height: 650
    x_title: "Date"
    y_title: "Count"
    style: 
      width: "95%"

chart_settings_ticket : &chart_settings_ticket
  - chart_type: "StackedBar"
    x_axis: "Created"
    x_axis_ticks: "D1"
    y_axis: "Status"
    stacked: "Ticket"
    title: ""
    legend: true
    x_title: "Created Date"
    y_title: "Ticket Count by Status"
    img_width: 900
    img_height: 500
    style: 
      width: "100%"
  - chart_type: "Pie"
    group_by: "Status"
    value: "Ticket"
    title: ""
    info: "label+value"
    legend: true
    legend_title: "Status"
    img_height: 500
    style: 
      width: "83%"
      padding-top: 15px;
  
chart_settings_new_table : &chart_settings_new_table
  - chart_type: "Bar"
    group_by: "Created On"
    group_by_type: "datetime"
    x_axis_ticks: "category"
    x_axis_ticks_angle: -90
    value: "Table"
    title: ""
    x_title: "Created Date"
    y_title: "Table Count"
    style: 
      width: "80%"

chart_settings_active_writes : &chart_settings_active_writes
  - chart_type: "Pie"
    name: "Table"
    value: "Writes"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Table"
    img_height: 500
    style: 
      width: "80%"

chart_settings_active_reads : &chart_settings_active_reads
  - chart_type: "Pie"
    name: "Table"
    value: "Reads"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Table"
    img_height: 500
    style: 
      width: "80%"

chart_settings_data_distribution : &chart_settings_data_distribution
  - chart_type: "Pie"
    name: "Database"
    value: "Size"
    title: ""
    info: "label+percent"
    legend: no
    legend_title: "Database Name"
    img_height: 500
    style: 
      width: "80%"

#DEFAULT TEMPLATE FOR STATISTICS
critical_stats_template: *ticket_sql_table_template
ticket_stats_template: *ticket_sql_table_template
server_stats_template: *percona_table_template
sql_stats_template: *sql_table_template
graph_stats_template: *graph_template
qan_stats_template: *qan_analysis_template

stats_order:
  ticket_stats: 
  - report_name: Tickets Handled
    query_file: TICKET_HANDLED.sql
    data_base: reports
    description:
    active: yes
    chart_settings: *chart_settings_ticket
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "created_On"
        replace_header_text: "Created"
  critical_stats:
  - report_name: Alert Count by Host Name
    description:
    query_file: TICKET_alert_by_host.sql
    data_base: whitewalker
    show_table: no
    active: yes
    chart_settings: *chart_settings_cs_count_by_host
  - report_name: Alert Count for Top Services
    description:
    query_file: TICKET_alert_top_services.sql
    data_base: whitewalker
    show_table: no
    active: yes
    chart_settings: *chart_settings_count_by_service
  - report_name: Daywise Alert Count
    description:
    query_file: TICKET_alert_daywise.sql
    data_base: whitewalker
    show_table: no
    active: yes
    chart_settings: *chart_settings_alert_daywise
  server_stats: 
  - report_name: RDS Instance Statistics
    #In command we can add new parameters like "region=ap-south-1" and --db-instance-identifier or --db-cluster-identifier should define in last parameter in command
    command: ["aws", "rds", "describe-db-instances", "--output=json","--db-instance-identifier"]
    active: no
    description:
    design_template: *rds_instance_table_template
    ssh_name_filter:
    extract_info: 
      rds_instance_system_summary:
        report_name: System Information
        active: yes
        description: 
        design_template: *rds_instance_table_template

  - report_name: Server Information
    command: ["pt-summary"]
    active: yes
    description:
    ssh_name_filter:
    extract_info: 
      percona_toolkit_system_summary:
        report_name: System Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_system_template
        script:
          - text_match_header: "Uptime"
            replace_header_text: ""
            py_code: "up_time = 'in_put'\nut_ls = up_time.split(',')\nif len(ut_ls)>2:\n    out_put = (ut_ls[0]+', '+ut_ls[1])"
      processor:
        report_name: CPU Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_cpu_template
        script:
          - text_match_header: "Speeds"
            replace_header_text: "CPU Speed"
            py_code: "cpu_speed = 'in_put'.split('x')\nif len(cpu_speed)==2:\n    out_put = cpu_speed[0]+'x'+str(round(float(cpu_speed[1])/1000,2))+' GHz'"
      memory:
        report_name: Memory Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_memory_template
      network_connections:
        report_name: Network Information
        active: yes
        description:
        ssh_name_filter:
        design_template: *percona_network_template
  sql_stats:
  - report_name: New Tables Created Summary
    description:
    query_file: new_table_summary.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
  - report_name: New Tables Created
    description: The tables rebuilt by pt-online-schema change and its foreign key table can be listed under new tables as they are recreated logically. Also table rebuild for fragmentation will be listed under newly created tables.
    query_file: new_table.sql
    data_base:
    active: yes
    chart_settings: *chart_settings_new_table
    ssh_name_filter: ["source"]
    mysql_filter: "max_tables_create_limit"
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
  - report_name: Data Distribution
    description:
    query_file: data_dist.sql
    data_base:
    active: yes
    show_table: yes
    design_template: *sum_sql_table_template
    chart_settings: *chart_settings_data_distribution
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
      - text_match_header: "Database"
        remove_from_chart: "TOTAL"
  - report_name: Top 10 Fragmented Tables
    description: The below are top 10 Fragmented tables. The Fragmented tables will hold space while we delete some records from table. In order to recover the deleted space we need to rebuild the below list of tables.Highly fragemnted tables will affect the DB performance.
    query_file: fragment.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "Current Size"
        py_code: *bytes_to_human_readable
      - text_match_header: "Fragmentation"
        py_code: *bytes_to_human_readable
  - report_name: Auto Increment Usage Analytics
    description: 
    query_file: ai_usage.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
  - report_name: Tables Without Primary / Unique Key
    description: The primary keys are necessary to the database for the faster look-ups and some percona tools ( pt-table-checksum ) uses the primary key as its base for its functioning. The below are list of tables without any primary or unique keys in the respective databases. It will affect the DB performance by going to full table scan.
    query_file: with_out_key.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
  - report_name: Top 10 tables Ordered by Size
    description: 
    query_file: bigger_tables.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
  - report_name: Non-InnoDB Tables
    description: 
    query_file: non_inno_db.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
  - report_name: Users Without Password
    description: User level security has to be implemented at DB to restrict the level of access each user has, based on the hostname and need of these user from the application perspective, below are the things that should be avoided at most cases with regards to DB.<br><br>Every user should have password, user's without password should be avoided from the DB, To further improve mysql 5.6 comes with password validation plugin which defines the minimum length and policy to create the user. With introduction of 5.7 which is further more extended with password expiration.Above are the list of users violating all the above said by having a empty password, Please avoid this users at any cost.
    query_file: users_without_password_old.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
  - report_name: Users Without Password
    description: User level security has to be implemented at DB to restrict the level of access each user has, based on the hostname and need of these user from the application perspective, below are the things that should be avoided at most cases with regards to DB.<br><br>Every user should have password, user's without password should be avoided from the DB, To further improve mysql 5.6 comes with password validation plugin which defines the minimum length and policy to create the user. With introduction of 5.7 which is further more extended with password expiration.Above are the list of users violating all the above said by having a empty password, Please avoid this users at any cost.
    query_file: users_without_password5.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
  - report_name: Open Host Users
    description: In MySQL user authentication happens at host-level, we can restrict users based on IP or Hostname, we would highly recommend to restrict users only to application server or within local secure network,<br>Users with open-host '%' , have the right to access DB from any host. This paves the ways of easy compromise of security, above are list of users with open-host.
    query_file: open_host_users.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
  - report_name: Super Privilege Users
    description: SUPER/admin privileges has to be restricted to a limited users (trusted names and host ) and applications users should never have SUPER privileges.
    query_file: super_privilege_users.sql
    data_base:
    active: yes
    design_template: *table_template_super_privileges
    ssh_name_filter: ["source"]
  - report_name: Locked accounts
    description: 'Locked accounts, Will not be able to login the MySQL server. When tried connecting they will receive the error ""Account is locked"".The procedures, functions, events, triggers created by this user as definer will still continue to work.If this user is no longer required, User can be dropped post the validation that user is not defined as definer on procedures, functions, events, triggers.'
    query_file: locked_accounts.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
  - report_name: Expired Passwords
    description: Password for a user account can be manually set as expired or expired automatically by setting password_lifetime (password validity in days) on user attributes.When the password is expired, MySQL will allow to login to the server, You cannot execute any command before changing the password. Following message will be displayed.'You must reset your password using ALTER USER statement before executing this statement.'
    query_file: expired_passwords.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
  - report_name: Metered User Connections
    description: These user are having user level connection limits, This can override the global configuration like max_connections, Exhaustion of these limits will cause connection / query to fail.Unless it's set intentionally, We recommend to remove this limits.
    query_file: metered_user_connections.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
  - report_name: Unused Index
    description: 
    query_file: unused_index.sql
    data_base:
    active: yes
    mysql_filter: "min_uptime_unused_index"
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
  - report_name: Duplicate Index
    description:
    query_file: duplicate_index.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
    script:
      - text_match_header: "Size"
        py_code: *bytes_to_human_readable
  - report_name: Active Tables By Write Operation
    description:
    query_file: active_writes.sql
    data_base:
    active: yes
    ssh_name_filter: ["source"]
    chart_settings: *chart_settings_active_writes
  - report_name: Active Tables By Read Operation
    description:
    query_file: active_reads.sql
    data_base:
    active: yes
    chart_settings: *chart_settings_active_reads

  graph_stats1:
  - report_name: System Information
    description: 
    active: yes
    individual_graph_name: no
    design_template: *graph_template_unit
    ssh_name_filter:
    graphs:
      - p101
      - p102
      - p103
      - p104
  - report_name: System Usage Graphs
    description: 
    active: yes
    individual_graph_name: yes
    ssh_name_filter:
    graphs:
      - p105
      - p106
      - p107
      - p109
      - p309
      - p111
  - report_name: MySQL Statistics
    description: 
    active: yes
    individual_graph_name: no
    design_template: *graph_template_unit
    ssh_name_filter:
    graphs:
      - p201
      - p202
      - p203
  - report_name: MySQL Critical Statistics
    description: 
    active: yes
    individual_graph_name: yes
    ssh_name_filter:
    graphs:
      - p307
      - p311
      - p315
      - p317: "chage_from_dt_to_previous_day"
      - p204
  - report_name: MySQL HA Statistics
    description: 
    active: yes
    individual_graph_name: yes
    ssh_name_filter: ["replica" , "slave"]
    graphs:
      - p319

  qan_analysis1: 
  - report_name: Top Queries based on Execution time
    description:
    active: yes
    ssh_name_filter:
    query: 
      columns:
      - field: num_queries
        title: Hit Count
        path: num_queries
      - field: query_time
        title: Time Taken (Avg)
        path: metrics.query_time.stats.avg
        formula: X
      - field: fingerprint
        title: Query
        path: fingerprint
      group_by: "queryid"
      order_by: "-query_time"
      main_metric: "query_time"
      limit: 5
      offset: 0
      search: ""
      keyword: ""
      include_only_fields: []
      filters:
      - key: database
        exclude: ["postgres","pmm-managed"]
      - key: service_type
        exclude: ["postgresql"]
      - key: node_name
        exclude: []
      - key: schema
        exclude: ["sys","performance_schema","information_schema","mysql"]
        include: []
        limit: 2
  
  - report_name: Top Queries based on Count of Execution
    description:
    active: yes
    ssh_name_filter:
    query: 
      columns:
      - field: num_queries
        title: Hit Count
        path: num_queries
      - field: query_time
        title: Time Taken (Avg)
        path: metrics.query_time.stats.avg
        formula: X
      - field: fingerprint
        title: Query
        path: fingerprint
      group_by: "queryid"
      order_by: "-num_queries"
      main_metric: "num_queries"
      limit: 5
      offset: 0
      search: ""
      keyword: ""
      include_only_fields: []
      filters:
      - key: database
        exclude: ["postgres","pmm-managed"]
      - key: service_type
        exclude: ["postgresql"]
      - key: node_name
        exclude: ["pmm-server"]
      - key: schema
        exclude: ["sys","performance_schema","information_schema","mysql"]
        include: []
        limit: 2
  
  - report_name: Top Queries based on Full Table Scan
    description:
    active: yes
    ssh_name_filter:
    query: 
      columns:
      - field: num_queries
        title: Hit Count
        path: num_queries
      - field: query_time
        title: Time Taken (Avg)
        path: metrics.query_time.stats.avg
        formula: X
      - field: fingerprint
        title: Query
        path: fingerprint
      - field: full_scan
        title: Full Scan (Sum/Sec)
        path: metrics.full_scan.stats.sum_per_sec
      group_by: "queryid"
      order_by: "-full_scan"
      main_metric: "num_queries"
      limit: 5
      offset: 0
      search: ""
      keyword: ""
      include_only_fields: []
      filters:
      - key: database
        exclude: ["postgres","pmm-managed"]
      - key: service_type
        exclude: ["postgresql"]
      - key: node_name
        exclude: ["pmm-server"]
      - key: schema
        exclude: ["sys","performance_schema","information_schema","mysql"]
        include: []
        limit: 2
  
  - report_name: Top Queries based on Disk Tmp table
    description:
    active: yes
    ssh_name_filter:
    query: 
      columns:
      - field: num_queries
        title: Hit Count
        path: num_queries
      - field: query_time
        title: Time Taken (Avg)
        path: metrics.query_time.stats.avg
        formula: X
      - field: fingerprint
        title: Query
        path: fingerprint
      - field: tmp_disk_tables
        title: Disk Tmp table (Sum/Sec)
        path: metrics.tmp_disk_tables.stats.sum_per_sec
      group_by: "queryid"
      order_by: "-tmp_disk_tables"
      main_metric: "num_queries"
      limit: 5
      offset: 0
      search: ""
      keyword: ""
      include_only_fields: []
      filters:
      - key: database
        exclude: ["postgres","pmm-managed"]
      - key: service_type
        exclude: ["postgresql"]
      - key: node_name
        exclude: ["pmm-server"]
      - key: schema
        exclude: ["sys","performance_schema","information_schema","mysql"]
        include: []
        limit: 2

#For ticket and critical stats we can only add description at top.
description:
  server_stats:
    top:
    bottom:
  sql_stats:
    top:
    bottom:
  graph_stats:
    top:
    bottom:
  qan_analysis: 
    top: The query analytics details are gathered from the performance_schema in MySQL, The max length of the sample digest query that can be stored is limited to 1024 bytes. Given this limitation, The query sample shown in the report can be a truncated query, In the event of analysing the truncated query, Kindly co-relate the digest pattern with the actual query in the application tier.
    bottom: