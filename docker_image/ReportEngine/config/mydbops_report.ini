
[Settings]
ssh_path= ~/.ssh/config
slow_query= no 
error_log1_removed= yes
error_log_path_removed= logs/error.log
error_log_traceback= yes
info_log= yes
info_log_path=  logs/info.log
inventory_path= config/server_inventory.cnf
inventory_default_group= all

default_connection_type= ssh
default_instance= virtual
client_name = userexperior
client_env_name = userexperior
client_key_server_hash = YUhSMGNEb3ZMMnRsZVhObGNuWmxjaTV0ZVdSaWIzQnpMbU52YlM5blpYUmZaR1YwWVdsc2MxOW1iM0pmYzJWdVpGOXRZV2xzUHc9PQ==
client_key_server_token = 481c12be49cd7e8ae757a44b0459320b
client_email = <EMAIL>
report_store_path = my_reports
report_generate_type= pdf 
report_generate_by= group
email_provider = ses
email_content_template = email_content.email
report_generate_by_delete= group
;email_provider = ses/sendgrid
;report_generate_by_deleteall/group/instance


disable_ticket_stats= no
disable_cloud_upload= no
disable_send_mail= no
event_post= http://**************:4031/events

note_title= 'IMP Note :'
note_css_class= description_box
note_description= If we already have a ticket for any of the above recommendations, please ignore.

[Graph]
p101= System Uptime : 
p102= Virtual CPUs :
p103= RAM :
p104= Memory Available : 

p105= Load Average : The average system load on a Linux server for a defined period of time.
p106= Overall CPU Utilizations : This graph shows the CPU utilization of the servers.
p107= Memory Distribution : It shows the overall memory usage and its split up. In general DB servers are configured to use 90-95% of overall memory.
p109= Disk Operations : It shows the IOPS utilization in number of the DB server.
p309= Disk Bandwidth : Shows volume of reads and writes the storage is handling. This can be a better measure of IO capacity usage for network attached and SSD storage as it is often bandwidth limited.  Amount of data being written to the disk can be used to estimate Flash storage life time.
p111= Network Utilization Hourly : It shows the hourly network usage in a day.

p201= MySQL Uptime :
p202= InnoDB Buffer Pool Size : 
p203= Buffer Pool Size of Total RAM : 

p307= MySQL Connections : Connections is the number of connection attempts (successful or not) to the MySQL server.
p311= MySQL Client Thread Activity : Threads Connected is the number of open connections, while Threads Running is the number of threads not sleeping.
p313= MySQL Questions : The number of statements executed by the server. This includes only statements sent to the server by clients and not statements executed within stored programs, unlike the Queries used in the QPS calculation.<br>This variable does not count the following commands:<br>* ``COM_PING``<br>* ``COM_STATISTICS``<br>* ``COM_STMT_PREPARE``<br>* ``COM_STMT_CLOSE``<br>* ``COM_STMT_RESET``
p315= InnoDB Buffer Pool Dirty Data : 
p317= InnoDB Log File Usage Hourly : InnoDB Log File Usage Hourly - Along with the buffer pool size, `innodb_log_file_size` is the most important setting when we are working with InnoDB. This graph shows how much data was written to InnoDB's redo logs over each hour. When the InnoDB log files are full, InnoDB needs to flush the modified pages from memory to disk.<br><br>The rules of the thumb is to keep one hour of traffic in those logs and let the checkpointing perform its work as smooth as possible. If you don't do this, InnoDB will do synchronous flushing at the worst possible time, ie when you are busiest.<br><br>This graph can help guide you in setting the correct `innodb_log_file_size`.
p204= Top Command Counters : Top Command Counters - The Com_{{xxx}} statement counter variables indicate the number of times each xxx statement has been executed. There is one status variable for each type of statement. For example, Com_delete and Com_update count [``DELETE``](dev.mysql.com/doc/refman/5.7/en/delete.html) and [``UPDATE``](dev.mysql.com/doc/refman/5.7/en/update.html) statements, respectively. Com_delete_multi and Com_update_multi are similar but apply to [``DELETE``](dev.mysql.com/doc/refman/5.7/en/delete.html) and [``UPDATE``](dev.mysql.com/doc/refman/5.7/en/update.html) statements that use multiple-table syntax.
p319= MySQL Replication Lag : Shows the number of seconds the Slave host is delayed in replication applying events compared to when the Master host applied them, denoted by the Seconds_Behind_Master - value, and only applies to a Slave host. The most common reasons for Slaves to lag their Master are:<br>* Network round trip time - High latency links will lead to non-zero replication lag values<br>* Single threaded nature of replication channels - Master servers have the advantage of applying changes in parallel, whereas Slaves are only able to apply changes in serial, thus limiting their throughput.  In some cases Group Commit can help but is not always applicable.<br>* High number of changed rows or computationally expensive SQL - Depending on the replication format (ROW vs STATEMENT), significant changes to the database through high volume of rows modified, or expensive CPU will all contribute to Slaves lagging behind their Master<br>Generally adding more CPU or Disk resources can alleviate replication lag issues, up to a point.

p68= Top 5 CPU Usage :
p66= Top 5 Used Memory :
p70= Memory Usage :
p72= Top 5 Network Traffic :
p74= Top 5 PostgreSQL Connections :
p76= Top 5 Commit Transactions :
p78= Top 5 Rollbacks Transactions :
p80= Top 5 Locks :
p82= Top 5 Read Operations with Blocks :
p84= Top 5 Write Operations with Blocks :


p1004= Overall CPU Utilization : The graph depicted above showcases the utilization of CPU over a certain period. <br>This information can be useful in recognizing tasks that consume a significant amount of CPU and determining if the CPU is limiting the systems performance. <br>Numerous factors can influence CPU performance, including multiple processes running, Backups and Slow Queries in MongoDB, among others.
p1008= Disk Mount Points : The displayed graphs indicate the percentage of disk space being used for each defined mount point in the system. <br>When a mount point reaches close to 100% capacity, it increases the risk of a "disk full" error, which can result in service failure or system crashes. <br>To prevent this, it is recommended to eliminate any unused files or expand the allocated space for the affected mount point if it is approaching its maximum capacity.
p1010= Disk IOPS : The Grafana Disk IOPS graph displays the Input/Output Operations Per Second (IOPS) metric for a specified disk or disks, allowing users to monitor disk performance and identify potential issues. <br>The graph typically shows IOPS values over time, allowing users to view trends and patterns in disk activity. <br>Frequent abrupt increases in the number of I/O operations being processed are indicative of performance problems arising from an overburdened I/O subsystem.
p1012= Disk IO Utilization : The Disk IO Utilization metric provides insights into the extent to which Input/Output (IO) operations are consuming the disk resources, allowing users to monitor the health and performance of their disks. <br>This metric is typically displayed as a percentage value that represents the ratio of time the disk spends performing IO operations to the time it is idle. <br>A high IO utilization percentage may indicate that the disk is approaching its maximum capacity and may require optimization or additional resources to ensure optimal performance. <br>A high IO utilization percentage suggests that the disk is processing a high volume of IO operations, which can result in a high IO queue depth. Therefore, monitoring both metrics can provide a comprehensive understanding of the health and performance of the IO subsystem, and help identify potential issues before they cause significant problems.
p1006= Memory : The above graph is used to monitor the memory usage of a MongoDB instance, which is crucial for ensuring optimal performance. <br>MongoDB stores data in memory to enhance query performance and reduce disk I/O. Monitoring memory usage can help identify potential issues such as low memory or memory leaks that may affect the overall performance of the system. <br>By monitoring the memory usage of both the system and the MongoDB instance, users can gain a comprehensive understanding of the systems health and performance and take necessary steps to optimize it. <br>The Grafana Memory graph provides an easy-to-understand visualization of memory usage, allowing users to quickly identify trends, patterns, and potential issues. <br>MongoDB usually uses around 50% of memory to cache frequently accessed data for better query performance. However, inefficient indexing or queries with $or operator can cause memory consumption to rise aggressively. Monitoring memory usage and optimizing indexes and queries can help maintain optimal system performance.

p1018= Connections : The MongoDB Connection Grafana graph displays the number of connections made to a MongoDB instance over time. <br>Monitoring the connection graph is crucial for ensuring optimal system performance as a high number of connections can lead to resource exhaustion and slow down the system. <br>The connection graph provides a visual representation of the connection activity, allowing users to identify trends and patterns that may impact the systems performance. <br>By monitoring the connection graph, users can identify potential issues and take necessary steps to optimize the systems performance, such as increasing connection limits or optimizing queries.
p1028= Opcounters : The PMM graph above demonstrates the rate of database operations, including inserts, updates, deletes, and returns, being performed on a MongoDB instance. <br>This graph provides valuable insights into the usage patterns of the system, allowing users to monitor the performance and identify potential issues. <br>By monitoring the operation per second graph, users can identify queries or operations that are consuming excessive system resources and optimize them to ensure optimal system performance. <br>Additionally, users can use this graph to track the growth and usage of their databases over time and make informed decisions regarding scaling and resource allocation.
p1036= MongoDB Oplog : The MongoDB Oplog Grafana graph displays the rate of write operations being performed on a MongoDB instances replication oplog over time. <br>Monitoring the Oplog graph is crucial for ensuring data consistency and replication performance in a MongoDB cluster. <br>The graph provides insights into the rate of write operations being replicated and can help users identify potential bottlenecks and performance issues. <br>By monitoring the Oplog graph, users can ensure that the replication process is keeping up with the write operations and that the secondary nodes in the cluster are up-to-date. Additionally, users can use the Oplog graph to estimate the time required for a secondary node to catch up with the primary node in the event of a failure or maintenance downtime.
p1026= Query Efficiency : The MongoDB Query Efficiency Grafana graph displays the query execution time and rate over time, providing insights into the systems query performance. <br>Monitoring the Query Efficiency graph is essential for identifying slow queries and bottlenecks that may impact the systems performance. <br>By monitoring this graph, users can identify inefficient queries, optimize indexes, and make informed decisions regarding system resource allocation. <br>The Query Efficiency graph allows users to track the performance of queries over time, providing insights into usage patterns and identifying potential issues before they impact system performance. <br>Additionally, users can use this graph to measure the effectiveness of their optimization efforts and track improvements in query performance over time.
p1032= Scanned and Moved Obj : The MongoDB Scanned Objects and Scanned/Returned Graphs in Grafana display the number of objects scanned and returned by queries over time, providing insights into the systems query performance. <br>Monitoring these graphs is essential for identifying slow queries and bottlenecks that may impact the systems performance. <br>By monitoring the Scanned Objects and Scanned/Returned Graphs, users can identify inefficient queries, optimize indexes, and make informed decisions regarding system resource allocation. <br>The Scanned Objects Graph displays the number of objects scanned by queries over time, while the Scanned/Returned Graph displays the ratio of objects scanned to objects returned. <br>Both graphs provide valuable insights into the efficiency of queries and can help users optimize their queries for optimal performance. <br>Additionally, users can use these graphs to track improvements in query performance over time and make informed decisions regarding system resource allocation.
p1034= WiredTiger Concurrency Tickets Available : The MongoDB WiredTiger Tickets Grafana graph displays the number of tickets acquired and the total number of tickets available over time, providing insights into the WiredTiger storage engines resource utilization. <br>The WiredTiger storage engine in MongoDB uses tickets to manage access to various resources, such as cache and file handles, to ensure efficient resource allocation. <br>Monitoring the WiredTiger Tickets graph is essential for identifying resource utilization patterns and bottlenecks that may impact system performance. <br>By monitoring this graph, users can optimize resource allocation and ensure that the system is efficiently utilizing available resources. <br>The WiredTiger Tickets graph allows users to track the usage of individual resources and identify potential issues before they impact system performance.
p1024= Queued Operations : The MongoDB Queued Operations Grafana graph displays the number of operations waiting to be executed in the MongoDB servers operation queue over time. <br>Monitoring the Queued Operations graph is essential for identifying potential performance issues caused by high traffic loads or long-running queries that may lead to a backlog of operations waiting to be executed. <br>The Queued Operations graph allows users to track the systems performance under heavy load and make informed decisions regarding resource allocation and system scalability.
p1022= Latency : The MongoDB operations latency graph provides a visual representation of the latency experienced by various operations performed on a MongoDB database. <br>The graph displays the average latency for each operation over a specific time period, allowing database administrators to identify performance bottlenecks and optimize their database accordingly. <br>By monitoring the latency graph, administrators can gain insights into the health and efficiency of their database, and take proactive measures to ensure optimal performance and user experience.
p1020= Cursors : The MongoDB Cursor Grafana graph shows the number of active cursors in the system, which is crucial for identifying potential performance bottlenecks caused by inefficient queries or resource contention issues. <br>By monitoring this graph, users can optimize resource allocation and make informed decisions regarding system scalability. <br>The Cursor graph also helps users estimate the systems resource requirements and ensure that the system can handle the number of active cursors efficiently. <br>By default a cursor times out after 10 minutes.
p1024= Queued Operations : The MongoDB Queued Operations Grafana graph displays the number of operations waiting to be executed in the MongoDB servers operation queue over time. <br>Monitoring the Queued Operations graph is essential for identifying potential performance issues caused by high traffic loads or long-running queries that may lead to a backlog of operations waiting to be executed. <br>The Queued Operations graph allows users to track the systems performance under heavy load and make informed decisions regarding resource allocation and system scalability.
p1022= Latency : The MongoDB operations latency graph provides a visual representation of the latency experienced by various operations performed on a MongoDB database. <br>The graph displays the average latency for each operation over a specific time period, allowing database administrators to identify performance bottlenecks and optimize their database accordingly. <br>By monitoring the latency graph, administrators can gain insights into the health and efficiency of their database, and take proactive measures to ensure optimal performance and user experience.
p1020= Cursors : The MongoDB Cursor Grafana graph shows the number of active cursors in the system, which is crucial for identifying potential performance bottlenecks caused by inefficient queries or resource contention issues. <br>By monitoring this graph, users can optimize resource allocation and make informed decisions regarding system scalability. <br>The Cursor graph also helps users estimate the systems resource requirements and ensure that the system can handle the number of active cursors efficiently. <br>By default a cursor times out after 10 minutes.