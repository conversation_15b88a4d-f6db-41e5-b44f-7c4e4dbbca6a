"""
Author : Vetrivel.N
Org : Mydbops
Date : 23-12-2021
"""

from paramiko.config import SSHConfig
from fabric import Connection
# import util.LoadConfig as lc
from constants import constant as my_const
from util import response as resp
import util.Log as log
import json
import invoke

class VirtualInstance:

    config = SSHConfig()
    server = None
    def __init__(self,server):
        self.global_settings = my_const.global_settings
        self.server = server
        
        
    def connect(self):
        try:
            self.config.parse(open(self.global_settings.get("ssh_path")))
            # self.all_ssh_details = self.global_settings.get("ssh_host")
            self.server_ssh_name = self.server.get("ssh_name")
            self.server_ssh_password = self.server.get("ssh_password")
            self.disabled_algorithms = self.server.get("disabled_algorithms")
        except Exception as err:
            return resp.MyResponse("E","exception SSH connection path is not correct")

        #ssh_details = self.all_ssh_details.get(self.server_ssh_name)
        # if ssh_details != None and ssh_details.get("ProxyJump") != None and ssh_details.get("ProxyJump") != "":
        #     self.conn = Connection(self.server_ssh_name, gateway=Connection(ssh_details.get("ProxyJump")))
        # else:
        #     self.conn = Connection(self.server_ssh_name)
        #self.conn = Connection(host="ubuntu@***********",port=2345,) #,connect_kwargs={"password":"ubuntu"}

        self.conn = Connection(host= self.server_ssh_name)
        self.connect_kwargs = {}
        if self.server_ssh_password != None and self.server_ssh_password !="":
            #log.print_log("S","VirtualInstance.connect or login using PASSWORD")
            self.connect_kwargs["password"]= self.server_ssh_password
        if self.disabled_algorithms != None and self.disabled_algorithms !="":
            self.connect_kwargs["disabled_algorithms"]= dict(pubkeys=self.disabled_algorithms)
        if self.connect_kwargs != {}:
            self.conn.connect_kwargs = self.connect_kwargs
 
    def disconnect(self):
        try:
            self.conn.close()
        except Exception as err:
            return resp.MyResponse("E","exception while disconnect SSH connection")
        
    def send_command(self,command,output_format="text"):
        result_list = []
        err = self.connect()
        if err is not None:
            return result_list,err

        try:
            # self.conn.shell = False
            # self.conn.connect_timeout = 2
            output = self.conn.run(command, hide=True)
            log.print_log("S","command going to execute : "+str(command))
            if output.return_code != 0:
                return result_list,resp.MyResponse("E","exception while send_command [ return_code !=0 ] via ssh: "+ output.command)
            else:
                if output.stdout is not None and output.stdout !="":
                    if output_format == "json":
                        result = json.loads(output.stdout)
                    else:
                        result = output.stdout.split("\n")
                    if len(result)>0:
                        result_list = result
        except invoke.exceptions.UnexpectedExit as err:
            if "mydbops_check_postgres" in command and  err.result.stdout != "":
                result = err.result.stdout
                result = result.split("|")
                if len(result)>0:
                    result_list = result
                if result_list is not None and result_list != "":
                    return result_list,None
            else:
                return result_list,resp.MyResponse("E","exception while send_command via ssh: "+str(command),err)    
        except Exception as err:
            return result_list,resp.MyResponse("E","exception while send_command via ssh: "+str(command),err)
        
        err = self.disconnect()
        if err is not None:
            return result_list,err

        return result_list,None
