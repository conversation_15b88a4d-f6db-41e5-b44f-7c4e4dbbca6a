"""
Author : Vetrivel.N
Org : Mydbops
Date : 23-12-2021
"""
import subprocess
import util.Log as log
import json
import os
import signal
from threading import Timer
from util import response as resp
import invoke
import ast

def send_command(dbconfig,command="",output_format="text",timeout=120):
    result_list = []
    try:
        kill = lambda process: process.kill()
        if type(dbconfig) == dict:
            db_input = ['mysql','-h',dbconfig.get("host"),'-u',dbconfig.get("user"),'-p'+dbconfig.get("password"),'-P',str(dbconfig.get("port"))]
            if dbconfig.get("database") != None and dbconfig.get("database") !="":
                db_input.append(dbconfig.get("database"))
            db_input.extend(["-e",command])
        else:
            db_input = dbconfig
            # if type(dbconfig) == str:
            #     db_input = "sh -c '"+dbconfig+"'"
            # else:
            #     db_input = dbconfig
        if type(dbconfig) == str:
            result = subprocess.getoutput(db_input)
            if output_format == "json":
                if type(result) != str:
                    result_list = json.loads(result.decode("utf-8"))
                else:
                    result_list = json.loads(result)
                # result_list = json.loads(result)
            else:
                result_list = result.split("\n")
        else:
            process = subprocess.Popen(db_input,stdout=subprocess.PIPE,stderr=subprocess.STDOUT,stdin=subprocess.PIPE,)
            my_timer = Timer(timeout, kill, [process])
            try:
                try:
                    my_timer.start()
                    log.print_log("S","command going to execute : "+str(db_input))
                    if (process.returncode != None and process.returncode != 0) or process.stderr != None:
                        return result_list,resp.MyResponse("E","Local.send_command error :"+str(process.stderr.read()))
                    else:
                        output = (process.stdout.read())
                        if output is not None :
                            log.print_log("S","output from command execution :"+str(output.decode("utf-8")))
                            if output_format == "json":
                                result = json.loads(output.decode("utf-8"))
                                # result = ast.literal_eval(json.dumps(output.decode("utf-8")))
                            else:
                                result = output.decode("utf-8").split("\n")
                            if len(result) > 0:
                                result_list = result
                finally:
                    my_timer.cancel()

                is_exit = process.wait(timeout =10)
                if is_exit != 0:
                    process.kill()
            except Exception as err_execution:
                try:
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                except:
                    pass
                return result_list,resp.MyResponse("E","exception while send_command via terminal: "+str(dbconfig)+str(command),err_execution)
    except invoke.exceptions.UnexpectedExit as err:
            if "mydbops_check_postgres" in command:
                result = err.result.stdout
                result = result.split("|")
                if len(result)>0:
                    result_list = result
                if result_list is not None and result_list != "":
                    return result_list,None
            else:
                return result_list,resp.MyResponse("E","exception while send_command via ssh: "+str(command),err)
    except Exception as err:
        return result_list,resp.MyResponse("E","exception while send_command via terminal: "+str(dbconfig)+str(command),err)
    return result_list,None
