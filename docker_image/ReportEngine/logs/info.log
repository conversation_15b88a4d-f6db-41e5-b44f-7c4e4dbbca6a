2023-04-28 15:27:16,639 INFO 1682675835223355000tKV Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:27:19,470 INFO 1682675835223355000tKV event url is empty >>> 
2023-04-28 15:27:19,471 INFO 1682675835223355000tKV report generation done , data :{} >>> None
2023-04-28 15:27:19,471 INFO 1682675835223355000tKV {'duration': '0:00:03.462907', 'type': 'Application', 'tag': 'report', 'name': 'mydbhealthreport', 'executionID': '1682675835223355000tKV', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'custom_report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 15:27:15', 'endTime': '2023-04-28 15:27:18', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'custom', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}}, 'version': '2.0.10'} >>> 
2023-04-28 15:29:53,248 INFO 1682675991757901000oci Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:30:00,424 INFO 1682675991757901000oci event url is empty >>> 
2023-04-28 15:30:00,425 INFO 1682675991757901000oci report generation done , data :{} >>> None
2023-04-28 15:30:00,425 INFO 1682675991757901000oci {'duration': '0:00:04.600104', 'type': 'Application', 'tag': 'custom_report', 'name': 'mydbhealthreport', 'executionID': '1682675991757901000oci', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 15:29:51', 'endTime': '2023-04-28 15:29:56', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'custom', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}}, 'version': '2.0.10'} >>> 
2023-04-28 15:30:31,455 INFO 1682676030012624000ZRW Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:30:31,457 INFO 1682676030012624000ZRW event url is empty >>> 
2023-04-28 15:30:31,457 INFO 1682676030012624000ZRW report generation done , data :{} >>> None
2023-04-28 15:30:31,457 INFO 1682676030012624000ZRW {'duration': '0:00:01.444294', 'type': 'Application', 'tag': 'weekly_report', 'name': 'mydbhealthreport', 'executionID': '1682676030012624000ZRW', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 15:30:30', 'endTime': '2023-04-28 15:30:31', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'weekly', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}}, 'version': '2.0.10'} >>> 
2023-04-28 15:31:21,290 INFO 1682676079721891000Bya Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:32:18,580 INFO 1682676079721891000Bya event url is empty >>> 
2023-04-28 15:32:18,582 INFO 1682676079721891000Bya report generation done , data :{} >>> None
2023-04-28 15:32:18,582 INFO 1682676079721891000Bya {'duration': '0:00:01.570470', 'type': 'Application', 'tag': 'weekly_report', 'name': 'mydbhealthreport', 'executionID': '1682676079721891000Bya', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 15:31:19', 'endTime': '2023-04-28 15:31:21', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'weekly', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}}, 'version': '2.0.10'} >>> 
2023-04-28 15:32:29,826 INFO 1682676148296587000sDr Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:33:26,583 INFO 1682676205216380000CXP Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:33:29,047 INFO 1682676205216380000CXP event url is empty >>> 
2023-04-28 15:33:29,047 INFO 1682676205216380000CXP report generation done , data :{} >>> None
2023-04-28 15:33:29,048 INFO 1682676205216380000CXP {'duration': '0:00:01.368496', 'type': 'Application', 'tag': 'weekly_report', 'name': 'mydbhealthreport', 'executionID': '1682676205216380000CXP', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 15:33:25', 'endTime': '2023-04-28 15:33:26', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'weekly', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}, 'from_time': '2023-04-20', 'to_time': '2023-04-27'}, 'version': '2.0.10'} >>> 
2023-04-28 15:33:50,549 INFO 1682676229092144000nky Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:33:53,039 INFO 1682676229092144000nky event url is empty >>> 
2023-04-28 15:33:53,039 INFO 1682676229092144000nky report generation done , data :{} >>> None
2023-04-28 15:33:53,040 INFO 1682676229092144000nky {'duration': '0:00:01.458302', 'type': 'Application', 'tag': 'weekly_report', 'name': 'mydbhealthreport', 'executionID': '1682676229092144000nky', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 15:33:49', 'endTime': '2023-04-28 15:33:50', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'weekly', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'from_time': '2023-04-20 00:00:00', 'to_time': '2023-04-27 23:59:59', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}, 'from_time': '2023-04-20', 'to_time': '2023-04-27'}, 'version': '2.0.10'} >>> 
2023-04-28 15:41:39,589 INFO 1682676698024998000sDp Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:43:24,589 INFO 1682676803032814000wfV Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 15:43:34,538 INFO 1682676803032814000wfV event url is empty >>> 
2023-04-28 15:43:34,539 INFO 1682676803032814000wfV report generation done , data :{} >>> None
2023-04-28 15:43:34,539 INFO 1682676803032814000wfV {'duration': '0:00:01.559435', 'type': 'Application', 'tag': 'weekly_report', 'name': 'mydbhealthreport', 'executionID': '1682676803032814000wfV', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 15:43:23', 'endTime': '2023-04-28 15:43:24', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'weekly', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'mongo_clusters': 'Node2|***********|27021,Node2|***********|27017,Node2|***********|27018,Node2|***********|27019', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}, 'from_time': '2023-04-20', 'to_time': '2023-04-27', 'server_names': ['mongo_cluster']}, 'version': '2.0.10'} >>> 
2023-04-28 20:45:59,202 INFO 1682694956492500000NyZ Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 20:45:59,207 INFO 1682694956492500000NyZ event url is empty >>> 
2023-04-28 20:45:59,207 INFO 1682694956492500000NyZ report generation done , data :{} >>> None
2023-04-28 20:45:59,208 INFO 1682694956492500000NyZ {'duration': '0:00:02.713415', 'type': 'Application', 'tag': 'weekly_report', 'name': 'mydbhealthreport', 'executionID': '1682694956492500000NyZ', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 20:45:56', 'endTime': '2023-04-28 20:45:59', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_type': 'weekly', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'mongo_clusters': 'Node2|***********|27021,Node2|***********|27017,Node2|***********|27018,Node2|***********|27019', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}, 'from_time': '2023-04-20', 'to_time': '2023-04-27', 'server_names': ['mongo_cluster']}, 'version': '2.0.10'} >>> 
2023-04-28 20:49:56,284 INFO 1682695184617256000jhd Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-28 20:49:56,507 INFO 1682695184617256000jhd Report going to execute for -> weekly_mongo_cluster >>> 
2023-04-28 20:49:57,679 INFO 1682695184617256000jhd Common.process_report_template, entered stats -> ticket_stats >>> 
2023-04-28 20:49:57,680 INFO 1682695184617256000jhd Common.process_report_template, entered stats -> critical_stats >>> 
2023-04-28 20:49:57,680 INFO 1682695184617256000jhd Common.process_report_template, entered stats -> server_stats >>> 
2023-04-28 20:49:57,681 INFO 1682695184617256000jhd Common.process_task, entered report -> Server Information >>> 
2023-04-28 20:50:05,480 INFO 1682695184617256000jhd command going to execute : bash /usr/local/mydbops/healthReport/MydbHealthReport.sh -u mydb -p mydb -a admin -m "Node2|***********|27021,Node2|***********|27017,Node2|***********|27018,Node2|***********|27019" 2>>/usr/local/mydbops/healthReport/log/error.log >>> 
2023-04-28 20:50:05,483 INFO 1682695184617256000jhd Common.process_task, entered report -> Node : ***********:27021 >>> 
2023-04-28 20:50:05,496 INFO 1682695184617256000jhd Common.process_task, entered report -> Node : ***********:27017 >>> 
2023-04-28 20:50:05,510 INFO 1682695184617256000jhd Common.process_task, entered report -> Node : ***********:27018 >>> 
2023-04-28 20:50:05,523 INFO 1682695184617256000jhd Common.process_task, entered report -> Node : ***********:27019 >>> 
2023-04-28 20:50:05,536 INFO 1682695184617256000jhd Common.process_task, entered report -> Mongo Informations : ***********:27021 >>> 
2023-04-28 20:50:05,547 INFO 1682695184617256000jhd Common.process_task, entered report -> Mongo Informations : ***********:27017 >>> 
2023-04-28 20:50:05,554 INFO 1682695184617256000jhd Common.process_task, entered report -> Mongo Informations : ***********:27018 >>> 
2023-04-28 20:50:05,559 INFO 1682695184617256000jhd Common.process_task, entered report -> Mongo Informations : ***********:27019 >>> 
2023-04-28 20:50:05,564 INFO 1682695184617256000jhd Common.process_task, entered report -> Rating >>> 
2023-04-28 20:50:07,152 INFO 1682695184617256000jhd Common.process_task, entered report -> DataBase Statistics >>> 
2023-04-28 20:50:07,162 INFO 1682695184617256000jhd Common.process_task, entered report -> Considerable Collections >>> 
2023-04-28 20:50:07,171 INFO 1682695184617256000jhd Common.process_task, entered report -> Counters >>> 
2023-04-28 20:50:08,186 INFO 1682695184617256000jhd Common.process_task, entered report -> Users >>> 
2023-04-28 20:50:08,188 INFO 1682695184617256000jhd Common.process_task, entered report -> Indexes >>> 
2023-04-28 20:50:08,194 INFO 1682695184617256000jhd Common.process_report_template, entered stats -> graph_stats1 >>> 
2023-04-28 20:50:08,194 INFO 1682695184617256000jhd Common.process_report_template, entered stats -> qan_analysis1 >>> 
2023-04-28 20:50:11,800 INFO 1682695184617256000jhd Group level data ready , Report going to generate -> weekly >>> 
2023-04-28 20:50:22,826 INFO 1682695184617256000jhd MydbEngine.generate_report : File generated successfully >>> 
2023-04-28 20:50:32,587 INFO 1682695184617256000jhd upload_file, success :  >>> 
2023-04-28 20:50:32,593 INFO 1682695184617256000jhd updated report path at local >>> 
2023-04-28 20:50:34,152 INFO 1682695184617256000jhd send_mail_by_aws_ses, Send Mail successfully {'MessageId': '01090187c873a270-b50fc204-4c42-47e0-965e-4b88602b1813-000000', 'ResponseMetadata': {'RequestId': '04d4f772-f6de-4506-acb3-7d3c34834eeb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Fri, 28 Apr 2023 15:20:34 GMT', 'content-type': 'text/xml', 'content-length': '326', 'connection': 'keep-alive', 'x-amzn-requestid': '04d4f772-f6de-4506-acb3-7d3c34834eeb'}, 'RetryAttempts': 0}} >>> 
2023-04-28 20:50:34,160 INFO 1682695184617256000jhd event url is empty >>> 
2023-04-28 20:50:34,160 INFO 1682695184617256000jhd report generation done , data :{} >>> None
2023-04-28 20:50:34,160 INFO 1682695184617256000jhd {'duration': '0:00:49.542151', 'type': 'Application', 'tag': 'custom_report', 'name': 'mydbhealthreport', 'executionID': '1682695184617256000jhd', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'S', 'message': 'report generation done', 'startTime': '2023-04-28 20:49:44', 'endTime': '2023-04-28 20:50:34', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_execution_type': 'custom', 'connection_type': ['ssh'], 'db_type': ['mongo'], 'inventory': {'weekly': {'mongo_cluster': {'connection_type': 'ssh', 'db_type': 'mongo', 'mongo_clusters': 'Node2|***********|27021,Node2|***********|27017,Node2|***********|27018,Node2|***********|27019', 'enabled_stats': ['server_stats'], 'ssh_name': 'mongo_cluster'}}}, 'from_time': '2023-04-20', 'to_time': '2023-04-27', 'server_names': ['mongo_cluster']}, 'version': '2.0.10'} >>> 
2023-04-30 18:37:01,297 INFO 1682859990192176000tMO event url is empty >>> 
2023-04-30 18:37:01,299 ERROR 1682859990192176000tMO Loadconfig error in keyserver request  , data :{} >>> HTTPConnectionPool(host='keyserver.mydbops.com', port=80): Max retries exceeded with url: /get_details_for_send_mail?user_token=481c12be49cd7e8ae757a44b0459320b&client_name=vymo (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x122abf0a0>, 'Connection to keyserver.mydbops.com timed out. (connect timeout=30)'))

2023-04-30 18:37:01,299 ERROR 1682859990192176000tMO {'duration': '0:00:31.096626', 'type': 'Application', 'tag': 'custom_report', 'name': 'mydbhealthreport', 'executionID': '1682859990192176000tMO', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'E', 'message': "HTTPConnectionPool(host='keyserver.mydbops.com', port=80): Max retries exceeded with url: /get_details_for_send_mail?user_token=481c12be49cd7e8ae757a44b0459320b&client_name=vymo (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x122abf0a0>, 'Connection to keyserver.mydbops.com timed out. (connect timeout=30)'))", 'startTime': '2023-04-30 18:36:30', 'endTime': '2023-04-30 18:37:01', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_execution_type': 'custom', 'connection_type': [], 'db_type': [], 'inventory': {}, 'from_time': None, 'to_time': None, 'server_names': [], 'groups': []}, 'version': '2.0.10', 'error': 'Loadconfig error in keyserver request ', 'trace': '~   File "/opt/homebrew/lib/python3.9/site-packages/requests/adapters.py", line 507, in send\n    raise ConnectTimeout(e, request=request)\n'} >>> 

2023-04-30 18:37:34,116 INFO 1682860052825182000ccR event url is empty >>> 
2023-04-30 18:37:34,117 ERROR 1682860052825182000ccR Loadconfig error in keyserver request  , data :{} >>> ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))

2023-04-30 18:37:34,117 ERROR 1682860052825182000ccR {'duration': '0:00:01.287053', 'type': 'Application', 'tag': 'custom_report', 'name': 'mydbhealthreport', 'executionID': '1682860052825182000ccR', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'E', 'message': "('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))", 'startTime': '2023-04-30 18:37:32', 'endTime': '2023-04-30 18:37:34', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_execution_type': 'custom', 'connection_type': [], 'db_type': [], 'inventory': {}, 'from_time': None, 'to_time': None, 'server_names': [], 'groups': []}, 'version': '2.0.10', 'error': 'Loadconfig error in keyserver request ', 'trace': '~   File "/opt/homebrew/lib/python3.9/site-packages/requests/adapters.py", line 501, in send\n    raise ConnectionError(err, request=request)\n'} >>> 

2023-04-30 18:38:05,321 INFO 1682860080414231000nEG Report for -> weekly_mongo_cluster , going to check for schedule >>> 
2023-04-30 18:38:05,322 INFO 1682860080414231000nEG Report going to execute for -> weekly_mongo_cluster >>> 
2023-04-30 18:38:05,322 INFO 1682860080414231000nEG Common.process_report_template, entered stats -> ticket_stats >>> 
2023-04-30 18:38:05,322 INFO 1682860080414231000nEG Common.process_report_template, entered stats -> critical_stats >>> 
2023-04-30 18:38:05,323 INFO 1682860080414231000nEG Common.process_report_template, entered stats -> server_stats >>> 
2023-04-30 18:38:05,323 INFO 1682860080414231000nEG Common.process_task, entered report -> Server Information >>> 
2023-04-30 18:38:13,127 INFO 1682860080414231000nEG command going to execute : bash /usr/local/mydbops/healthReport/MydbHealthReport.sh -u mydb -p mydb -a admin -m "Node2|***********|27021,Node2|***********|27017,Node2|***********|27018,Node2|***********|27019" 2>>/usr/local/mydbops/healthReport/log/error.log >>> 
2023-04-30 18:38:13,130 INFO 1682860080414231000nEG Common.process_task, entered report -> Node : Node2 >>> 
2023-04-30 18:39:01,534 INFO 1682860080414231000nEG Common.process_task, entered report -> Node : Node2 >>> 
2023-05-10 14:43:52,947 INFO 1683710020335822000lzt Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:43:52,949 INFO 1683710020335822000lzt Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:43:52,949 INFO 1683710020335822000lzt Going TO PMM Server Login  >>> 
2023-05-10 14:43:56,996 INFO 1683710020335822000lzt event url is empty >>> 
2023-05-10 14:43:56,996 ERROR 1683710020335822000lzt exception when try to PMM login at internal hook , data :{} >>> ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))

2023-05-10 14:43:56,996 ERROR 1683710020335822000lzt {'duration': '0:00:16.655987', 'type': 'Application', 'tag': 'Weekly_report', 'name': 'mydbhealthreport', 'executionID': '1683710020335822000lzt', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'E', 'message': "('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))", 'startTime': '2023-05-10 14:43:40', 'endTime': '2023-05-10 14:43:56', 'output': {'client': 'vymo', 'client_env': 'vymo_india', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_execution_type': 'Weekly', 'connection_type': ['ssh'], 'db_type': ['mysql'], 'inventory': {'weekly': {'bastion': {'connection_type': 'ssh', 'db_type': 'mysql', 'node_name': 'vetriveln_test', 'enabled_stats': ['server_stats', 'sql_stats', 'graph_stats', 'qan_analysis'], 'ssh_name': 'bastion'}}}, 'from_time': '2023-05-02', 'to_time': '2023-05-09', 'server_names': ['bastion'], 'groups': ['weekly']}, 'version': '2.0.10', 'error': 'exception when try to PMM login at internal hook', 'trace': '~   File "/opt/homebrew/lib/python3.9/site-packages/requests/adapters.py", line 501, in send\n    raise ConnectionError(err, request=request)\n'} >>> 

2023-05-10 14:44:25,706 INFO 1683710064252937000Uve Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:44:25,706 INFO 1683710064252937000Uve Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:44:28,252 INFO 1683710064252937000Uve command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306 -e " SELECT DATEDIFF(NOW(), NOW() - INTERVAL VARIABLE_VALUE SECOND) AS uptime_days FROM performance_schema.session_status WHERE VARIABLE_NAME = 'Uptime'; "  >>> 
2023-05-10 14:44:28,253 INFO 1683710064252937000Uve Common.process_report_template, entered stats -> ticket_stats >>> 
2023-05-10 14:44:28,253 INFO 1683710064252937000Uve Common.process_report_template, entered stats -> critical_stats >>> 
2023-05-10 14:44:28,254 INFO 1683710064252937000Uve Common.process_report_template, entered stats -> server_stats >>> 
2023-05-10 14:44:28,254 INFO 1683710064252937000Uve Common.process_task, entered report -> Server Information >>> 
2023-05-10 14:44:34,812 INFO 1683710064252937000Uve command going to execute : pt-summary >>> 
2023-05-10 14:44:34,814 INFO 1683710064252937000Uve Common.analyse_response, from node :  >>>      SELinux | No SELinux detected
2023-05-10 14:45:11,861 INFO 1683710064252937000Uve Common.analyse_response, from node :  >>>   Locator   Size     Speed             Form Factor   Type          Type Detail  ========= ======== ================= ============= ============= ===========
2023-05-10 14:45:30,492 INFO 1683710128933138000igT Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:45:30,493 INFO 1683710128933138000igT Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:45:35,403 INFO 1683710128933138000igT command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306 -e " SELECT DATEDIFF(NOW(), NOW() - INTERVAL VARIABLE_VALUE SECOND) AS uptime_days FROM performance_schema.session_status WHERE VARIABLE_NAME = 'Uptime'; "  >>> 
2023-05-10 14:45:35,404 INFO 1683710128933138000igT Common.process_report_template, entered stats -> ticket_stats >>> 
2023-05-10 14:45:35,405 INFO 1683710128933138000igT Common.process_report_template, entered stats -> critical_stats >>> 
2023-05-10 14:45:35,405 INFO 1683710128933138000igT Common.process_task, entered report -> Alert Count by Host Name >>> 
2023-05-10 14:45:35,427 INFO 1683710128933138000igT command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'whitewalker', '-e', "SELECT\n  host,\n  count(*) as 'alert count'\nFROM\n  whitewalker.alerts_grouped\nWHERE\n  (host not like '%_mongo_%' and host not like '%_postgres_%' ) and\n  cdate between '2023-05-02 00:00:00' and '2023-05-09 23:59:59' and\n  client in ('vymo')\ngroup by host ORDER BY 2 desc limit 10;\n"] >>> 
2023-05-10 14:45:35,926 INFO 1683710128933138000igT output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
host	alert count
vymo_ausc_source_mysql	674
vymo_ausc_replica_mysql	1
 >>> 
2023-05-10 14:45:35,927 INFO 1683710128933138000igT Common.analyse_response, from node :  >>> mysql: [Warning] Using a password on the command line interface can be insecure.
2023-05-10 14:46:08,272 INFO 1683710128933138000igT Common.process_task, entered report -> Alert Count for Top Services >>> 
2023-05-10 14:46:08,283 INFO 1683710128933138000igT command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'whitewalker', '-e', "SELECT\n  concat(substring_index(host, '_', -3), '.', substring_index(service, 'Check_', -1)) as 'service',\n  count(*) as 'alert count'\nFROM\n  whitewalker.alerts_grouped\nWHERE\n  (host not like '%_mongo_%' and host not like '%_postgres_%' ) and\n  cdate between '2023-05-02 00:00:00' and '2023-05-09 23:59:59' and\n  client in ('vymo')\ngroup by host, service ORDER BY 2 desc limit 10;"] >>> 
2023-05-10 14:46:10,855 INFO 1683710128933138000igT output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
service	alert count
ausc_source_mysql.MySQL_Longqueries	221
ausc_source_mysql.Load	189
ausc_source_mysql.MySQL_Innodb_Idle_Blocker	168
ausc_source_mysql.MySQL_Connection	49
ausc_source_mysql.MySQL_Innodb_Max_Duration	39
ausc_source_mysql.IOStats	3
ausc_source_mysql.MySQL_Config_Diff_Config	3
ausc_source_mysql.Memory	2
ausc_replica_mysql.MySQL_Innodb_Idle_Blocker	1
 >>> 
2023-05-10 14:46:10,856 INFO 1683710128933138000igT Common.analyse_response, from node :  >>> mysql: [Warning] Using a password on the command line interface can be insecure.
2023-05-10 14:46:38,227 INFO 1683710196751535000qbN Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:46:38,228 INFO 1683710196751535000qbN Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:46:42,485 INFO 1683710196751535000qbN command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306 -e " SELECT DATEDIFF(NOW(), NOW() - INTERVAL VARIABLE_VALUE SECOND) AS uptime_days FROM performance_schema.session_status WHERE VARIABLE_NAME = 'Uptime'; "  >>> 
2023-05-10 14:46:42,486 INFO 1683710196751535000qbN Common.process_report_template, entered stats -> ticket_stats >>> 
2023-05-10 14:46:42,486 INFO 1683710196751535000qbN Common.process_report_template, entered stats -> critical_stats >>> 
2023-05-10 14:46:42,486 INFO 1683710196751535000qbN Common.process_task, entered report -> Alert Count by Host Name >>> 
2023-05-10 14:46:49,091 INFO 1683710196751535000qbN command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'whitewalker', '-e', "SELECT\n  host,\n  count(*) as 'alert count'\nFROM\n  whitewalker.alerts_grouped\nWHERE\n  (host not like '%_mongo_%' and host not like '%_postgres_%' ) and\n  cdate between '2023-05-02 00:00:00' and '2023-05-09 23:59:59' and\n  client in ('vymo')\ngroup by host ORDER BY 2 desc limit 10;\n"] >>> 
2023-05-10 14:46:50,119 INFO 1683710196751535000qbN output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
host	alert count
vymo_ausc_source_mysql	674
vymo_ausc_replica_mysql	1
 >>> 
2023-05-10 14:47:35,543 INFO 1683710254001569000Gng Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:47:35,544 INFO 1683710254001569000Gng Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:47:40,668 INFO 1683710259105268000jja Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:47:40,668 INFO 1683710259105268000jja Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:47:42,608 INFO 1683710259105268000jja command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306 -e " SELECT DATEDIFF(NOW(), NOW() - INTERVAL VARIABLE_VALUE SECOND) AS uptime_days FROM performance_schema.session_status WHERE VARIABLE_NAME = 'Uptime'; "  >>> 
2023-05-10 14:47:42,609 INFO 1683710259105268000jja Common.process_report_template, entered stats -> ticket_stats >>> 
2023-05-10 14:47:42,609 INFO 1683710259105268000jja Common.process_task, entered report -> Tickets Handled >>> 
2023-05-10 14:47:49,294 INFO 1683710259105268000jja command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'reports', '-e', "select\n  ticket,\n  status,\n  date(created_on) created_On,\n  consultant,\n  subject,\n  requester\nfrom\n  reports.monthly_report\nwhere \n  client='vymo' and\n  created_on between '2023-05-02 00:00:00' and '2023-05-09 23:59:59'\norder by created_on desc"] >>> 
2023-05-10 14:47:50,881 INFO 1683710259105268000jja output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
Ticket	Status	created_On	Consultant	Subject	Requester
210642	Closed	2023-05-09	Jeyaraj.j	vymo_asi | vymo_asi02_source_mysql | mysql_uptime in warning state	<EMAIL>
210081	Closed	2023-05-09	janaki kollipara	vymouat | vymo_uat_replica_mysql | mydbops_mysql_replication_status_delay_monitor in critical state	<EMAIL>
210098	Closed	2023-05-09	janaki kollipara	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
210099	Closed	2023-05-09	janaki kollipara	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
210115	Closed	2023-05-09	janaki kollipara	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
210125	Closed	2023-05-09	janaki kollipara	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
210130	Closed	2023-05-09	janaki kollipara	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
210205	Resolved	2023-05-09	Utchimahali Alias Suresh A	vymoindia | vymo_mongo_vymomongoc7_35_9 | load in critical state	<EMAIL>
210354	Waiting on Customer	2023-05-09	Utchimahali Alias Suresh A	Vymo | ACI C1 | Mydbops MongoDB Health Report - Report_ACI_C109_May_2023.pdf	<EMAIL>
210360	Waiting on Customer	2023-05-09	Utchimahali Alias Suresh A	Vymo | ACI C3 | Mydbops MongoDB Health Report - Report_ACI_C309_May_2023.pdf	<EMAIL>
210369	Waiting on Customer	2023-05-09	Utchimahali Alias Suresh A	Vymo | ACI C4 | Mydbops MongoDB Health Report - Report_ACI_C409_May_2023.pdf	<EMAIL>
210374	Waiting on Customer	2023-05-09	Pokuri Chandrika	Vymo ASI | Mydbops MongoDB Health Report - Report_monthly 09_May_2023.pdf	<EMAIL>
210377	Waiting on Customer	2023-05-09	Anand Amarnath C	Vymo | ACI C5 | Mydbops MongoDB Health Report - Report_ACI_C509_May_2023.pdf	<EMAIL>
210380	Waiting on Customer	2023-05-09	Anand Amarnath C	Vymo | ACI C6 | Mydbops MongoDB Health Report - Report_ACI_C609_May_2023.pdf	<EMAIL>
210381	Waiting on Customer	2023-05-09	Anand Amarnath C	Vymo | ACI C7 | Mydbops MongoDB Health Report - Report_ACI_C709_May_2023.pdf	<EMAIL>
210387	Waiting on Customer	2023-05-09	Vatcharavel V	Vymo | ACI C9 | Mydbops MongoDB Health Report - Report_ACI_C909_May_2023.pdf	<EMAIL>
210383	Waiting on Customer	2023-05-09	Vatcharavel V	Vymo | ACI C8 | Mydbops MongoDB Health Report - Report_ACI_C809_May_2023.pdf	<EMAIL>
210637	Closed	2023-05-09	Jeyaraj.j	vymo_asi | vymo_asi02_source_mysql | uptime in critical state	<EMAIL>
210635	Closed	2023-05-09	Jeyaraj.j	vymo_asi | vymo_asi02_replica_mysql | mydbops_mysql_replication_status_delay_monitor in critical state	<EMAIL>
210535	Waiting on Customer	2023-05-09	Vatcharavel V	vymoindia | vymo_mongo_vymomongoc11_50_10 | Slow query	<EMAIL>
210529	Waiting on Customer	2023-05-09	Utchimahali Alias Suresh A	Create Index on MONGODB	<EMAIL>
210507	Resolved	2023-05-09	Pokuri Chandrika	vymouat | vymo_uat_mongo_mongouat_4_57 | mongo_collscan_query in critical state	<EMAIL>
210472	Waiting on Customer	2023-05-09	Pokuri Chandrika	Check for query optimisation	<EMAIL>
210459	Waiting on Customer	2023-05-09	Anand Amarnath C	vymouat | vymo_uat_mongo_mongouat_4_57 | uptime in critical state	<EMAIL>
210396	Waiting on Customer	2023-05-09	Ramkumar S	Vymo | ACI C13 | Mydbops MongoDB Health Report - Report_ACI_C1309_May_2023.pdf	<EMAIL>
210394	Waiting on Customer	2023-05-09	Ramkumar S	Vymo | ACI C12 | Mydbops MongoDB Health Report - Report_ACI_C1209_May_2023.pdf	<EMAIL>
210392	Waiting on Customer	2023-05-09	Ramkumar S	Vymo | ACI C11 | Mydbops MongoDB Health Report - Report_ACI_C1109_May_2023.pdf	<EMAIL>
210390	Waiting on Customer	2023-05-09	Vatcharavel V	Vymo | ACI C10 | Mydbops MongoDB Health Report - Report_ACI_C1009_May_2023.pdf	<EMAIL>
210389	Waiting on Customer	2023-05-09	Pokuri Chandrika	Vymo | ASEA C3 | Mydbops MongoDB Health Report - Report_monthly09_May_2023.pdf	<EMAIL>
209676	Closed	2023-05-08	Komban B	vymouat | vymo_uat_replica_mysql | mydbops_mysql_replication_status_delay_monitor in critical state	<EMAIL>
209682	Closed	2023-05-08	Komban B	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
209913	Waiting on Customer	2023-05-08	Anand Amarnath C	Please check these query and create indexes	<EMAIL>
209683	Closed	2023-05-08	Komban B	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
209690	Waiting on Customer	2023-05-08	Komban B	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
209795	Resolved	2023-05-08	Ramkumar S	vymoindia | vymo_mongo_vymomongoc9_39_9 | mongo_collscan_query in recovery state	<EMAIL>
209900	Waiting on Customer	2023-05-08	Ramkumar S	vymo_asi | vymo_mongo_asi02mongoprodc2_21_5 | slow query	<EMAIL>
210042	Resolved	2023-05-08	Anand Amarnath C	vymoindia | vymo_mongo_vymomongoc9_hidden_39_8 | memory in warning state	<EMAIL>
209493	Resolved	2023-05-07	Pokuri Chandrika	vymoindia | vymo_mongo_vymomongoc4_hidden_18_9 | mongo_connect_primary in critical state	<EMAIL>
209483	Waiting on Consultant	2023-05-07	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
209461	Resolved	2023-05-07	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
209447	Closed	2023-05-07	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
209446	Closed	2023-05-07	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
209439	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	vymouat | vymo_uat_replica_mysql | mydbops_mysql_replication_status_delay_monitor in critical state	<EMAIL>
208957	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
209267	Waiting on Customer	2023-05-06	Ramkumar S	vymo | vymo_mongo_vymomongoc9_39_9 | slow query	<EMAIL>
208988	Waiting on Customer	2023-05-06	Pokuri Chandrika	vymouat | vymo_uat_mongo_mongouat_4_56 | disk in warning state	<EMAIL>
208966	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
208961	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
208956	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
208386	Closed	2023-05-05	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
208374	Resolved	2023-05-05	Sai Kumar	vymouat | vymo_uat_replica_mysql | mydbops_mysql_replication_status_delay_monitor in recovery state	<EMAIL>
208377	Resolved	2023-05-05	Pokuri Chandrika	vymoindia | vymo_mongo_vymomongoc4_hidden_18_9 | memory in warning state	<EMAIL>
208378	Resolved	2023-05-05	Pokuri Chandrika	Create Index on MONGODB	<EMAIL>
208383	Resolved	2023-05-05	Pokuri Chandrika	vymoindia | vymo_mongo_vymomongoc13_hidden_43_8 | memory in recovery state	<EMAIL>
208387	Resolved	2023-05-05	Paul Meriton Issac	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
208394	Closed	2023-05-05	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
208405	Closed	2023-05-05	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
208419	Closed	2023-05-05	Paul Meriton Issac	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
208630	Resolved	2023-05-05	Ramkumar S	Please share the performance analysis of this query on ASI02PROD.	<EMAIL>
208875	Waiting on Customer	2023-05-05	janaki kollipara	vymo |Monthly Query Tuning Report | April	<EMAIL>
208898	In Progress	2023-05-05	Deepthi Gunasekaran	Vymo | Weekly catchup call | MOM	<EMAIL>
207697	Closed	2023-05-04	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
207690	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	vymouat | vymo_uat_replica_mysql | mydbops_mysql_replication_status_delay_monitor in critical state	<EMAIL>
207698	In Progress	2023-05-04	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
207705	Closed	2023-05-04	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
207712	Closed	2023-05-04	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
207733	Closed	2023-05-04	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
207894	Resolved	2023-05-04	Ramkumar S	vymoindia | vymo_mongo_vymomongoc3_19_6 | mongo_numyields_query in critical state	<EMAIL>
207952	Resolved	2023-05-04	Vatcharavel V	vymoindia | vymo_mongo_vymomongoc9_39_9 | mongo_numyields_query in critical state	<EMAIL>
207994	Waiting on Customer	2023-05-04	Ramkumar S	vymoindia | vymo_mongo_vymomongoc1_hidden_20_9 | mongo_replication_lag in critical state	<EMAIL>
207893	Waiting on Customer	2023-05-04	Vatcharavel V	vymoindia | vymo_mongo_vymomongoc9_39_9 | mongo_numyields_query in critical state	<EMAIL>
208149	Resolved	2023-05-04	Ramkumar S	vymo | vymo_mongo_asi02mongoprodc2_21_6 | mongo_numyields_query in critical state	<EMAIL>
207214	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	vymouat | vymo_uat_replica_mysql | mydbops_mysql_replication_status_delay_monitor in critical state	<EMAIL>
207222	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
207223	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
207226	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
207229	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
207244	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
207520	Resolved	2023-05-03	Anand Amarnath C	Out of office Re: 175846 Vymo | vymomongoc12 | Duplicate Index List	<EMAIL>
207546	Resolved	2023-05-03	Anand Amarnath C	Out of office Re: 175848 Vymo | vymomongoc13 | Duplicate Index List	<EMAIL>
207241	Resolved	2023-05-03	Pokuri Chandrika	vymoindia | vymo_mongo_vymomongoc9_hidden_39_8 | load in warning state	<EMAIL>
206653	Resolved	2023-05-02	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in unknown state	<EMAIL>
206654	Closed	2023-05-02	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_status in warning state	<EMAIL>
206658	Closed	2023-05-02	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
206663	Closed	2023-05-02	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
206671	Closed	2023-05-02	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
206678	Closed	2023-05-02	Sai Kumar	vymosingapore | vymo_sg_replica1_mysql | mysql_replication_delay in critical state	<EMAIL>
206733	Closed	2023-05-02	Maha Lakshmi Ganapathineedi	Out of office Re: 144450 BiG COLLECTION DATA ARCHIVAL	<EMAIL>
206759	Closed	2023-05-02	Ajithkumar N	Out of office Re: 180164 vymoindia | vymo_mongo_vymomongoc2_21_5 | load in warning state	<EMAIL>
206760	Closed	2023-05-02	Ajithkumar N	Out of office Re: 188863 vymo_aci |vymo_mongo_vymomongoc2_21_9(Primary)| Oplog Alert	<EMAIL>
207006	Resolved	2023-05-02	Ramkumar S	vymoindia | vymo_mongo_vymomongoc13_43_9 | mongo_numyields_query in recovery state	<EMAIL>
207007	Closed	2023-05-02	Ajithkumar N	Out of office Re: 186653 vymoindia | vymo_mongo_vymomongoc7_35_9 | mongo_oplog in critical state	<EMAIL>
207100	Closed	2023-05-02	janaki kollipara	vymouat | vymo_uat_source_mysql | mysql_innodb_wait_counter in warning state	<EMAIL>
207160	Closed	2023-05-02	Pokuri Chandrika	Out of office Re: 176615 Vymo | ACI C1 | Version Upgrade 3.2 to 4.2	<EMAIL>
206650	Closed	2023-05-02	Maha Lakshmi Ganapathineedi	vymouat | vymo_uat_replica_mysql | mydbops_mysql_replication_status_delay_monitor in recovery state	<EMAIL>
 >>> 
2023-05-10 14:48:44,037 INFO 1683710322573591000mAZ Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:48:44,038 INFO 1683710322573591000mAZ Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:48:46,858 INFO 1683710322573591000mAZ command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306 -e " SELECT DATEDIFF(NOW(), NOW() - INTERVAL VARIABLE_VALUE SECOND) AS uptime_days FROM performance_schema.session_status WHERE VARIABLE_NAME = 'Uptime'; "  >>> 
2023-05-10 14:48:46,859 INFO 1683710322573591000mAZ Common.process_report_template, entered stats -> ticket_stats >>> 
2023-05-10 14:48:46,859 INFO 1683710322573591000mAZ Common.process_task, entered report -> Tickets Handled >>> 
2023-05-10 14:48:47,833 INFO 1683710322573591000mAZ command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'reports', '-e', "select\n  ticket,\n  status,\n  date(created_on) created_On,\n  consultant,\n  subject,\n  requester\nfrom\n  reports.monthly_report\nwhere \n  client='userexperior' and\n  created_on between '2023-05-02 00:00:00' and '2023-05-09 23:59:59'\norder by created_on desc"] >>> 
2023-05-10 14:48:48,464 INFO 1683710322573591000mAZ output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
Ticket	Status	created_On	Consultant	Subject	Requester
210090	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210123	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
210170	Closed	2023-05-09	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210176	Closed	2023-05-09	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
210186	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
210192	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210201	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
210241	Closed	2023-05-09	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210266	Closed	2023-05-09	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
210575	In Progress	2023-05-09	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant2_source_mysql | iostats in critical state	<EMAIL>
210590	Closed	2023-05-09	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210624	Closed	2023-05-09	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
210644	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209665	Closed	2023-05-08	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209748	Closed	2023-05-08	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209753	Waiting on Customer	2023-05-08	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209770	Scheduled Activity	2023-05-08	Shree Mantur	purge data from tenant 1 DB(***********)	<EMAIL>
209928	Resolved	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209931	Closed	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209946	Closed	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209990	Closed	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210015	Resolved	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
210026	Resolved	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209434	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209438	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209468	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209476	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209491	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209515	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209531	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209535	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209551	Closed	2023-05-07	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209570	Closed	2023-05-07	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_process_locking_state in warning state	<EMAIL>
209588	Closed	2023-05-07	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in critical state	<EMAIL>
209602	Closed	2023-05-07	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209625	Closed	2023-05-07	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209626	Closed	2023-05-07	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209628	Closed	2023-05-07	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209653	Closed	2023-05-07	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208941	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208945	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208950	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208951	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209040	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209047	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209055	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209058	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209063	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209064	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209066	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209067	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209068	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209070	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209071	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209081	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209084	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209089	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209091	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209093	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209095	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209104	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209107	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209114	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209115	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209117	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209118	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
209124	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209131	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209134	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209144	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in critical state	<EMAIL>
209145	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209146	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
209150	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209154	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209157	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209159	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209161	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209164	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209165	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209167	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209172	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209175	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209179	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209185	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209187	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209189	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209190	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209191	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
209194	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209199	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209201	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209203	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209209	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209237	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209242	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209244	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209247	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209252	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209255	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209256	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209263	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209264	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209266	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209269	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209270	Resolved	2023-05-06	Ashok kumar T	purge data from tenant 1 DB(***********)	<EMAIL>
209271	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209272	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209275	Resolved	2023-05-06	Ramasamy R	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209284	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209286	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209288	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209293	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209295	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209296	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209297	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209298	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209303	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209306	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209326	Closed	2023-05-06	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209328	Closed	2023-05-06	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209329	Closed	2023-05-06	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209403	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209414	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209423	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208360	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208362	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208365	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208366	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208369	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208370	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208371	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208372	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208376	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208379	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208390	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208402	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208408	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208409	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208411	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208436	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208447	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208448	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208498	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208502	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208504	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208506	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208514	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208522	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208525	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208526	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208529	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208530	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208532	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208534	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208535	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208536	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208537	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208540	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208541	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208549	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208711	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208712	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208715	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208718	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208722	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208724	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208730	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208732	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208734	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208736	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208738	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208745	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208748	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208756	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208757	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208758	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208769	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208772	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208774	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208777	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208781	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208786	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208795	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208799	Closed	2023-05-05	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208800	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208803	Closed	2023-05-05	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208819	Closed	2023-05-05	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208836	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208857	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208858	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208860	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208864	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208865	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208869	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208870	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208873	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208876	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208878	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208879	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208887	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208888	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208889	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208895	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208902	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208926	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208930	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208934	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208936	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207685	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207688	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207691	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207716	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207718	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207719	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207722	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207751	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207828	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207849	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207851	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207852	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207862	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207869	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207871	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207872	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207874	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207875	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207876	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207877	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207879	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207880	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207884	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207885	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207892	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207896	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207900	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207907	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207909	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207911	Resolved	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207917	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207923	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207926	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207927	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207931	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207932	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207933	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207934	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207939	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207940	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207941	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207944	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207945	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207946	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207973	Closed	2023-05-04	Vijayalakshmi Metla	Monthly Database Report - UserExperior | April - 2023	<EMAIL>
208100	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208113	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208120	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208124	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208125	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208129	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208130	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208138	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208144	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208155	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208157	Resolved	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208158	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208160	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208161	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208165	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208168	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208173	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208176	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208179	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208187	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208192	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208194	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208197	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208202	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208205	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208209	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208214	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208217	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208218	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208222	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208223	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208226	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208230	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208236	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208240	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208243	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208245	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208248	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208250	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208252	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208253	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208255	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208258	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208265	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208267	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208268	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208270	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208274	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208276	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208278	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208280	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208282	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208287	Resolved	2023-05-04	Ashok kumar T	purge data from tenant 1 DB(***********) (table:ue_events_base )	<EMAIL>
208288	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208289	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208292	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208299	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208302	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208304	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208307	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208309	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208312	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208315	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208318	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208319	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208321	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208322	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208324	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208329	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208337	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208340	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208344	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208347	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208348	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208349	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208351	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208352	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208354	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208355	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207198	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207202	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207205	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207206	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207236	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207237	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207326	Resolved	2023-05-03	Athistalakshmi.K	Recover database free space in tenant 2(**********)	<EMAIL>
207448	Closed	2023-05-03	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207453	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207454	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207455	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207456	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207457	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207458	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207461	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207462	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207464	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207465	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207495	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207499	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207522	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207531	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207538	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207544	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207549	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207569	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207646	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207647	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207648	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207649	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207650	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207652	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207653	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207671	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207672	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207675	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207677	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207679	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206719	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206721	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206722	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206723	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206725	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206726	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206727	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206728	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206768	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206769	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206772	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206776	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206781	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206810	Resolved	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206811	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206815	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206816	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206817	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206818	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206820	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206821	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206823	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206825	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206830	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206837	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206839	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206866	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206885	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206886	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206887	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206889	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206892	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206893	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206896	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206897	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206901	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206911	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206913	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206914	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206915	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206917	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206925	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206948	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206949	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206956	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206959	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206960	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206965	Resolved	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206998	Resolved	2023-05-02	Sharmika Bhogadi	purge data from tenant 2 DB(**********)	<EMAIL>
207003	Resolved	2023-05-02	Vinitha G	purge data from tenant 1 DB(***********)	<EMAIL>
207012	Waiting on Customer	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207019	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207023	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207030	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207033	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207039	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207066	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207081	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207083	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207089	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207099	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207102	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207106	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207111	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207112	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207115	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207121	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207124	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207126	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207128	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207129	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207141	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207143	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207146	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207149	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207151	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207155	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207159	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207173	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207179	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207183	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207184	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207185	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207186	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207188	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207190	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207191	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
 >>> 
2023-05-10 14:48:48,476 INFO 1683710322573591000mAZ Common.analyse_response, from node :  >>> mysql: [Warning] Using a password on the command line interface can be insecure.
2023-05-10 14:50:57,905 INFO 1683710322573591000mAZ Common.process_report_template, entered stats -> critical_stats >>> 
2023-05-10 14:50:57,906 INFO 1683710322573591000mAZ Common.process_task, entered report -> Alert Count by Host Name >>> 
2023-05-10 14:51:14,220 INFO 1683710322573591000mAZ command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'whitewalker', '-e', "SELECT\n  host,\n  count(*) as 'alert count'\nFROM\n  whitewalker.alerts_grouped\nWHERE\n  (host not like '%_mongo_%' and host not like '%_postgres_%' ) and\n  cdate between '2023-05-02 00:00:00' and '2023-05-09 23:59:59' and\n  client in ('userexperior')\ngroup by host ORDER BY 2 desc limit 10;\n"] >>> 
2023-05-10 14:51:15,107 INFO 1683710322573591000mAZ output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
host	alert count
userexperior_ue_tenant1_source_mysql	1279
userexperior_ue_tenant2_source_mysql	433
userexperior_ue_prod_source_mysql	129
userexperior_ue_tenant4_source_mysql	15
userexperior_monitor	3
 >>> 
2023-05-10 14:51:15,110 INFO 1683710322573591000mAZ Common.analyse_response, from node :  >>> mysql: [Warning] Using a password on the command line interface can be insecure.
2023-05-10 14:51:17,418 INFO 1683710322573591000mAZ Common.process_task, entered report -> Alert Count for Top Services >>> 
2023-05-10 14:51:18,484 INFO 1683710322573591000mAZ command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'whitewalker', '-e', "SELECT\n  concat(substring_index(host, '_', -3), '.', substring_index(service, 'Check_', -1)) as 'service',\n  count(*) as 'alert count'\nFROM\n  whitewalker.alerts_grouped\nWHERE\n  (host not like '%_mongo_%' and host not like '%_postgres_%' ) and\n  cdate between '2023-05-02 00:00:00' and '2023-05-09 23:59:59' and\n  client in ('userexperior')\ngroup by host, service ORDER BY 2 desc limit 10;"] >>> 
2023-05-10 14:51:18,931 INFO 1683710322573591000mAZ output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
service	alert count
tenant1_source_mysql.MySQL_Active_Thread	462
tenant1_source_mysql.MySQL_Transaction_Analyzer	413
tenant2_source_mysql.IOStats	280
tenant1_source_mysql.IOStats	158
tenant2_source_mysql.MySQL_Transaction_Analyzer	133
tenant1_source_mysql.Load	115
prod_source_mysql.IOStats	72
prod_source_mysql.MySQL_Transaction_Analyzer	57
tenant1_source_mysql.CPU	57
tenant1_source_mysql.MySQL_Unauth_User	46
 >>> 
2023-05-10 14:51:18,932 INFO 1683710322573591000mAZ Common.analyse_response, from node :  >>> mysql: [Warning] Using a password on the command line interface can be insecure.
2023-05-10 14:51:20,077 INFO 1683710322573591000mAZ Common.process_task, entered report -> Daywise Alert Count >>> 
2023-05-10 14:51:21,160 INFO 1683710322573591000mAZ command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'whitewalker', '-e', "SELECT\n  date(cdate) as 'date',\n  count(*) as 'alert count'\nFROM\n  whitewalker.alerts_grouped\nWHERE\n  (host not like '%_mongo_%' and host not like '%_postgres_%' ) and\n  date(cdate) between '2023-05-02 00:00:00' and '2023-05-09 23:59:59' and\n  client in ('userexperior')\ngroup by date(cdate) ORDER BY 1;"] >>> 
2023-05-10 14:51:21,711 INFO 1683710322573591000mAZ output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
date	alert count
2023-05-02	266
2023-05-03	257
2023-05-04	236
2023-05-05	225
2023-05-06	257
2023-05-07	174
2023-05-08	198
2023-05-09	246
 >>> 
2023-05-10 14:51:21,712 INFO 1683710322573591000mAZ Common.analyse_response, from node :  >>> mysql: [Warning] Using a password on the command line interface can be insecure.
2023-05-10 14:51:22,692 INFO 1683710322573591000mAZ Common.process_report_template, entered stats -> server_stats >>> 
2023-05-10 14:51:22,692 INFO 1683710322573591000mAZ Common.process_task, entered report -> Server Information >>> 
2023-05-10 14:51:29,886 INFO 1683710322573591000mAZ command going to execute : pt-summary >>> 
2023-05-10 14:51:29,888 INFO 1683710322573591000mAZ Common.analyse_response, from node :  >>>      SELinux | No SELinux detected
2023-05-10 14:51:34,198 INFO 1683710322573591000mAZ Common.analyse_response, from node :  >>>   Locator   Size     Speed             Form Factor   Type          Type Detail  ========= ======== ================= ============= ============= ===========
2023-05-10 14:51:41,782 INFO 1683710322573591000mAZ Common.process_report_template, entered stats -> sql_stats >>> 
2023-05-10 14:51:41,783 INFO 1683710322573591000mAZ Common.process_task, entered report -> Unused Index >>> 
2023-05-10 14:51:44,689 INFO 1683710322573591000mAZ command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306  -e " select 
  concat(ui.object_schema, '.', ui.object_name) 'Table Name',
  ui.index_name 'Index Name',
  (ifnull(iis.stat_value, 0) * @@innodb_page_size) 'Size' /*bytes*/
from (
  select 
    object_schema,
    object_name,
    index_name
  from 
    sys.schema_unused_indexes
  where 
    object_schema not in('information_schema','sys','mysql','performance_schema') 
  order by 
    object_schema,object_name,index_name
) ui left join  
  mysql.innodb_index_stats iis
on 
  ui.object_schema=iis.database_name and
  ui.object_name=iis.table_name and
  ui.index_name=iis.index_name and
  iis.stat_name='size'
order by 
  iis.stat_value desc limit 20; "  >>> 
2023-05-10 14:51:45,763 INFO 1683710322573591000mAZ Common.process_task, entered report -> Active Tables By Read Operation >>> 
2023-05-10 14:51:47,463 INFO 1683710322573591000mAZ command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306  -e " set session sql_mode='';
select sum(rows_fetched) into @total_reads from sys.schema_table_statistics;
select
  concat(table_schema, '.', table_name) as 'Table',
  rows_fetched as 'Reads',
  ifnull((rows_fetched*100)/@total_reads,0) as 'Percentage'
from
  sys.schema_table_statistics
where
  table_schema not in ('information_schema', 'sys', 'mysql', 'performance_schema')
order by rows_fetched desc limit 10; "  >>> 
2023-05-10 14:51:48,483 INFO 1683710322573591000mAZ Common.process_report_template, entered stats -> graph_stats1 >>> 
2023-05-10 14:51:48,483 INFO 1683710322573591000mAZ Common.process_report_template, entered stats -> qan_analysis1 >>> 
2023-05-10 14:51:48,484 INFO 1683710322573591000mAZ Group level data ready , Report going to generate -> weekly >>> 
2023-05-10 14:52:02,649 INFO 1683710322573591000mAZ MydbEngine.generate_report : File generated successfully >>> 
2023-05-10 14:53:56,967 INFO 1683710635048735000PSq Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-10 14:53:56,968 INFO 1683710635048735000PSq Report going to execute for -> weekly_bastion >>> 
2023-05-10 14:53:58,665 INFO 1683710635048735000PSq command going to execute : mysql -h ************** -u root -p'Rootdb123' -P 3306 -e " SELECT DATEDIFF(NOW(), NOW() - INTERVAL VARIABLE_VALUE SECOND) AS uptime_days FROM performance_schema.session_status WHERE VARIABLE_NAME = 'Uptime'; "  >>> 
2023-05-10 14:53:58,666 INFO 1683710635048735000PSq Common.process_report_template, entered stats -> ticket_stats >>> 
2023-05-10 14:53:58,666 INFO 1683710635048735000PSq Common.process_task, entered report -> Tickets Handled >>> 
2023-05-10 14:54:05,947 INFO 1683710635048735000PSq command going to execute : ['mysql', '-h', '*************', '-u', 'vetri', '-pkSaX42Xzz4xDrt', '-P', '3330', 'reports', '-e', "select\n  ticket,\n  status,\n  date(created_on) created_On,\n  consultant,\n  subject,\n  requester\nfrom\n  reports.monthly_report\nwhere \n  client='userexperior' and\n  created_on between '2023-05-02 00:00:00' and '2023-05-09 23:59:59'\norder by created_on desc"] >>> 
2023-05-10 14:54:06,511 INFO 1683710635048735000PSq output from command execution :mysql: [Warning] Using a password on the command line interface can be insecure.
Ticket	Status	created_On	Consultant	Subject	Requester
210090	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210123	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
210170	Closed	2023-05-09	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210176	Closed	2023-05-09	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
210186	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
210192	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210201	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
210241	Closed	2023-05-09	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210266	Closed	2023-05-09	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
210575	In Progress	2023-05-09	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant2_source_mysql | iostats in critical state	<EMAIL>
210590	Closed	2023-05-09	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210624	Closed	2023-05-09	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
210644	Closed	2023-05-09	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209665	Closed	2023-05-08	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209748	Closed	2023-05-08	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209753	Waiting on Customer	2023-05-08	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209770	Scheduled Activity	2023-05-08	Shree Mantur	purge data from tenant 1 DB(***********)	<EMAIL>
209928	Resolved	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209931	Closed	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209946	Closed	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209990	Closed	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
210015	Resolved	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
210026	Resolved	2023-05-08	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209434	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209438	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209468	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209476	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209491	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209515	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209531	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209535	Closed	2023-05-07	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209551	Closed	2023-05-07	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in warning state	<EMAIL>
209570	Closed	2023-05-07	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_process_locking_state in warning state	<EMAIL>
209588	Closed	2023-05-07	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in critical state	<EMAIL>
209602	Closed	2023-05-07	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209625	Closed	2023-05-07	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209626	Closed	2023-05-07	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209628	Closed	2023-05-07	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209653	Closed	2023-05-07	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208941	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208945	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208950	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208951	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209040	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209047	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209055	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209058	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209063	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209064	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209066	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209067	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209068	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209070	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209071	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209081	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209084	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209089	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209091	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209093	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209095	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209104	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209107	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209114	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209115	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209117	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209118	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
209124	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209131	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209134	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209144	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_innodb_history_length in critical state	<EMAIL>
209145	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209146	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
209150	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209154	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209157	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209159	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209161	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209164	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209165	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209167	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209172	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209175	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209179	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209185	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209187	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209189	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209190	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209191	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
209194	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209199	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209201	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209203	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209209	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209237	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209242	Closed	2023-05-06	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209244	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209247	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209252	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209255	Closed	2023-05-06	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209256	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209263	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209264	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209266	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209269	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209270	Resolved	2023-05-06	Ashok kumar T	purge data from tenant 1 DB(***********)	<EMAIL>
209271	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209272	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209275	Resolved	2023-05-06	Ramasamy R	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209284	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209286	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209288	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209293	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209295	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209296	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209297	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209298	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209303	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209306	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209326	Closed	2023-05-06	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209328	Closed	2023-05-06	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209329	Closed	2023-05-06	Jeyaraj.j	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209403	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
209414	Closed	2023-05-06	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
209423	Closed	2023-05-06	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208360	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208362	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208365	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208366	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208369	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208370	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208371	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208372	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208376	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208379	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208390	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208402	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208408	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208409	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208411	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208436	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208447	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208448	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208498	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208502	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208504	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208506	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208514	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208522	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208525	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208526	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208529	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208530	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208532	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208534	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208535	Closed	2023-05-05	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208536	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208537	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208540	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208541	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208549	Closed	2023-05-05	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208711	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208712	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208715	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208718	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208722	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208724	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208730	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208732	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208734	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208736	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208738	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208745	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208748	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208756	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208757	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208758	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208769	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208772	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208774	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208777	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208781	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208786	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208795	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208799	Closed	2023-05-05	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208800	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208803	Closed	2023-05-05	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208819	Closed	2023-05-05	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208836	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208857	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208858	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208860	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208864	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208865	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208869	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208870	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208873	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208876	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208878	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208879	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208887	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208888	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208889	Closed	2023-05-05	Karthick K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208895	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208902	Closed	2023-05-05	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208926	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208930	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208934	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208936	Closed	2023-05-05	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207685	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207688	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207691	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207716	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207718	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207719	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207722	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207751	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207828	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207849	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207851	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207852	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207862	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207869	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207871	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207872	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207874	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207875	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207876	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207877	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207879	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207880	Closed	2023-05-04	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207884	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207885	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207892	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207896	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207900	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207907	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207909	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207911	Resolved	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207917	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207923	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207926	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207927	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207931	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207932	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207933	Closed	2023-05-04	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207934	Closed	2023-05-04	Rajesh M	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207939	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207940	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207941	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207944	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207945	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207946	Closed	2023-05-04	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207973	Closed	2023-05-04	Vijayalakshmi Metla	Monthly Database Report - UserExperior | April - 2023	<EMAIL>
208100	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208113	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208120	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208124	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208125	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208129	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208130	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208138	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208144	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208155	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208157	Resolved	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208158	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208160	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208161	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208165	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208168	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208173	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208176	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208179	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208187	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208192	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208194	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208197	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208202	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208205	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208209	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208214	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208217	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208218	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208222	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208223	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208226	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208230	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208236	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208240	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208243	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208245	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208248	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208250	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208252	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208253	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208255	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208258	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208265	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208267	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208268	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208270	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208274	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208276	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208278	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208280	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208282	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208287	Resolved	2023-05-04	Ashok kumar T	purge data from tenant 1 DB(***********) (table:ue_events_base )	<EMAIL>
208288	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208289	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208292	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208299	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208302	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208304	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208307	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208309	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208312	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208315	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208318	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208319	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208321	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208322	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208324	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208329	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208337	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208340	Closed	2023-05-04	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208344	Closed	2023-05-04	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208347	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208348	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208349	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
208351	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
208352	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208354	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
208355	Closed	2023-05-04	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207198	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207202	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207205	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207206	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207236	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207237	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207326	Resolved	2023-05-03	Athistalakshmi.K	Recover database free space in tenant 2(**********)	<EMAIL>
207448	Closed	2023-05-03	Chandra Mohan	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207453	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207454	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207455	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207456	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207457	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207458	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207461	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207462	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207464	Closed	2023-05-03	Komban B	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207465	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207495	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207499	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207522	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207531	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207538	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207544	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207549	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207569	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207646	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207647	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207648	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207649	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207650	Closed	2023-05-03	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207652	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207653	Closed	2023-05-03	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207671	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207672	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207675	Closed	2023-05-03	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207677	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207679	Closed	2023-05-03	Maha Lakshmi Ganapathineedi	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206719	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206721	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206722	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206723	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206725	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206726	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206727	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206728	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206768	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206769	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206772	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206776	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206781	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206810	Resolved	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206811	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206815	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206816	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206817	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206818	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206820	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206821	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206823	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206825	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206830	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206837	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206839	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206866	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206885	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206886	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206887	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206889	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206892	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206893	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206896	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206897	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206901	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206911	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206913	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206914	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206915	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206917	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206925	Closed	2023-05-02	Paul Meriton Issac	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206948	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206949	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
206956	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206959	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
206960	Closed	2023-05-02	Meghasai Bodavula	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206965	Resolved	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
206998	Resolved	2023-05-02	Sharmika Bhogadi	purge data from tenant 2 DB(**********)	<EMAIL>
207003	Resolved	2023-05-02	Vinitha G	purge data from tenant 1 DB(***********)	<EMAIL>
207012	Waiting on Customer	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207019	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207023	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207030	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207033	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207039	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207066	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207081	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207083	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207089	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207099	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207102	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207106	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207111	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207112	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207115	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207121	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207124	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207126	Closed	2023-05-02	Vijayalakshmi Metla	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207128	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207129	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207141	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207143	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207146	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207149	Closed	2023-05-02	Sangeetha K	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207151	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207155	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207159	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207173	Closed	2023-05-02	janaki kollipara	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207179	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in warning state	<EMAIL>
207183	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207184	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207185	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in recovery state	<EMAIL>
207186	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207188	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207190	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
207191	Closed	2023-05-02	Sai Kumar	userexperior | userexperior_ue_tenant1_source_mysql | mysql_active_thread in critical state	<EMAIL>
 >>> 
2023-05-10 14:54:06,519 INFO 1683710635048735000PSq Common.analyse_response, from node :  >>> mysql: [Warning] Using a password on the command line interface can be insecure.
2023-05-26 14:42:55,937 INFO 1685092264564754000CSn event url is empty >>> 
2023-05-26 14:42:56,909 ERROR 1685092264564754000CSn Loadconfig error in keyserver request  , data :{} >>> HTTPConnectionPool(host='keyserver.mydbops.com', port=80): Max retries exceeded with url: /get_details_for_send_mail?user_token=481c12be49cd7e8ae757a44b0459320b&client_name=userexperior (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x163998220>, 'Connection to keyserver.mydbops.com timed out. (connect timeout=30)'))

2023-05-26 14:42:57,582 ERROR 1685092264564754000CSn {'duration': '0:00:32.192759', 'type': 'Application', 'tag': 'Weekly_report', 'name': 'mydbhealthreport', 'executionID': '1685092264564754000CSn', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'E', 'message': "HTTPConnectionPool(host='keyserver.mydbops.com', port=80): Max retries exceeded with url: /get_details_for_send_mail?user_token=481c12be49cd7e8ae757a44b0459320b&client_name=userexperior (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x163998220>, 'Connection to keyserver.mydbops.com timed out. (connect timeout=30)'))", 'startTime': '2023-05-26 14:41:04', 'endTime': '2023-05-26 14:41:36', 'output': {'client': 'userexperior', 'client_env': 'userexperior', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_execution_type': 'Weekly', 'connection_type': [], 'db_type': [], 'inventory': {}, 'from_time': None, 'to_time': None, 'server_names': [], 'groups': []}, 'version': '2.0.10', 'error': 'Loadconfig error in keyserver request ', 'trace': '~   File "/opt/homebrew/lib/python3.9/site-packages/requests/adapters.py", line 507, in send\n    raise ConnectTimeout(e, request=request)\n'} >>> 

2023-05-26 14:46:01,089 INFO 1685092560102165000tdO Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-26 14:46:01,090 INFO 1685092560102165000tdO Report going to execute for -> weekly_bastion >>> 
2023-05-26 14:46:01,090 INFO 1685092560102165000tdO Going TO PMM Server Login  >>> 
2023-05-26 14:47:04,860 INFO 1685092623927041000gNA Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-26 14:47:04,860 INFO 1685092623927041000gNA Report going to execute for -> weekly_bastion >>> 
2023-05-26 14:47:04,860 INFO 1685092623927041000gNA Going TO PMM Server Login  >>> 
2023-05-26 14:47:24,356 INFO 1685092643400610000djd Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-26 14:47:24,356 INFO 1685092643400610000djd Report going to execute for -> weekly_bastion >>> 
2023-05-26 14:47:24,356 INFO 1685092643400610000djd Going TO PMM Server Login  >>> 
2023-05-26 14:48:05,360 INFO 1685092684357956000iva Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-26 14:48:05,360 INFO 1685092684357956000iva Report going to execute for -> weekly_bastion >>> 
2023-05-26 14:48:05,360 INFO 1685092684357956000iva Going TO PMM Server Login  >>> 
2023-05-26 14:48:14,521 INFO 1685092684357956000iva  >>> 
2023-05-26 14:48:16,350 ERROR 1685092684357956000iva exception when try to PMM login at internal hook , data :{} >>> ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))

2023-05-26 14:48:16,350 ERROR 1685092684357956000iva {'duration': '0:00:01.981529', 'type': 'Application', 'tag': 'Weekly_report', 'name': 'mydbhealthreport', 'executionID': '1685092684357956000iva', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'E', 'message': "('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))", 'startTime': '2023-05-26 14:48:04', 'endTime': '2023-05-26 14:48:06', 'output': {'client': 'userexperior', 'client_env': 'userexperior', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_execution_type': 'Weekly', 'connection_type': ['ssh'], 'db_type': ['mysql'], 'inventory': {'weekly': {'bastion': {'connection_type': 'ssh', 'db_type': 'mysql', 'node_name': 'vetriveln_test', 'enabled_stats': ['ticket_stats', 'critical_stats', 'server_stats', 'sql_stats', 'graph_stats', 'qan_analysis'], 'ssh_name': 'bastion'}}}, 'from_time': '2023-05-18', 'to_time': '2023-05-25', 'server_names': ['bastion'], 'groups': ['weekly']}, 'version': '2.0.10', 'error': 'exception when try to PMM login at internal hook', 'trace': '~   File "/opt/homebrew/lib/python3.9/site-packages/requests/adapters.py", line 501, in send\n    raise ConnectionError(err, request=request)\n'} >>> 

2023-05-26 15:05:05,882 INFO 1685093704905489000qUN Report for -> weekly_bastion , going to check for schedule >>> 
2023-05-26 15:05:05,882 INFO 1685093704905489000qUN Report going to execute for -> weekly_bastion >>> 
2023-05-26 15:05:05,882 INFO 1685093704905489000qUN Going TO PMM Server Login  >>> 
2023-05-26 15:05:25,426 INFO 1685093704905489000qUN  >>> 
2023-05-26 15:05:28,633 ERROR 1685093704905489000qUN exception when try to PMM login at internal hook , data :{} >>> ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))

2023-05-26 15:05:29,082 ERROR 1685093704905489000qUN {'duration': '0:00:01.640515', 'type': 'Application', 'tag': 'Weekly_report', 'name': 'mydbhealthreport', 'executionID': '1685093704905489000qUN', 'taskID': '100', 'serverConfigID': '2', 'templateName': 'test-report', 'hostName': '', 'taskStatus': 'E', 'message': "('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))", 'startTime': '2023-05-26 15:05:04', 'endTime': '2023-05-26 15:05:06', 'output': {'client': 'userexperior', 'client_env': 'userexperior', 'client_email': '<EMAIL>', 'email_enabled': True, 'email_provider': 'ses', 'report_execution_type': 'Weekly', 'connection_type': ['ssh'], 'db_type': ['mysql'], 'inventory': {'weekly': {'bastion': {'connection_type': 'ssh', 'db_type': 'mysql', 'node_name': 'vetriveln_test', 'enabled_stats': ['ticket_stats', 'critical_stats', 'server_stats', 'sql_stats', 'graph_stats', 'qan_analysis'], 'ssh_name': 'bastion'}}}, 'from_time': '2023-05-18', 'to_time': '2023-05-25', 'server_names': ['bastion'], 'groups': ['weekly']}, 'version': '2.0.10', 'error': 'exception when try to PMM login at internal hook', 'trace': '~   File "/opt/homebrew/lib/python3.9/site-packages/requests/adapters.py", line 501, in send\n    raise ConnectionError(err, request=request)\n'} >>> 

