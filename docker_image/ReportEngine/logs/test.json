{"myDbHealthMetrics": {"systemStats": [{"host": "***********:27017", "hostUptime": "3.97 days", "ram": "1.75 GB", "cpu": 2, "architecture": "x86_64", "memoryUsed": "172 MB", "osType": "Linux", "osDistribution": "CentOS Linux release 8.2.2004 (Core) ", "osRelease": "Kernel 4.18.0-193.19.1.el8_2.x86_64", "osKernelVersion": "4.18.0-193.19.1.el8_2.x86_64"}, {"host": "***********:27018", "hostUptime": "3.97 days", "ram": "1.75 GB", "cpu": 2, "architecture": "x86_64", "memoryUsed": "173 MB", "osType": "Linux", "osDistribution": "CentOS Linux release 8.2.2004 (Core) ", "osRelease": "Kernel 4.18.0-193.19.1.el8_2.x86_64", "osKernelVersion": "4.18.0-193.19.1.el8_2.x86_64"}, {"host": "***********:27019", "hostUptime": "3.97 days", "ram": "1.75 GB", "cpu": 2, "architecture": "x86_64", "memoryUsed": "167 MB", "osType": "Linux", "osDistribution": "CentOS Linux release 8.2.2004 (Core) ", "osRelease": "Kernel 4.18.0-193.19.1.el8_2.x86_64", "osKernelVersion": "4.18.0-193.19.1.el8_2.x86_64"}], "instanceLevelStats": [{"host": "***********:27017", "numaEnabled": false, "maxOpenFiles": 64000, "mongoUser": "mongod", "dirtyRatio": "30", "dirtyBackgroundRatio": "10", "swappiness": "30", "logRotate": "disabled", "transparentHugepage": "always", "defrag": "madvise", "diskVolumeName": "/dev/nvme0n1p1", "diskMountPoint": "/", "Partition": "/var/lib/mongo1/", "diskOptions": "rw,relatime", "diskFtype": "xfs"}, {"host": "***********:27018", "numaEnabled": false, "maxOpenFiles": 64000, "mongoUser": "mongod", "dirtyRatio": "30", "dirtyBackgroundRatio": "10", "swappiness": "30", "logRotate": "disabled", "transparentHugepage": "always", "defrag": "madvise", "diskVolumeName": "/dev/nvme0n1p1", "diskMountPoint": "/", "Partition": "/var/lib/mongo2/", "diskOptions": "rw,relatime", "diskFtype": "xfs"}, {"host": "***********:27019", "numaEnabled": false, "maxOpenFiles": 64000, "mongoUser": "mongod", "dirtyRatio": "30", "dirtyBackgroundRatio": "10", "swappiness": "30", "logRotate": "disabled", "transparentHugepage": "always", "defrag": "madvise", "diskVolumeName": "/dev/nvme0n1p1", "diskMountPoint": "/", "Partition": "/var/lib/mongo/", "diskOptions": "rw,relatime", "diskFtype": "xfs"}], "mongoMetrics": [{"host": "***********:27017", "uptime": "0.65 days", "version": "5.0.16", "memoryUsed": "172 MB", "sortMemory": "100 MB", "authentication": "enabled", "mongoPort": 27017, "tls": false, "ssl": false, "storageEngine": "wiredTiger", "compressionType": "snappy", "status": "Replica Set", "replicaName": "Destination", "replState": "Secondary", "replMember": 3, "tags": null, "oplogSize": "990 MB", "oplogUsed": "996.24 MB", "oplogCount": 1156713, "oplogDuration": "94.62 hours", "keyFile": "/etc/mongo.keyfile"}, {"host": "***********:27018", "uptime": "0.65 days", "version": "5.0.16", "memoryUsed": "173 MB", "sortMemory": "100 MB", "authentication": "enabled", "mongoPort": 27018, "tls": false, "ssl": false, "storageEngine": "wiredTiger", "compressionType": "snappy", "status": "Replica Set", "replicaName": "Destination", "replState": "Secondary", "replMember": 3, "tags": null, "oplogSize": "990 MB", "oplogUsed": "994.11 MB", "oplogCount": 1154146, "oplogDuration": "94.62 hours", "keyFile": "/etc/mongo.keyfile"}, {"host": "***********:27019", "uptime": "0.01 days", "version": "5.0.16", "memoryUsed": "167 MB", "sortMemory": "100 MB", "authentication": "enabled", "mongoPort": 27019, "tls": false, "ssl": false, "storageEngine": "wiredTiger", "compressionType": "snappy", "status": "Replica Set", "replicaName": "Destination", "replState": "Secondary", "replMember": 3, "tags": null, "oplogSize": "4.61 GB", "oplogUsed": "573.44 KB", "oplogCount": 5645, "oplogDuration": "15.84 hours", "keyFile": "/etc/mongo.keyfile"}], "Recommendations": [{"host": "***********:27017", "instanceLevelRecommendations": {"DirtyRatio": {"Recommendation": "We are always recommending to maintain the DirtyRatio less than 15.", "Reason": ["As a member of the Mydbops MongoDB database support team, we recommend configuring the Dirty Ratio for your MongoDB deployment to ensure optimal performance and stability of your database.", "Dirty Ratio is a kernel parameter that controls the amount of memory used for file system caching, and it can significantly impact the I / O performance of your database. ", "Setting a higher Dirty Ratio can improve the read performance of your database, while setting a lower ratio can improve the write performance.", "It is important to consider factors such as the available memory, the expected workload, and the type of storage when setting the Dirty Ratio. We recommend setting the Dirty Ratio to a value that balances the read and write performance and ensures that there is enough memory available for other system processes.", "Our support team can assist you with configuring the Dirty Ratio for your MongoDB deployment and answer any questions or concerns you may have during the process."]}, "Swappiness": {"Recommendation": "We are always recommending to maintain the swappiness less than 10.", "Reason": ["As a member of the Mydbops MongoDB database support team, we recommend configuring the swappiness setting for your MongoDB deployment to ensure optimal performance and stability of your database.", "Swappiness is a kernel parameter that controls the degree to which the Linux kernel swaps out processes from memory to disk when under memory pressure.", "This parameter can have a significant impact on the performance of your database, as swapping out active MongoDB processes can result in high I/O wait times and degraded query performance. We recommend setting the swappiness to a value that balances the memory usage of your MongoDB deployment and the other system processes running on the server.", "A low swappiness value can help ensure that MongoDB processes are not excessively swapped out to disk, while a high swappiness value can help ensure that other system processes have access to sufficient memory resources.", "Our support team can assist you with configuring the swappiness setting for your MongoDB deployment and answer any questions or concerns you may have during the process."]}, "Logrotate": {"Recommendation": "We are always recommending to enable the logRotation with minimum of 15 days of log retention. ", "Reason": ["As a member of the Mydbops MongoDB database support team, we strongly recommend implementing log rotation for your MongoDB deployment to ensure optimal performance and stability.", "Log rotation is the process of automatically archiving and compressing old log files and creating new ones to avoid filling up disk space and causing potential system issues.", "MongoDB generates various types of log files, including diagnostic logs, query logs, and profiling logs, which can quickly consume significant amounts of disk space if not managed properly.", "By implementing log rotation, you can ensure that your log files are efficiently managed, reducing the risk of performance degradation and data loss due to disk space limitations.", "Our support team can help you set up and configure log rotation for your MongoDB deployment and answer any questions or concerns you may have during the process."]}, "TransparentHugepage": {"Recommendation": "We are always recommending to maintain THP in 'never' state.", "Reason": ["As a member of the MongoDB database support team, we recommend configuring the Transparent Huge Pages (THP) setting for your MongoDB deployment to ensure optimal performance and stability of your database. THP is a Linux kernel feature that enables the use of larger memory pages for improved performance. ", "However, THP can also cause performance issues with MongoDB, as it can lead to increased memory usage and higher CPU utilization. We recommend disabling THP for your MongoDB deployment to avoid these issues. ", "Disabling THP can help ensure that MongoDB is not affected by any unpredictable changes in the memory management system and that it has consistent access to memory resources. ", "Our support team can assist you with disabling the THP setting for your MongoDB deployment and answer any questions or concerns you may have during the process."]}, "Defrag": {"Recommendation": "We are always recommending to maintain defrag in 'never' state.", "Reason": [" As a member of the Mydbops MongoDB database support team, we recommend configuring the Transparent Huge Pages (THP) defragmentation setting for your MongoDB deployment to ensure optimal performance and stability of your database. ", "THP defragmentation is a Linux kernel feature that reclaims fragmented memory used by THP. In some cases, THP fragmentation can cause performance issues with MongoDB, as it can lead to increased memory usage and higher CPU utilization. ", "Enabling THP defragmentation can help ensure that the memory used by THP is efficiently utilized and that MongoDB has consistent access to memory resources. We recommend enabling THP defragmentation for your MongoDB deployment to avoid these issues. ", "Our support team can assist you with enabling THP defragmentation for your MongoDB deployment and answer any questions or concerns you may have during the process.As a member of the MongoDB database support team, we recommend configuring the Transparent Huge Pages (THP) defragmentation setting for your MongoDB deployment to ensure optimal performance and stability of your database. ", "THP defragmentation is a Linux kernel feature that reclaims fragmented memory used by THP. In some cases, THP fragmentation can cause performance issues with MongoDB, as it can lead to increased memory usage and higher CPU utilization. ", "Enabling THP defragmentation can help ensure that the memory used by THP is efficiently utilized and that MongoDB has consistent access to memory resources. We recommend enabling THP defragmentation for your MongoDB deployment to avoid these issues. ", "Our support team can assist you with enabling THP defragmentation for your MongoDB deployment and answer any questions or concerns you may have during the process."]}, "diskMountPoint": {"Recommendation": "We are always recommending to maintain the mongodb data directory in a separate mount point.", "Reason": ["As a member of the Mydbops MongoDB database support team, we strongly recommend storing your MongoDB data files on a dedicated disk mount point to ensure optimal performance and stability of your deployment. ", "Storing data files on a dedicated disk mount point provides several benefits, including improved I/O performance and increased resilience to disk failures. When selecting a mount point, it is important to consider factors such as the expected size of your database, the number of concurrent connections, and the disk type and size. ", "It is also important to ensure that the disk is formatted using a file system that is compatible with your operating system and that it has adequate disk space to accommodate your data growth. ", "Our support team is available to assist you with selecting and configuring a suitable disk mount point for your MongoDB deployment and to answer any questions or concerns you may have during the process."]}}, "mongoRecommendations": {"MongoPort": {"Recommendation": "We are recommending to use other ports instead of default one.", "Reason": ["As a member of the Mydbops MongoDB database support team, we strongly recommend avoiding the use of MongoDB's default port (27017) for your production deployment, as it can expose your database to security risks such as unauthorized access, data tampering, and denial-of-service attacks. ", "Attackers can easily scan the internet for MongoDB servers running on the default port, and attempt to exploit known vulnerabilities or perform brute-force attacks to gain access to your data. We recommend changing the default port to a non-standard port number and to ensure that your firewall rules only allow access to authorized IP addresses. ", "Our support team can assist you with changing the default port and implementing additional security measures to protect your MongoDB deployment, and answer any questions or concerns you may have during the process."]}, "TransitEncryption": {"Recommendation": "We are recommending to enable the transit encryption.", "Reason": ["As a member of the Mydbops MongoDB database support team, we strongly recommend enabling Transit Encryption for your MongoDB deployment to ensure secure data transmission between your application and the database server. ", "Transit Encryption encrypts network traffic between the client and server using SSL/TLS protocols, protecting data from interception and tampering. ", "It is recommended to use the latest version of SSL/TLS protocols supported by your MongoDB deployment, and to carefully configure your certificate authorities to avoid potential security vulnerabilities. ", "Our support team can assist you with enabling Transit Encryption and configuring SSL/TLS protocols and certificate authorities, and answer any questions or concerns you may have during the process. ", "By enabling Transit Encryption, you can ensure the confidentiality and integrity of your data, and comply with security regulations and best practices."]}, "CompressionType": {"Recommendation": "We are recommending to use the zlib compression as the default compression.", "Reason": ["As a database support team, we recommend using either Zlib or Zstandard (Zstd) compression algorithms in WiredTiger for MongoDB, depending on your specific requirements. ", "Zlib is a widely-used compression algorithm that offers a good balance between compression ratio and speed, making it a good choice for general-purpose workloads. ", "By carefully selecting the appropriate compression algorithm and tuning compression settings, We can optimize storage usage and reduce costs while ensuring that your workload meets performance and latency requirements. ", "We are available to assist you with any questions or concerns you may have during this process."]}, "TagSet": {"Recommendation": "We are recommending that to add any tag set based on the requirement.", "Reason": ["As a member of the Mydbops MongoDB database support team, we recommend using replica tags in MongoDB to help control the distribution of data across your replica set. ", "By using replica tags, you can assign tags to members of the replica set, indicating their purpose or location. This allows you to control which members of the set hold specific data or perform specific tasks, such as running analytics workloads or handling backup operations. ", "Replica tags also allow you to ensure that read and write operations are distributed evenly across your replica set, optimizing performance and minimizing the risk of downtime. ", "It is important to carefully plan and document your use of replica tags to ensure that they are used effectively and consistently across your deployment. By implementing replica tags, you can achieve greater control and flexibility in your MongoDB replica set and optimize your database's performance and availability. ", "We are available to assist you with any questions or concerns you may have during this process."]}}}, {"host": "***********:27018", "mongoRecommendations": {"TransitEncryption": {"Recommendation": "We are recommending to enable the transit encryption.", "Reason": ["As a member of the Mydbops MongoDB database support team, we strongly recommend enabling Transit Encryption for your MongoDB deployment to ensure secure data transmission between your application and the database server. ", "Transit Encryption encrypts network traffic between the client and server using SSL/TLS protocols, protecting data from interception and tampering. ", "It is recommended to use the latest version of SSL/TLS protocols supported by your MongoDB deployment, and to carefully configure your certificate authorities to avoid potential security vulnerabilities. ", "Our support team can assist you with enabling Transit Encryption and configuring SSL/TLS protocols and certificate authorities, and answer any questions or concerns you may have during the process. ", "By enabling Transit Encryption, you can ensure the confidentiality and integrity of your data, and comply with security regulations and best practices."]}, "CompressionType": {"Recommendation": "We are recommending to use the zlib compression as the default compression.", "Reason": ["As a database support team, we recommend using either Zlib or Zstandard (Zstd) compression algorithms in WiredTiger for MongoDB, depending on your specific requirements. ", "Zlib is a widely-used compression algorithm that offers a good balance between compression ratio and speed, making it a good choice for general-purpose workloads. ", "By carefully selecting the appropriate compression algorithm and tuning compression settings, We can optimize storage usage and reduce costs while ensuring that your workload meets performance and latency requirements. ", "We are available to assist you with any questions or concerns you may have during this process."]}, "TagSet": {"Recommendation": "We are recommending that to add any tag set based on the requirement.", "Reason": ["As a member of the Mydbops MongoDB database support team, we recommend using replica tags in MongoDB to help control the distribution of data across your replica set. ", "By using replica tags, you can assign tags to members of the replica set, indicating their purpose or location. This allows you to control which members of the set hold specific data or perform specific tasks, such as running analytics workloads or handling backup operations. ", "Replica tags also allow you to ensure that read and write operations are distributed evenly across your replica set, optimizing performance and minimizing the risk of downtime. ", "It is important to carefully plan and document your use of replica tags to ensure that they are used effectively and consistently across your deployment. By implementing replica tags, you can achieve greater control and flexibility in your MongoDB replica set and optimize your database's performance and availability. ", "We are available to assist you with any questions or concerns you may have during this process."]}}}, {"host": "***********:27019", "mongoRecommendations": {"TransitEncryption": {"Recommendation": "We are recommending to enable the transit encryption.", "Reason": ["As a member of the Mydbops MongoDB database support team, we strongly recommend enabling Transit Encryption for your MongoDB deployment to ensure secure data transmission between your application and the database server. ", "Transit Encryption encrypts network traffic between the client and server using SSL/TLS protocols, protecting data from interception and tampering. ", "It is recommended to use the latest version of SSL/TLS protocols supported by your MongoDB deployment, and to carefully configure your certificate authorities to avoid potential security vulnerabilities. ", "Our support team can assist you with enabling Transit Encryption and configuring SSL/TLS protocols and certificate authorities, and answer any questions or concerns you may have during the process. ", "By enabling Transit Encryption, you can ensure the confidentiality and integrity of your data, and comply with security regulations and best practices."]}, "CompressionType": {"Recommendation": "We are recommending to use the zlib compression as the default compression.", "Reason": ["As a database support team, we recommend using either Zlib or Zstandard (Zstd) compression algorithms in WiredTiger for MongoDB, depending on your specific requirements. ", "Zlib is a widely-used compression algorithm that offers a good balance between compression ratio and speed, making it a good choice for general-purpose workloads. ", "By carefully selecting the appropriate compression algorithm and tuning compression settings, We can optimize storage usage and reduce costs while ensuring that your workload meets performance and latency requirements. ", "We are available to assist you with any questions or concerns you may have during this process."]}, "TagSet": {"Recommendation": "We are recommending that to add any tag set based on the requirement.", "Reason": ["As a member of the Mydbops MongoDB database support team, we recommend using replica tags in MongoDB to help control the distribution of data across your replica set. ", "By using replica tags, you can assign tags to members of the replica set, indicating their purpose or location. This allows you to control which members of the set hold specific data or perform specific tasks, such as running analytics workloads or handling backup operations. ", "Replica tags also allow you to ensure that read and write operations are distributed evenly across your replica set, optimizing performance and minimizing the risk of downtime. ", "It is important to carefully plan and document your use of replica tags to ensure that they are used effectively and consistently across your deployment. By implementing replica tags, you can achieve greater control and flexibility in your MongoDB replica set and optimize your database's performance and availability. ", "We are available to assist you with any questions or concerns you may have during this process."]}}}]}, "myDbDistribution": [{"dbname": "codeculture", "collCnt": 2, "idxCnt": 2, "idxSize": "78.97 MB", "objects": 90657, "dataSize": "47.48 MB", "avgDocSize": "549.17 B", "strSize": "450.79 MB"}, {"dbname": "_mongopush", "collCnt": 1, "idxCnt": 3, "idxSize": "60 KB", "objects": 7, "dataSize": "97.67 KB", "avgDocSize": "13.95 KB", "strSize": "76 KB"}, {"dbname": "SampleCollections", "collCnt": 1, "idxCnt": 1, "idxSize": "24 KB", "objects": 345, "dataSize": "14.36 KB", "avgDocSize": "42.61 B", "strSize": "24 KB"}, {"dbname": "Test_hellow", "collCnt": 1, "idxCnt": 1, "idxSize": "20 KB", "objects": 6, "dataSize": "198 B", "avgDocSize": "33 B", "strSize": "20 KB"}, {"dbname": "Test&hellow", "collCnt": 2, "idxCnt": 2, "idxSize": "16 KB", "objects": 0, "dataSize": "0 Bytes", "avgDocSize": "0 Bytes", "strSize": "8 KB"}, {"dbname": "sample_airbnb", "collCnt": 1, "idxCnt": 10, "idxSize": "212 KB", "objects": 0, "dataSize": "0 Bytes", "avgDocSize": "0 Bytes", "strSize": "4.77 MB"}, {"dbname": "sample_analytics", "collCnt": 3, "idxCnt": 3, "idxSize": "84 KB", "objects": 0, "dataSize": "0 Bytes", "avgDocSize": "0 Bytes", "strSize": "9.24 MB"}, {"dbname": "sample_geospatial", "collCnt": 1, "idxCnt": 2, "idxSize": "324 KB", "objects": 0, "dataSize": "0 Bytes", "avgDocSize": "0 Bytes", "strSize": "728 KB"}, {"dbname": "sample_mflix", "collCnt": 5, "idxCnt": 9, "idxSize": "6.61 MB", "objects": 0, "dataSize": "0 Bytes", "avgDocSize": "0 Bytes", "strSize": "8.12 MB"}, {"dbname": "sample_restaurants", "collCnt": 2, "idxCnt": 2, "idxSize": "28 KB", "objects": 0, "dataSize": "0 Bytes", "avgDocSize": "0 Bytes", "strSize": "1.84 MB"}, {"dbname": "sample_supplies", "collCnt": 1, "idxCnt": 1, "idxSize": "8 KB", "objects": 0, "dataSize": "0 Bytes", "avgDocSize": "0 Bytes", "strSize": "4 KB"}, {"dbname": "Total", "collCnt": 20, "idxCnt": 36, "idxSize": "86.34 MB", "objects": 91015, "dataSize": "47.59 MB", "avgDocSize": "14.56 KB", "strSize": "475.61 MB"}], "myDbConsiderableCollections": {"topCollections": [{"coll": "cms_user_activities_ingestion", "dbName": "codeculture", "dbSize": "47.48 MB", "dbStorageSize": "450.79 MB", "collSize": "47.48 MB", "collStorageSize": "176.41 MB", "collTotalIndex": 1, "collIndexSize": "39.18 MB", "fragmentation": "32 KB", "fragmentationRatio": "0.01%", "archivalRecommendation": false, "firstDocumentDate": "2022-07-25"}, {"coll": "tasks", "dbName": "_mongopush", "dbSize": "97.67 KB", "dbStorageSize": "76 KB", "collSize": "97.67 KB", "collStorageSize": "76 KB", "collTotalIndex": 3, "collIndexSize": "60 KB", "fragmentation": "0 Bytes", "fragmentationRatio": "0.00%", "archivalRecommendation": false}, {"coll": "food", "dbName": "SampleCollections", "dbSize": "14.36 KB", "dbStorageSize": "24 KB", "collSize": "14.36 KB", "collStorageSize": "24 KB", "collTotalIndex": 1, "collIndexSize": "24 KB", "fragmentation": "0 Bytes", "fragmentationRatio": "0.00%", "archivalRecommendation": false, "firstDocumentDate": "2023-01-20"}, {"coll": "test hel", "dbName": "Test_hellow", "dbSize": "198 B", "dbStorageSize": "20 KB", "collSize": "198 B", "collStorageSize": "20 KB", "collTotalIndex": 1, "collIndexSize": "20 KB", "fragmentation": "0 Bytes", "fragmentationRatio": "0.00%", "archivalRecommendation": false, "firstDocumentDate": "2023-02-09"}], "emptyColl": [{"dbName": "Test&hellow", "collection": "test hel"}, {"dbName": "Test&hellow", "collection": "test&hel"}, {"dbName": "codeculture", "collection": "cms_user_activities"}, {"dbName": "sample_airbnb", "collection": "listingsAndReviews"}, {"dbName": "sample_analytics", "collection": "accounts"}, {"dbName": "sample_analytics", "collection": "customers"}, {"dbName": "sample_analytics", "collection": "transactions"}, {"dbName": "sample_geospatial", "collection": "shipwrecks"}, {"dbName": "sample_mflix", "collection": "comments"}, {"dbName": "sample_mflix", "collection": "movies"}, {"dbName": "sample_mflix", "collection": "sessions"}, {"dbName": "sample_mflix", "collection": "theaters"}, {"dbName": "sample_mflix", "collection": "users"}, {"dbName": "sample_restaurants", "collection": "neighborhoods"}, {"dbName": "sample_restaurants", "collection": "restaurants"}, {"dbName": "sample_supplies", "collection": "sales"}]}, "myDbOpCounters": [{"Insert": 0, "Query": 464, "Update": 17, "Delete": 199, "Getmore": 14279, "Command": 87913}, {"Insert": 0, "Query": 171, "Update": 0, "Delete": 0, "Getmore": 3306, "Command": 65939}, {"Insert": 0, "Query": 76, "Update": 0, "Delete": 0, "Getmore": 0, "Command": 3381}], "mydbUsers": {"rootUsers": [{"db": "admin", "user": "mydb"}, {"db": "admin", "user": "spiderman"}], "dropUsers": [], "dummyUsers": [], "noAccessDBs": []}, "myDbIndexes": {"DuplicateIndexes": [{"DBName": "sample_airbnb", "CollName": "listingsAndReviews", "DBStrSize": "4.77 MB", "CollStSize": "4.77 MB", "IdxCnt": 10, "Pre_Idx": {"_id": 1}, "DpIdxCnt": 2, "DpIdx": [{"_id": 1, "a.qty1": 1}, {"_id": 1, "a.qty": 1}]}]}, "myDbUnusedIndex": [], "Rating": "64 %"}