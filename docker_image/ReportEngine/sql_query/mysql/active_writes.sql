set session sql_mode='';
select sum(rows_inserted+rows_updated+rows_deleted) into @total_writes from sys.schema_table_statistics;
select
  concat(table_schema, '.', table_name) as 'Table',
  (rows_inserted+rows_updated+rows_deleted) as 'Writes',
  ifnull(((rows_inserted+rows_updated+rows_deleted)*100)/@total_writes,0) as 'Percentage'
from
  sys.schema_table_statistics
where 
  table_schema not in ('information_schema', 'sys', 'mysql', 'performance_schema')
order by 2 desc limit 10;