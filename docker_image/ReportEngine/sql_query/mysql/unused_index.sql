select 
  concat(ui.object_schema, '.', ui.object_name) 'Table Name',
  ui.index_name 'Index Name',
  (ifnull(iis.stat_value, 0) * @@innodb_page_size) 'Size' /*bytes*/
from (
  select 
    object_schema,
    object_name,
    index_name
  from 
    sys.schema_unused_indexes
  where 
    object_schema not in('information_schema','sys','mysql','performance_schema') 
  order by 
    object_schema,object_name,index_name
) ui left join  
  mysql.innodb_index_stats iis
on 
  ui.object_schema=iis.database_name and
  ui.object_name=iis.table_name and
  ui.index_name=iis.index_name and
  iis.stat_name='size'
order by 
  iis.stat_value desc limit 20;