select
  tab as 'Table',
  cs as 'Current Size' /*bytes*/,
  fs as 'Fragmentation' /*bytes*/,
  round((fs*100)/cs) as 'Percent %'
from (
  select
    concat(table_schema, '.', table_name) as tab,
    (data_length + index_length + data_free) as cs,
    data_free as fs
  from 
    information_schema.tables
  where
    engine = 'innodb' and
    table_schema not in ('information_schema', 'sys', 'mysql', 'performance_schema')) as get_fragment
where
  fs > (100*1024*1024) and round((fs*100)/cs) > 5
order by 3 desc limit 10;