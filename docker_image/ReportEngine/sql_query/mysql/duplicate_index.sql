select
  concat(ri.table_schema, '.', ri.table_name) 'Table Name',
  ri.redundant_index_name 'Index Name',
  ri.redundant_index_columns 'Index Column',
  (ifnull(iis.stat_value,0) * @@innodb_page_size) 'Size' /*bytes*/
from (
  select
    table_schema,
    table_name,
    redundant_index_name,
    redundant_index_columns
  from
    sys.schema_redundant_indexes
  where
    table_schema not in ('information_schema','sys','mysql','performance_schema')
  ) ri left join
  mysql.innodb_index_stats iis
on
  ri.table_schema=iis.database_name and ri.table_name=iis.table_name and ri.redundant_index_name=iis.index_name and iis.stat_name='size'
order by
  iis.stat_value desc limit 20;