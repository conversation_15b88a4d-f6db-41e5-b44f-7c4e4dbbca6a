select
  tab as 'Table',
  col as 'Column',
  cv as 'Current Value',
  ud as 'Appox. Usage Period (Days)',
  round(usg, 2) as 'Usage %'
from (
  select
    concat(table_schema, '.', table_name) as tab,
    concat(column_name, ' ', column_type, ' ', if(locate('unsigned', column_type)=0, 'signed', '')) as col,
    auto_increment as cv,
    datediff(now(), create_time) as ud,
    ((auto_increment * 100) / (case data_type when 'tinyint' then 255 when 'smallint' then 65535 when 'mediumint' then 16777215 when 'int' then 4294967295 when 'bigint' then 18446744073709551615 end >> if(locate('unsigned', column_type)>0,0,1))) as usg
  from
    information_schema.columns inner join
    information_schema.tables using (table_schema, table_name)
  where
    table_schema not in ('sys','mysql','information_schema','performance_schema') and
    table_type = 'BASE TABLE' and
    extra = 'auto_increment'
  order by 5 desc, 3 desc) as get_auto_inc_usage
where
  usg >= 70;