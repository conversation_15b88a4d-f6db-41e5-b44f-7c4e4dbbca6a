select
  user 'Username',
  host 'Hostname',
  if(max_user_connections=0,'No Limits',max_user_connections) 'concurrent connections allowed',
  if(max_connections=0,'No Limits',max_connections) 'connections allowed per hr',
  if(max_questions=0,'No Limits',max_questions) 'queries allowed per hr',
  if(max_updates=0,'No Limits',max_updates) 'updates allowed per hr'
from
  mysql.user
where
  (
  max_user_connections<>0 or
  max_connections<>0 or
  max_questions<>0 or
  max_updates<>0
  ) and
  user not in ('pmm');