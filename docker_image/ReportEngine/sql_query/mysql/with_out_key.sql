select
  concat(tab.table_schema, '.', tab.table_name) as 'Table',
  tab.engine as 'Storage Engine',
  (tab.data_length + tab.index_length + tab.data_free) as 'Size' /*bytes*/
from
  information_schema.tables as tab left join
  (
  select
    table_schema,
    table_name
  from
    information_schema.statistics
  group by
    table_schema, table_name, index_name
  having
    sum(case when non_unique=0 and nullable!='yes' then 1 else 0 end) = count(*)
  ) as stat on tab.table_schema = stat.table_schema and tab.table_name = stat.table_name
where
  tab.table_schema not in ('sys', 'mysql', 'information_schema', 'performance_schema') and
  tab.table_type = 'BASE TABLE' and
  stat.table_name is null
order by 3 desc;