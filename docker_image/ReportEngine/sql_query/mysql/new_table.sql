select
  concat(table_schema, '.', table_name) as 'Table',
  create_time as 'Created On',
  engine as 'Storage Engine',
  ifnull((data_length + index_length + data_free),0) as 'Size' /*bytes*/,
  table_rows as 'Approx. Row Count'
from
  information_schema.tables
where
  table_schema not in ('information_schema', 'sys', 'mysql', 'performance_schema') and
  create_time between '{{from_time}}' and '{{to_time}}'
order by 2;