set session sql_mode='';
select sum(rows_fetched) into @total_reads from sys.schema_table_statistics;
select
  concat(table_schema, '.', table_name) as 'Table',
  rows_fetched as 'Reads',
  ifnull((rows_fetched*100)/@total_reads,0) as 'Percentage'
from
  sys.schema_table_statistics
where
  table_schema not in ('information_schema', 'sys', 'mysql', 'performance_schema')
order by rows_fetched desc limit 10;