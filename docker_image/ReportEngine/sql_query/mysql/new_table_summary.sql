select
  table_schema 'Database',
  engine as 'Storage Engine',
  count(*) 'Count Of New Tables Created / Altered',
  ifnull(sum(data_length + index_length + data_free),0) as 'Size' /*bytes*/
from
  information_schema.tables
where
  table_schema not in ('information_schema', 'sys', 'mysql', 'performance_schema') and
  create_time between '{{from_time}}' and '{{to_time}}'
group by table_schema, engine order by 4;