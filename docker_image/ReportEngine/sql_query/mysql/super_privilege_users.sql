select
  user 'Username',
  host 'Hostname',
  if(super_priv='Y','Yes','-') 'Super Privilege',
  if(create_priv='Y','Yes','-') 'Create Access',
  if(drop_priv='Y','Yes','-') 'Drop Access',
  if(grant_priv='Y','Yes','-') 'Grant Access To Other User'
from
  mysql.user
where
  (
    Super_priv='Y' or
    Create_priv='Y' or
    Drop_priv='Y' or
    Grant_priv='Y'
  ) and
  user not like 'pt%' and
  user not like 'pmm%' and
  user not like 'repl%' and
  user not like 'mysql.%' and
  user not like 'nagios%' and
  user not like 'backup%' and
  user not like 'archive%' and
  user not like 'mydbops%' and
  user not in ('maxscale');