
SELECT
queryid,
any(service_name) service_name,
any(node_name) node,
any(example) query,
any(fingerprint) fingerprint,
avg(m_full_scan_sum / m_full_scan_cnt) as full_tbl_scan_count,
sum(num_queries) num_queries
FROM pmm.metrics
WHERE
(example NOT LIKE 'SET%')
AND (example NOT LIKE 'SHOW%') 
AND (example NOT LIKE 'SELECT @@%') 
AND (example NOT IN ('', 'COMMIT', 'BEGIN')) 
AND (example NOT LIKE '/* mysql-connector%') 
AND (example NOT LIKE 'show%') 
AND (example NOT LIKE 'SELECT OBJECT%') 
AND (example NOT LIKE '%performance_schema.%') 
AND (example NOT LIKE '%information_schema.%') 
AND (example NOT LIKE '%mysql.%')
AND (example NOT LIKE '%commit%') 
AND (example NOT LIKE '%INFORMATION_SCHEMA.%') 
AND (example NOT LIKE '%PERFORMANCE_SCHEMA.%') 
AND (example NOT LIKE '%MYSQL.%') 
AND (example NOT LIKE '%pmm-agent:perfschema%') 
AND (example NOT LIKE '%heartbeat.%') 
AND (example NOT LIKE '%HEARTBEAT%')
AND (example NOT LIKE '%SELECT UNIX_TIMESTAMP(ts)%') 
AND (example NOT LIKE '%flush%') 
AND (example NOT LIKE 'FLUSH%') 
AND (period_start BETWEEN '{{from_time}}' and '{{to_time}}')
AND (node_name = '{{node_name}}')
GROUP BY queryid
ORDER BY full_tbl_scan_count DESC
LIMIT {{qan_limit}}