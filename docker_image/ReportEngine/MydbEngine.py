"""
Author : Vetrivel.N
Org : Mydbops
Date : 16-12-2021
Verstion 2 :
Date : 3 - March - 2023
Notes : code may looks crazy, due to tight dealine, this is the best what i can do, 
feel free to follow the design pattern, solid principles or whatever you like to implement
welcome to bring innovation/standards :)

For errors, raise and golang type of error handling used,
Golang style error handling means - instead of raise -> then use try catch to get error and process.
"""
""""""


from util import response as resp
from util.LoadConfig import LoadConfig
import util.Common as common
from generate import generate
import util.Log as log
import pdfkit
import time
import requests
import json
from dirsync import sync 
import multiprocessing
from datetime import date, datetime
import SendMail
import os
import util.Hook as hooks
from constants import constant as my_const


requests.packages.urllib3.disable_warnings(requests.packages.urllib3.exceptions.InsecureRequestWarning)
#conf = myloginpath.parse('mydbops_login',"/Users/<USER>/.mylogin.cnf",True)
#conf = myloginpath.parse('client',"/Users/<USER>/.my.cnf",False)
# from pyhtml2pdf import converter
# converter.convert(source=f'file:////Users/<USER>/Documents/VetrivelN/mydb_health_report/docker_image/ReportEngine/my_reports/document_style/header.html', target='sample.pdf')
def update_execution_log():
    execution = my_const.execution
    try:
        with open('config/execution_log.json', 'w', encoding='utf-8') as f:
            json.dump(execution, f, ensure_ascii=False, indent=4, default=str)
    except Exception as error_elog:
        exe_resp = resp.MyResponse("E","update_execution_log",error_elog)
        exe_resp.print_message()
        exit(0)

def create_execution_log_file(execution_file_name):
    try:
        with open(execution_file_name, 'w') as outfile:  
            json.dump({}, outfile)
    except Exception as error_elog:
        exe_resp = resp.MyResponse("E","tried to create new file, but failed",error_elog)
        exe_resp.print_message()
        exit(0)

def read_execution_log():
    execution = my_const.execution
    try:
        execution_file_name = 'config/execution_log.json'
        is_exist = os.path.exists(execution_file_name)
        if is_exist == False:
            log.print_log("S","execution_log.json does not exist ")
            create_execution_log_file(execution_file_name)

        with open(execution_file_name, 'r') as openfile:
            execution = json.load(openfile)
            if execution != None and len(execution.keys())>0:
                for execution_instance in execution:
                    execution[execution_instance] = datetime.strptime(execution[execution_instance],'%Y-%m-%d')
        my_const.execution = execution
    except Exception as error_elog:
        create_execution_log_file(execution_file_name)

def generate_report(config,report_html_data,file_name,report_type="",report_for="All"):
    try:
        created_date = date.today().strftime("%d/%b/%Y")
        file_name += created_date.replace("/","_")
        report_generate_type = my_const.global_settings.get("report_generate_type")
        report_store_path = my_const.global_settings.get("report_store_path")
        client_name = my_const.global_settings.get("client_name")
     
       
        final_description = ""
        title = my_const.global_settings.get("note_title")
        css_class = my_const.global_settings.get("note_css_class")
        description = my_const.global_settings.get("note_description")
        if title is not None and title != "" and description is not None and description != "" :
            final_description = '<div > <h6>'+title+'</h6> <p class="'+str(css_class)+'">'+description+'</p> <div>'

        first_page_image = common.convert_path_to_image(report_store_path+"/document_style/img/home.png")
        html_css_link = "document_style/css/style.css"
        index_page_content = '<div class="class_header"><center><h1 style="font-weight: bold;">Index</h1></center><table class="table_index">'+my_const.global_settings.get("index_ticket_content")+my_const.global_settings.get("index_content")+'</table> </div> <div class="break"></div>'
        if my_const.global_settings.get("from_time") != None and my_const.global_settings.get("to_time"):
            # from_date = datetime.strptime(my_const.global_settings.get("from_time"), "%Y-%m-%d").strftime("%d/%m/%Y")
            # to_time = datetime.strptime(my_const.global_settings.get("to_time"), "%Y-%m-%d").strftime("%d/%m/%Y")
            from_date = my_const.global_settings.get("from_time")
            to_time = my_const.global_settings.get("to_time")
            from_to_time = from_date + " - " + to_time

        final_html = """<html><head>
        <link rel="stylesheet" href='"""+html_css_link+"""'>
        </head><body >
            <div>
        <div class="first_page">
        <center>
            <img class="img_shadow" src="data:image/png;base64,""" +first_page_image+ """" " width="980" height="800" />
        </center>
        </div>
        <div class="first_page_details home_report">
            <h1 class="file_title">Mydbops Health Report</h1>
            <h1 class="file_title_report_type"> Group : """+report_type.capitalize()+"""</h1>
            <h1 class="file_title_client">Client : """+client_name.capitalize()+"""</h1>
            <h2 class="file_title_create_date">Created Date : """+created_date+"""</h2>
            <h2 class="file_title_create_date"> """+from_to_time+"""</h2>
        </div>
        </div>"""+index_page_content+'<div class="">'+report_html_data + final_description +"""<div class="break thank_you">Thank you</div></div></body> </html>"""
        if report_generate_type == "html":
            file_name = file_name+".html"
            common.write_file(file_name,final_html)
        else:
            try:
                common.write_file(file_name+".html",final_html)
                options = {
                "page-size": "A3",
                "margin-top": "1.5in",
                "margin-right": "0.5in",
                "margin-bottom": "1.5in",
                "margin-left": "0.5in",
                "encoding": "UTF-8",
                "no-outline": None,
                "enable-local-file-access": True,
                "header-html": report_store_path+"/document_style/header.html",
                "footer-html": report_store_path+"/document_style/footer.html",
                "disable-smart-shrinking": True
                }
                file_name = file_name+".pdf"
                pdfkit.from_string(final_html,file_name,options=options,css=report_store_path+"/"+html_css_link,)
            except Exception as epdf:
                exe_resp = resp.MyResponse("E","MydbEngine.generate_report : File generate failed :0 ",epdf)
                exe_resp.print_message()
                exit(0)
        # rp.update_report_files(file_name,report_for,created_date)
        actual_file_name = file_name.replace(report_store_path+"/","")

        log.print_log("S","MydbEngine.generate_report : File generated successfully","")
        if my_const.disable_cloud_upload  is False or my_const.disable_send_mail is False:
            err = SendMail.upload_and_send_mail(client_name,file_name,actual_file_name,config,report_for,created_date)
            if err is not None:
                err.print_message()
                exit(0)
    except Exception as file_generate_error:
        exe_resp = resp.MyResponse("E","MydbEngine.generate_report : File generate failed",file_generate_error)
        exe_resp.print_message()
        exit(0)


def is_report_executed(inventory_name,server):
    execution = my_const.execution
    is_executed = False
    report_type = server.get("report_type")
    if report_type == None:
        report_type = "monthly"
    else:
        report_type = report_type.lower()
    current_time   = date.today()
    current_time_as_per_file   = datetime.strptime(current_time.strftime("%Y-%m-%d"),'%Y-%m-%d') 
    current_day_name =  current_time.strftime("%A")
    current_day =  current_time.strftime("%d")

    if inventory_name not in execution:
        if report_type == "weekly":    
            if current_day_name == "Monday":
                is_executed = False
                execution[inventory_name] = current_time
            else:
                is_executed = True
        else:
            if int(current_day) == 1:
                is_executed = False
                execution[inventory_name] = current_time
            else:
                is_executed = True
    else:
        last_execution = execution[inventory_name]
        
        if report_type == "weekly":    
            if current_day_name == "Monday" and current_time_as_per_file != last_execution:
                is_executed = False
                execution[inventory_name] = current_time
            else:
                is_executed = True
        else:
            if int(current_day) == 1 and current_time_as_per_file != last_execution:
                is_executed = False
                execution[inventory_name] = current_time
            else:
                is_executed = True
    my_const.execution = execution
    return is_executed

def clear_gloabal_settings(config):
    my_const.global_settings["index_content"] = ""
    my_const.global_settings["index_ticket_content"] = ""
    my_const.global_settings["ticket_handled"] = ""
    my_const.global_settings["critical_stats"] = ""
    my_const.global_settings["paging"].clear_counter()                  

def folder_sync(global_settings,src,dest):
    temp_report_store_path = global_settings.get("report_store_path")
    while True:
        try:
            print("Going to SYNC folders")
            # sync("/Users/<USER>/Desktop/delete","/Users/<USER>/Desktop/delete1","sync",twoway=True,create=True,ctime=True,exclude=[".[a-z].[a-z].sw?"])
            # sync("/Users/<USER>/Desktop/delete1","/Users/<USER>/Desktop/delete","sync",twoway=True,create=True,ctime=True,content=True)

            sync("/usr/Mydbops_ReportEngine_Backup/ReportEngine/config","/opt/Mydbops_ReportEngine_Source/ReportEngine/config","sync",twoway=True,create=True,ctime=True,exclude=["\..*\.sw.$"])
            sync("/usr/Mydbops_ReportEngine_Backup/ReportEngine/template","/opt/Mydbops_ReportEngine_Source/ReportEngine/template","sync",twoway=True,create=True,ctime=True,exclude=["\..*\.sw.$"])


        except Exception as err:
            print(err)
        time.sleep(60)

def call_folder_sync(global_settings):
    global sync_thread_id
    if sync_thread_id == None or (sync_thread_id != None and not sync_thread_id.is_alive()):
        sync_thread_id = multiprocessing.Process(target=folder_sync, args=(global_settings,"src","dest"))
        sync_thread_id.start()

def get_ticket_handled_and_critical_stats(config):
    out = ""
    if my_const.global_settings.get("ticket_handled") is not None:
        out += my_const.global_settings.get("ticket_handled")
    if my_const.global_settings.get("critical_stats") is not None:
        out += my_const.global_settings.get("critical_stats")
    return out

sync_thread_id = None
def execute_report():
    is_folder_sync_called = False
    global sync_thread_id
    read_execution_log()

    try:
        common.initDefaultResponse()
        # my_const.execution_id = str(time.time_ns())+''.join(random.choices(string.ascii_letters, k=3))
        # my_const.bot_mode = False
        config = LoadConfig()
        exe_resp = config.init()
        if exe_resp is not None:
            exe_resp.print_message()
            exit(0)

        server_idx = 1
        report_store_path = my_const.global_settings.get("report_store_path")
        report_generate_by = my_const.global_settings.get("report_generate_by")
        if my_const.inventory_list == None or my_const.inventory_list == {} or len(my_const.inventory_list) == 0:
            exe_resp = resp.MyResponse("E","Inventory list is empty,please check configuration files")
            exe_resp.print_message()
            exit(0)

        for inventory_id in my_const.inventory_list:
            server_list = my_const.inventory_list[inventory_id]
            report_html_data = ""
            if server_list == None or server_list == {} or len(server_list) == 0:
                exe_resp = resp.MyResponse("E","Server list is empty")
                exe_resp.print_message()
                exit(0)

            for server_id in server_list:
                server = server_list[server_id]
                if server.get("template") != None:
                    try:
                        report_type = server.get("report_type")
                        if report_type != None:
                            inventory_name = inventory_id+"_"+server_id
                            # is_executed = is_report_executed(inventory_name,server)
                            log.print_log("S","Report for -> " +str(inventory_name)+" , going to check for schedule")

                            log.print_log("S","Report going to execute for -> " +str(inventory_name))
                            server,err_resp = hooks.load_pre_hooks(server,server_id)
                            if err_resp is not None:
                                err_resp.print_message()
                                exit(0)
                            instance_html_data,err = generate.generate_report_from_template(server,server_idx)
                            if err is not None:
                                err.print_message()
                                exit(0)
                            if report_generate_by == "instance" :
                                ticket_critical = get_ticket_handled_and_critical_stats(config)
                                instance_html_data = ticket_critical + instance_html_data
                                if instance_html_data !="":
                                    log.print_log("S","Instant level data ready , Report going to generate -> " +str(inventory_name))
                                    generate_report(config,instance_html_data,report_store_path+"/Report_"+inventory_id+"_"+server_id,report_type,server_id)
                                    clear_gloabal_settings(config)
                            report_html_data += instance_html_data
                            
                    except Exception as error:
                        exe_resp = resp.MyResponse("E","MydbEngine.Loop",error)
                        exe_resp.print_message()
                        exit(0)
            if report_generate_by == "group":
                ticket_critical = get_ticket_handled_and_critical_stats(config)
                report_html_data = ticket_critical + report_html_data
                if report_html_data !="":
                    log.print_log("S","Group level data ready , Report going to generate -> " +str(inventory_id))
                    generate_report(config,report_html_data,report_store_path+"/Report_"+inventory_id,inventory_id,inventory_id)
                    clear_gloabal_settings(config)

        if report_generate_by == "all":
            ticket_critical = get_ticket_handled_and_critical_stats(config)
            report_html_data = ticket_critical + report_html_data
            if report_html_data !="" :
                log.print_log("S","All level data ready , Report going to generate ")
                generate_report(config,report_html_data,report_store_path+"/Report_for_all_instances","All Instances","")
                clear_gloabal_settings(config)

    except Exception as error:
        exe_resp = resp.MyResponse("E","MydbEngine.Loop*******************************",error)
        exe_resp.print_message()
        exit(0)

    update_execution_log()
    rsp = resp.MyResponse("S","report generation done")
    rsp.print_message()

    exit(0)

if __name__ == '__main__':
    execute_report()