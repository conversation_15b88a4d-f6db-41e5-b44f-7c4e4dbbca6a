myDbHealthMetrics = function (Instant) {
    var hostInfo = db.hostInfo();
    var mongoConf = db.adminCommand({ getCmdLineOpts: 1 });
    var ServerStat = db.serverStatus();
    var instanceLevelStats = {};
    var allInstanceLevelStats = JSON.parse(cat(Instant));
    try {
        var host = ServerStat.repl.me
    } catch (e) {
        var host = ServerStat.host
    }
    allInstanceLevelStats.forEach(function (info) {
        if (info.host == host) {
            instanceLevelStats = info;
            return ;
        }
    })
    var systemStats = {};
    var mongoMetrics = {}
    systemStats.host = host;
    mongoMetrics.host = host;
    systemStats.hostUptime = Number(cat('/proc/uptime').split(" ")[0] / 86400).toFixed(2) + " days"
    systemStats.ram = sizeConversion(Number(hostInfo.system.memSizeMB * 1024 * 1024));
    systemStats.cpu = hostInfo.system.numCores;
    systemStats.architecture = hostInfo.system.cpuArch;
    systemStats.memoryUsed = sizeConversion(ServerStat.mem.resident * 1024 * 1024);
    systemStats.osType = hostInfo.os.type
    systemStats.osDistribution = hostInfo.os.name;
    systemStats.osRelease = hostInfo.os.version;
    systemStats.osKernelVersion = hostInfo.extra.kernelVersion;

    mongoMetrics.uptime = Number(ServerStat.uptime / 86400).toFixed(2) + " days";
    mongoMetrics.version = ServerStat.version;
    mongoMetrics.memoryUsed = sizeConversion(ServerStat.mem.resident * 1024 * 1024);
    if (parseFloat(mongoMetrics.version) < 4.4) {
    mongoMetrics.sortMemory = sizeConversion(db.adminCommand({ getParameter: 1, "internalQueryExecMaxBlockingSortBytes": 1 }).internalQueryExecMaxBlockingSortBytes)
    } else {
        mongoMetrics.sortMemory = '100 MB'
    } 
   try {
        mongoMetrics.authentication = mongoConf.parsed.security.authorization;
    } catch (err) {
        mongoMetrics.authentication = "disabled";
    }

    try {
        mongoMetrics.mongoPort = mongoConf.parsed.net.port;
    } catch (err) {
        mongoMetrics.mongoPort = 27017;
    }

    try {
        if (mongoConf.parsed.net.tls)
            mongoMetrics.tls = true;
        else
            mongoMetrics.tls = false;
    } catch (err) {
        mongoMetrics.tls = false;
    }
    try {
        if (mongoConf.parsed.net.ssl)
            mongoMetrics.ssl = true;
        else
            mongoMetrics.ssl = false;
    } catch (err) {
        mongoMetrics.ssl = false;
    }

    mongoMetrics.storageEngine = db.serverStatus().storageEngine.name;

    try {
        mongoMetrics.compressionType = mongoConf.parsed.storage.wiredTiger.collectionConfig.blockCompressor;
    } catch (err) {
        mongoMetrics.compressionType = "snappy";
    }

    mongoMetrics.status = ServerStat.repl ? 'Replica Set' : 'StandAlone';
    if (ServerStat.repl) {
        mongoMetrics.replicaName = ServerStat.repl.setName
        mongoMetrics.replState = ServerStat.repl.ismaster == true ? 'Primary' : ServerStat.repl.arbiterOnly == true ? 'Arbiter' : ServerStat.repl.hidden == true ? 'Hidden' : 'Secondary';
        mongoMetrics.replMember = rs.status().members.length;
        mongoMetrics.tags = null;
        rs.conf().members.forEach(function (mem) {
            if (mem.host == (rs.isMaster().me) && Object.keys(mem.tags).length > 0) {
                mongoMetrics.tags = mem.tags;
            }
        })
        mongoMetrics.oplogSize = sizeConversion(Number(db.getReplicationInfo().logSizeMB * 1024 * 1024));
        mongoMetrics.oplogUsed = sizeConversion(Number(db.getReplicationInfo().usedMB * 1024 * 1024));
        mongoMetrics.oplogCount = db.getSiblingDB("local").oplog.rs.count();
        mongoMetrics.oplogDuration = db.getReplicationInfo().timeDiffHours + " hours";
        try {
            mongoMetrics.keyFile = mongoConf.parsed.security.keyFile;
        } catch (err) {
            mongoMetrics.keyFile = null;
        }
    }

    var out = {
        systemStats: systemStats,
        instanceLevelStats: instanceLevelStats,
        mongoMetrics: mongoMetrics,
    }
    var [Recommendations, Rating] = mydbRecommendations(out)
    out.Recommendations = Recommendations;
    return [out, Rating];
}
