myDbOpCounters = function () {
    var serverOpCountes = {
    }
    var serverStatus = db.serverStatus()
    try {
        serverOpCountes.host = serverStatus.repl.me
    } catch (e) {
        serverOpCountes.host = serverStatus.host
    }
    serverOpCountes.Insert = Number(serverStatus.opcounters.insert);
    serverOpCountes.Query = Number(serverStatus.opcounters.query);
    serverOpCountes.Update = Number(serverStatus.opcounters.update);
    serverOpCountes.Delete = Number(serverStatus.opcounters.delete);
    serverOpCountes.Getmore = Number(serverStatus.opcounters.getmore);
    serverOpCountes.Command = Number(serverStatus.opcounters.command);
    return serverOpCountes
}
