myDbUsers = function () {
    var skipUsers = [];
    var rootUsers = [];
    var dropUsers = [];
    var dummyUsers = [];
    var noAccessDBs = [];
    var specialUsers = {
        rootUsers: [],
        dropUsers: [],
        dummyUsers: [],
        noAccessDBs: []
    };
    var Rating = 0
    db.getMongo().getDBNames().forEach(function (dbname) {
        if (dbname != "config" && dbname != "local") {
            try {
                var Users = db.getSiblingDB(dbname).getUsers();
            } catch {
                noAccessDBs.push(dbname);
                return;
            }
            Users.forEach(function (u) {
                if (skipUsers.includes(u.user)) {
                    return;
                }
                if (u.roles.length == 0) {
                    dummyUsers.push({
                        db: dbname,
                        user: u.user
                    });
                    return;
                }
                for (k = 0; k < u.roles.length; k++) {
                    if (u.roles[k].role == 'root') {
                        rootUsers.push({
                            db: dbname,
                            user: u.user
                        });
                        return;
                    }
                    privileges = db.getSiblingDB(dbname).getRole(u.roles[k].role, { showPrivileges: true }).privileges;
                    for (j = 0; j < privileges.length; j++) {
                        if (privileges[j].actions.includes('remove')) {
                            dropUsers.push({
                                db: dbname,
                                user: u.user,
                                roles: u.roles
                            });
                            return;
                        }
                    }
                }
            })
        }
    })
    if (rootUsers.length) {
        specialUsers.rootUsers = rootUsers;
    }
    if (dropUsers.length) {
        specialUsers.dropUsers = dropUsers;
    }
    if (dummyUsers.length) {
        specialUsers.dummyUsers = dummyUsers;
    }
    if (noAccessDBs.length) {
        specialUsers.noAccessDBs = noAccessDBs;
    }

    try {
        if (db.adminCommand({ getCmdLineOpts: 1 }).parsed.security.authorization == 'enabled') {
            if (rootUsers.length == 1) {
                Rating += 3;
            } else if (rootUsers.length <= 3 && rootUsers.length >= 1) {
                Rating += 2;
            } else {
                Rating += 1;
            }
            if (dropUsers.length < 7) {
                Rating += 3;
            } else if (dropUsers.length <= 15 && dropUsers.length >= 7) {
                Rating += 2;
            } else {
                Rating += 1;
            }
            if (dropUsers.length == 0) {
                Rating += 3;
            }
        } else {
            Rating += 0;
        }
    } catch (err) {
        Rating += 0;
    }

    return [specialUsers, Rating];
}
