myDbDistribution = function () {
    var data = [];
    var totalCollCount = 0
    var totalIndexCount = 0
    var totalIndexSize = 0
    var totalObjects = 0
    var totalDataSize = 0
    var totalAvgObjectSize = 0
    var totalStorageSize = 0
    db.getMongo().getDBNames().forEach(function (dbname) {
        if (dbname != "admin" && dbname != "config" && dbname != "local") {
            var dbStats = db.getSiblingDB(dbname).stats()
            var collCount = dbStats.collections
            totalCollCount = totalCollCount + collCount
            var indexCount = dbStats.indexes
            totalIndexCount = totalIndexCount + indexCount
            indexSize = dbStats.indexSize
            totalIndexSize = totalIndexSize + indexSize
            var indexSize = sizeConversion(indexSize)
            var objects = dbStats.objects
            totalObjects = totalObjects + objects
            var dataSize = dbStats.dataSize
            totalDataSize = totalDataSize + dataSize
            var avgObjectSize = dbStats.avgObjSize
            totalAvgObjectSize = totalAvgObjectSize + avgObjectSize
            avgObjectSize = sizeConversion(avgObjectSize)
            var storageSize = dbStats.storageSize
            totalStorageSize = totalStorageSize + storageSize
            storageSize = sizeConversion(storageSize)
            var sample = JSON.parse("{ " + "\"dbname\": \"" + dbname + "\", " + "\"collCnt\": " + collCount + ", " + "\"idxCnt\": " + indexCount + ", " + "\"idxSize\": \"" + indexSize + "\", " + "\"objects\": " + objects + ", " + "\"dataSize\": " + dataSize + ", " + "\"avgDocSize\": \"" + avgObjectSize + "\", " + "\"strSize\": \"" + storageSize + "\" }");
            data.push(sample);
        }
    })
    var final = [];
    data.sort(function (sizeA, sizeB) {
        return (sizeB.dataSize - sizeA.dataSize)
    }).forEach(function (d) {
        d.dataSize = sizeConversion(d.dataSize)
        final.push(d)
    })
    var totalIndexSize = sizeConversion(totalIndexSize);
    var totalDataSize = sizeConversion(totalDataSize);
    var totalAvgObjectSize = sizeConversion(totalAvgObjectSize);
    var totalStorageSize = sizeConversion(totalStorageSize);
    final.push({
        dbname: "Total",
        collCnt: totalCollCount,
        idxCnt: totalIndexCount,
        idxSize: totalIndexSize,
        objects: totalObjects,
        dataSize: totalDataSize,
        avgDocSize: totalAvgObjectSize,
        strSize: totalStorageSize
    })

    return final
}
