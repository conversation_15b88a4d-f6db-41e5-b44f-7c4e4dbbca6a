
mydbRecommendations = function (obj) {
    var systemStats = obj.systemStats;
    var instanceLevelStats = obj.instanceLevelStats;
    var mongoMetrics = obj.mongoMetrics;
    Rating = 0;
    var sysRecommendations = {};
    if (systemStats.OsDistribution == 'Ubuntu' && systemStats.OsRelease < 20) {
        sysRecommendations.OsVersion = {
            Recommendation: "We are recommending to upgrade the OS version to the available latest version.",
            Reason: ["MongoDB provides official packages for Ubuntu releases, which means that there are compatible versions of MongoDB for each supported version of Ubuntu.",
                "Ubuntu 16.04: MongoDB 3.2, 3.4, 3.6, 4.0. ",
                "Ubuntu 18.04: MongoDB 3.6, 4.0, 4.2, 4.4, 5.0. ",
                "Ubuntu 20.04: MongoDB 4.4, 5.0.",
                "So we are recommending upgrading the OS to the latest version available."]
        }
    } else {
        Rating += 3;
    }

    var instanceLevelRecommendations = {};
    {
        if (instanceLevelStats.numaEnabled)
            instanceLevelRecommendations.Numa = {
                Recommendation: "We are recommending to disable the numa.",
                Reason: [
                    "As part of our MongoDB database support team, we strongly recommend disabling NUMA (Non-Uniform Memory Access) on your system to ensure the optimal performance and stability of your MongoDB deployment.",
                    "Although NUMA is a hardware feature that allows multiple processors to access different regions of memory on a system, it can cause performance degradation and instability for MongoDB deployments as MongoDB is designed to run as a single process.",
                    "By disabling NUMA in the BIOS settings of your system or using kernel boot parameters, MongoDB can utilize all available memory evenly, leading to improved query performance and overall stability.",
                    "We are available to assist you with any questions or concerns you may have during this process."]
            }
        else {
            Rating += 3;
        }
        if (instanceLevelStats.maxOpenFiles < 64000)
            instanceLevelRecommendations.maxOpenFiles = {
                Recommendation: "We are allways recommending to configure the open files limit minimum of 64000.",
                Reason: [
                    "As a member of the Mydbops MongoDB database support team, we strongly recommend adjusting the 'maxOpenFiles' setting to ensure optimal performance and stability of your MongoDB deployment.",
                    "This setting determines the maximum number of files that MongoDB can have open at any given time, including data files, indexes, and log files. If this setting is too low, it can result in performance issues and potential data loss.",
                    "We recommend setting this value to at least 64k to avoid such problems. However, the optimal value for your deployment may depend on factors such as the size of your database and the number of concurrent connections.",
                    "Our support team is available to help you determine the best value for your specific deployment and to assist with any questions or concerns you may have during this process."]
            }
        else {
            Rating += 3;
        }

        if (instanceLevelStats.mongoUser == 'root')
            instanceLevelRecommendations.MongoUser = {
                Recommendation: "We are always recommending to run the mongod process with the mongod user instead of root user.",
                Reason: [
                    "As a member of the Mydbops MongoDB database support team, we strongly recommend avoiding running MongoDB as the root user in your production deployment, as it can expose your database to security risks such as unauthorized access, data tampering, and system-level attacks.",
                    "Running MongoDB as the root user gives it unrestricted access to the system resources, which can be exploited by attackers to gain control of your server or to perform malicious activities.",
                    "We recommend creating a dedicated system user for MongoDB with limited permissions, and ensuring that this user has access only to the required directories and resources. ",
                    "Our support team can assist you with creating a dedicated system user for MongoDB and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 3;
        }

        if (instanceLevelStats.dirtyRatio > '15')
            instanceLevelRecommendations.DirtyRatio = {
                Recommendation: "We are always recommending to maintain the DirtyRatio less than 15.",
                Reason: [
                    "As a member of the Mydbops MongoDB database support team, we recommend configuring the Dirty Ratio for your MongoDB deployment to ensure optimal performance and stability of your database.",
                    "Dirty Ratio is a kernel parameter that controls the amount of memory used for file system caching, and it can significantly impact the I / O performance of your database. ",
                    "Setting a higher Dirty Ratio can improve the read performance of your database, while setting a lower ratio can improve the write performance.",
                    "It is important to consider factors such as the available memory, the expected workload, and the type of storage when setting the Dirty Ratio. We recommend setting the Dirty Ratio to a value that balances the read and write performance and ensures that there is enough memory available for other system processes.",
                    "Our support team can assist you with configuring the Dirty Ratio for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 3;
        }

        if (instanceLevelStats.dirtyBackgroundRatio > '5')
            instanceLevelRecommendations.DirtyBackgroundRatio = {
                Recommendation: "We are always recommending to maintain the DirtyRatio less than 5.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we recommend configuring the Dirty Background Ratio for your MongoDB deployment to ensure optimal performance and stability of your database.",
                    "The Dirty Background Ratio is a kernel parameter that controls the amount of memory used for background file system caching. This ratio can significantly impact the I/O performance of your database and the responsiveness of your system.",
                    "Setting a higher Dirty Background Ratio can improve the background caching performance, while setting a lower ratio can reduce the impact on foreground operations such as database queries.",
                    "It is important to consider factors such as the available memory, the expected workload, and the type of storage when setting the Dirty Background Ratio.",
                    "We recommend setting the Dirty Background Ratio to a value that ensures there is enough memory available for foreground operations and that the background caching is optimized for the expected workload.",
                    "Our support team can assist you with configuring the Dirty Background Ratio for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 3;
        }

        if (instanceLevelStats.swappiness > '10')
            instanceLevelRecommendations.Swappiness = {
                Recommendation: "We are always recommending to maintain the swappiness less than 10.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we recommend configuring the swappiness setting for your MongoDB deployment to ensure optimal performance and stability of your database.",
                    "Swappiness is a kernel parameter that controls the degree to which the Linux kernel swaps out processes from memory to disk when under memory pressure.",
                    "This parameter can have a significant impact on the performance of your database, as swapping out active MongoDB processes can result in high I/O wait times and degraded query performance. We recommend setting the swappiness to a value that balances the memory usage of your MongoDB deployment and the other system processes running on the server.",
                    "A low swappiness value can help ensure that MongoDB processes are not excessively swapped out to disk, while a high swappiness value can help ensure that other system processes have access to sufficient memory resources.",
                    "Our support team can assist you with configuring the swappiness setting for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 3;
        }

        if (instanceLevelStats.logRotate == 'disabled')
            instanceLevelRecommendations.Logrotate = {
                Recommendation: "We are always recommending to enable the logRotation with minimum of 15 days of log retention. ",
                Reason: ["As a member of the Mydbops MongoDB database support team, we strongly recommend implementing log rotation for your MongoDB deployment to ensure optimal performance and stability.",
                    "Log rotation is the process of automatically archiving and compressing old log files and creating new ones to avoid filling up disk space and causing potential system issues.",
                    "MongoDB generates various types of log files, including diagnostic logs, query logs, and profiling logs, which can quickly consume significant amounts of disk space if not managed properly.",
                    "By implementing log rotation, you can ensure that your log files are efficiently managed, reducing the risk of performance degradation and data loss due to disk space limitations.",
                    "Our support team can help you set up and configure log rotation for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            };
        else {
            if (instanceLevelStats.Logrotate != 'enabled' && instanceLevelStats.logRetention < 15) {
                instanceLevelRecommendations.LogrotateRetention = {
                    Recommendation: "Log rotation is an important aspect of managing MongoDB logs. We recommend maintaining retention logs of at least 15 days.",
                    Reason: ["Our support team can help you set up and configure log rotation for your MongoDB deployment and answer any questions or concerns you may have during the process."]
                }
                Rating += 2;
            } else {
                Rating += 5;
            }
        }
        if (instanceLevelStats.transparentHugepage != 'never')
            instanceLevelRecommendations.TransparentHugepage = {
                Recommendation: "We are always recommending to maintain THP in 'never' state.",
                Reason: ["As a member of the MongoDB database support team, we recommend configuring the Transparent Huge Pages (THP) setting for your MongoDB deployment to ensure optimal performance and stability of your database. THP is a Linux kernel feature that enables the use of larger memory pages for improved performance. ",
                    "However, THP can also cause performance issues with MongoDB, as it can lead to increased memory usage and higher CPU utilization. We recommend disabling THP for your MongoDB deployment to avoid these issues. ",
                    "Disabling THP can help ensure that MongoDB is not affected by any unpredictable changes in the memory management system and that it has consistent access to memory resources. ",
                    "Our support team can assist you with disabling the THP setting for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 2;
        }

        if (instanceLevelStats.defrag != 'never')
            instanceLevelRecommendations.Defrag = {
                Recommendation: "We are always recommending to maintain defrag in 'never' state.",
                Reason: [" As a member of the Mydbops MongoDB database support team, we recommend configuring the Transparent Huge Pages (THP) defragmentation setting for your MongoDB deployment to ensure optimal performance and stability of your database. ",
                    "THP defragmentation is a Linux kernel feature that reclaims fragmented memory used by THP. In some cases, THP fragmentation can cause performance issues with MongoDB, as it can lead to increased memory usage and higher CPU utilization. ",
                    "Enabling THP defragmentation can help ensure that the memory used by THP is efficiently utilized and that MongoDB has consistent access to memory resources. We recommend enabling THP defragmentation for your MongoDB deployment to avoid these issues. ",
                    "Our support team can assist you with enabling THP defragmentation for your MongoDB deployment and answer any questions or concerns you may have during the process.As a member of the MongoDB database support team, we recommend configuring the Transparent Huge Pages (THP) defragmentation setting for your MongoDB deployment to ensure optimal performance and stability of your database. ",
                    "THP defragmentation is a Linux kernel feature that reclaims fragmented memory used by THP. In some cases, THP fragmentation can cause performance issues with MongoDB, as it can lead to increased memory usage and higher CPU utilization. ",
                    "Enabling THP defragmentation can help ensure that the memory used by THP is efficiently utilized and that MongoDB has consistent access to memory resources. We recommend enabling THP defragmentation for your MongoDB deployment to avoid these issues. ",
                    "Our support team can assist you with enabling THP defragmentation for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 2;
        }

        if (instanceLevelStats.diskMountPoint == '/')
            instanceLevelRecommendations.diskMountPoint = {
                Recommendation: "We are always recommending to maintain the mongodb data directory in a separate mount point.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we strongly recommend storing your MongoDB data files on a dedicated disk mount point to ensure optimal performance and stability of your deployment. ",
                    "Storing data files on a dedicated disk mount point provides several benefits, including improved I/O performance and increased resilience to disk failures. When selecting a mount point, it is important to consider factors such as the expected size of your database, the number of concurrent connections, and the disk type and size. ",
                    "It is also important to ensure that the disk is formatted using a file system that is compatible with your operating system and that it has adequate disk space to accommodate your data growth. ",
                    "Our support team is available to assist you with selecting and configuring a suitable disk mount point for your MongoDB deployment and to answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 2;
        }

        if (instanceLevelStats.diskFtype != 'xfs')
            instanceLevelRecommendations.DiskFtype = {
                Recommendation: "We are always recommending to use the xfs file system for the mongodb data disk.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we recommend using the XFS filesystem type for your MongoDB deployment to ensure optimal performance and stability of your database. ",
                    "XFS is a high-performance filesystem that is optimized for large-scale, high-throughput workloads, making it an excellent choice for MongoDB deployments. XFS offers several benefits, including support for large files and file systems, improved metadata performance, and better scalability compared to other filesystem types such as Ext4. ",
                    "Additionally, XFS offers several advanced features, such as support for online resizing and data check summing, that can further enhance the reliability and performance of your MongoDB deployment. ",
                    "Our support team can assist you with configuring the XFS filesystem for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 3;
        }
    }

    var mongoRecommendations = {};
    {
        if (parseFloat(mongoMetrics.version) < 5.0) {
            mongoRecommendations.Version = {
                Recommendation: "We are recommending upgrade the mongodb version to latest version.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we recommend upgrading your MongoDB deployment to the latest version to take advantage of new features, performance improvements, and bug fixes. ",
                    "The latest version of MongoDB offers several enhancements, including improved security, better scalability, and advanced analytics capabilities, making it an excellent choice for your database needs. ",
                    "Upgrading to the latest version can also help ensure that your deployment is secure and meets current compliance standards. ",
                    "Our support team can assist you with upgrading your MongoDB deployment to the latest version and answer any questions or concerns you may have during the process."]
            };
            if (parseFloat(mongoMetrics.version) == 4.4)
                Rating += 4;
            else if (parseFloat(mongoMetrics.version) == 4.4)
                Rating += 3;
            else if (parseFloat(mongoMetrics.version) == 4.2)
                Rating += 2;
            else
                if (parseFloat(mongoMetrics.version) == 4.0)
                    Rating += 1;
        } else {
            Rating += 5;
        }

        if (parseFloat(mongoMetrics.version) < 4.4 && Number(mongoMetrics.sortMemory.split(' ')[0]) > '32')
            mongoRecommendations.SortMemory = {
                Recommendation: "We are recommending maintain the sort memory as default value. (Below 4.4 versions 32 MB and other is 100 MB)",
                Reason: ["The sort operation in MongoDB can consume a significant amount of memory, and setting an appropriate memory limit can help ensure that the sort operation runs efficiently and does not cause excessive memory usage or crashes. ",
                    "We recommend setting the sort memory limit to a value that is appropriate for your database workload and hardware resources. ",
                    "Our support team can assist you with configuring the sort memory limit for your MongoDB deployment and answer any questions or concerns you may have during the process. ",
                    "By setting an appropriate sort memory limit, you can ensure that your database queries run efficiently and deliver the best possible performance. ",
                    "Note that the configuration options for sort memory have changed in MongoDB version 4.4, so if you are upgrading from an earlier version, you may need to adjust your configuration accordingly. Our support team can help you with this as well."]
            };
        else {
            Rating += 3;
        }

        if (mongoMetrics.authentication != 'enabled')
            mongoRecommendations.Authentication = {
                Recommendation: "We are recommending to enable the security for the mongodb.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we highly recommend enabling authentication for your MongoDB deployment to protect your data from unauthorized access. ",
                    "Enabling authentication requires users to provide valid credentials before they can access the database, adding an extra layer of security to your deployment. ",
                    "To enable authentication, you can configure authentication mechanisms such as SCRAM-SHA-256, x.509 certificates, or LDAP. Our support team can assist you with enabling authentication for your MongoDB deployment and answer any questions or concerns you may have during the process. ",
                    "By enabling authentication, you can ensure that your database is secure and protected from malicious actors. It is important to note that in MongoDB version 4.4 and later, authentication is enabled by default, so you may not need to perform any additional configuration if you are using a newer version of MongoDB. ",
                    "However, our support team can still provide guidance and assistance if needed."]
            }
        else {
            Rating += 5;
        }

        if (mongoMetrics.mongoPort == '27017')
            mongoRecommendations.MongoPort = {
                Recommendation: "We are recommending to use other ports instead of default one.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we strongly recommend avoiding the use of MongoDB's default port (27017) for your production deployment, as it can expose your database to security risks such as unauthorized access, data tampering, and denial-of-service attacks. ",
                    "Attackers can easily scan the internet for MongoDB servers running on the default port, and attempt to exploit known vulnerabilities or perform brute-force attacks to gain access to your data. We recommend changing the default port to a non-standard port number and to ensure that your firewall rules only allow access to authorized IP addresses. ",
                    "Our support team can assist you with changing the default port and implementing additional security measures to protect your MongoDB deployment, and answer any questions or concerns you may have during the process."]
            };
        else {
            Rating += 3;
        }

        if (!mongoMetrics.tls && !mongoMetrics.ssl) {
            mongoRecommendations.TransitEncryption = {
                Recommendation: "We are recommending to enable the transit encryption.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we strongly recommend enabling Transit Encryption for your MongoDB deployment to ensure secure data transmission between your application and the database server. ",
                    "Transit Encryption encrypts network traffic between the client and server using SSL/TLS protocols, protecting data from interception and tampering. ",
                    "It is recommended to use the latest version of SSL/TLS protocols supported by your MongoDB deployment, and to carefully configure your certificate authorities to avoid potential security vulnerabilities. ",
                    "Our support team can assist you with enabling Transit Encryption and configuring SSL/TLS protocols and certificate authorities, and answer any questions or concerns you may have during the process. ",
                    "By enabling Transit Encryption, you can ensure the confidentiality and integrity of your data, and comply with security regulations and best practices."]
            }

        } else {
            if (parseFloat(mongoMetrics.version) > 4.0 && mongoMetrics.ssl) {
                mongoRecommendations.TransitEncryption = "From 4.2v on words the ssl option is deprecated. So we are recommending to use tls configuration for transit encryption";
                Rating += 2;
            }
            else {
                Rating += 3;
            }
        }

        if (mongoMetrics.storageEngine != 'wiredTiger')
            mongoRecommendations.StorageEngine = {
                Recommendation: "We are recommending to use the wiredTiger as the default storage.",
                Reason: ["As a member of the Mydbops MongoDB database support team, we recommend enabling the WiredTiger storage engine for your MongoDB deployment to take advantage of its advanced features and performance benefits. ",
                    "The WiredTiger storage engine is the default storage engine for MongoDB version 3.2 and later, and it offers significant advantages over the previous MMAPv1 engine, including improved compression, faster performance, and more efficient use of disk space. ",
                    "To enable WiredTiger, you can simply specify it as the storage engine when starting up your MongoDB instance. ",
                    "Our support team can assist you with enabling WiredTiger for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 3;
        }

        if (mongoMetrics.compressionType != 'zlib')
            mongoRecommendations.CompressionType = {
                Recommendation: "We are recommending to use the zlib compression as the default compression.",
                Reason: ["As a database support team, we recommend using either Zlib or Zstandard (Zstd) compression algorithms in WiredTiger for MongoDB, depending on your specific requirements. ",
                    "Zlib is a widely-used compression algorithm that offers a good balance between compression ratio and speed, making it a good choice for general-purpose workloads. ",
                    "By carefully selecting the appropriate compression algorithm and tuning compression settings, We can optimize storage usage and reduce costs while ensuring that your workload meets performance and latency requirements. ",
                    "We are available to assist you with any questions or concerns you may have during this process."]
            };
        else {
            Rating += 2;
        }

        if (mongoMetrics.status == 'StandAlone')
            mongoRecommendations.NodeStatus = {
                Recommendation: "We are recommending to configure the replica set instead of standalone",
                Reason: ["As a member of the Mydbops MongoDB database support team, we highly recommend against using MongoDB in standalone mode for production deployments. ",
                    "Using MongoDB in standalone mode can increase the risk of data loss and downtime, especially in scenarios where the server hardware or network connection experiences issues. ",
                    "Instead, we recommend using a replica set or sharded cluster deployment to ensure high availability, data redundancy, and automatic failover in the event of hardware or network failures. ",
                    "Replica sets and sharded clusters also offer better scalability, allowing you to distribute your workload across multiple nodes and handle increasing amounts of data and traffic. ",
                    "Our support team can assist you with setting up replica sets or sharded clusters for your MongoDB deployment and answer any questions or concerns you may have during the process."]
            }
        else {
            Rating += 5;
            if (mongoMetrics.replMember % 2 == 0 || mongoMetrics.replMember == 1)
                mongoRecommendations.ReplicaMemberCount = {
                    Recommendation: "We are recommending to configure the odd number of members in the replica set.",
                    Reason: ["As a member of the Mydbops MongoDB database support team, we recommend against using an even number of nodes in a MongoDB replica set deployment. ",
                        "Using an even number of nodes can create a situation where a primary node cannot be elected in case of a network partition, leading to reduced availability and increased risk of data loss. ",
                        "Instead, we recommend using an odd number of nodes, which ensures that a primary node can always be elected in case of a network partition. Additionally, we recommend placing each node in a different availability zone or geographic region to provide additional protection against network failures or natural disasters. ",
                        "Our support team can assist you with designing and configuring a replica set deployment that meets your specific needs and answer any questions or concerns you may have during the process."]
                };
            else {
                Rating += 3;
            }

            if (mongoMetrics.tags == null && mongoMetrics.replState == 'Secondary')
                mongoRecommendations.TagSet = {
                    Recommendation: "We are recommending that to add any tag set based on the requirement.",
                    Reason: ["As a member of the Mydbops MongoDB database support team, we recommend using replica tags in MongoDB to help control the distribution of data across your replica set. ",
                        "By using replica tags, you can assign tags to members of the replica set, indicating their purpose or location. This allows you to control which members of the set hold specific data or perform specific tasks, such as running analytics workloads or handling backup operations. ",
                        "Replica tags also allow you to ensure that read and write operations are distributed evenly across your replica set, optimizing performance and minimizing the risk of downtime. ",
                        "It is important to carefully plan and document your use of replica tags to ensure that they are used effectively and consistently across your deployment. By implementing replica tags, you can achieve greater control and flexibility in your MongoDB replica set and optimize your database's performance and availability. ",
                        "We are available to assist you with any questions or concerns you may have during this process."]
                };
            else {
                Rating += 3;
            }

            if (Number(mongoMetrics.oplogDuration.split(' ')[0]) < 24 && db.getReplicationInfo().usedMB * 100 / db.getReplicationInfo().logSizeMB > 80)
                mongoRecommendations.OplogResize = {
                    Recommendation: "We are recommending increase the oplog size.",
                    Reason: ["As a member of the Mydbops MongoDB database support team, we highly recommend configuring the right oplog size for your MongoDB deployment to ensure optimal performance and avoid running out of space. The oplog is a capped collection that records all write operations that occur on a MongoDB instance. ",
                        "The size of the oplog determines how far back you can go in time to recover data in case of a failure or disaster. The recommended oplog size depends on your workload, the size of your dataset, and the frequency and size of your write operations. ",
                        "As a rule of thumb, the oplog size should be at least 5% of the disk space on each member of the replica set, and larger if you have a heavy write workload. ",
                        "Our support team can assist you with sizing and configuring the oplog for your MongoDB deployment and answer any questions or concerns you may have during the process."]
                };
            else {
                Rating += 3;
            }

            if (mongoMetrics.keyFile == null)
                mongoRecommendations.KeyFile = {
                    Recommendation: "We are recommending to configure keyfile in the replica set for the internal authentication.",
                    Reason: ["As a member of the Mydbops MongoDB database support team, we highly recommend using a keyfile to secure your MongoDB deployments. A keyfile is a shared secret that enables authentication between nodes in a MongoDB replica set or sharded cluster. ",
                        "Without a keyfile, any user with network access to the MongoDB deployment could potentially access or modify the data, which is a significant security risk. ",
                        "By using a keyfile, you can ensure that only authorized users can access your MongoDB deployment, thereby mitigating the risk of unauthorized access and data breaches. ",
                        "Our support team can assist you with generating and configuring a keyfile for your MongoDB deployment, and we can also provide guidance on best practices for key management and rotation."]
                };
            else {
                Rating += 3;
            }
        }
    }

    var Recommendations = {};
    Recommendations.host = mongoMetrics.host
    if (Object.keys(sysRecommendations).length > 0) {
        Recommendations.sysRecommendations = sysRecommendations
    }
    if (Object.keys(instanceLevelRecommendations).length > 0) {
        Recommendations.instanceLevelRecommendations = instanceLevelRecommendations
    }
    if (Object.keys(mongoRecommendations).length > 0) {
        Recommendations.mongoRecommendations = mongoRecommendations
    }
    return [Recommendations, Rating];
}
