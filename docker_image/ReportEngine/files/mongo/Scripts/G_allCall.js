mydbHealthReport = function (username, pwd, authDB, Instant, skipIps) {
    slave();
    var finalOut = {
        myDbHealthMetrics: {
            systemStats: [],
            instanceLevelStats: [],
            mongoMetrics: [],
            Recommendations: []
        },
        myDbDistribution: {},
        myDbConsiderableCollections: {},
        myDbOpCounters: [],
        mydbUsers: {},
        myDbIndexes: {},
        myDbUnusedIndex: []
    };
    var Rating = 0;
    var replRating = 0;
    var b = myDb_TTL_DuplicateIndexes();
    var c = myDbUsers();
    finalOut.myDbDistribution = myDbDistribution()
    finalOut.myDbConsiderableCollections = myDbConsiderableCollections();
    finalOut.myDbIndexes = b[0];
    finalOut.mydbUsers = c[0];

    Rating += b[1];
    Rating += c[1];

    var isMaster = rs.isMaster();
    if (typeof username == 'undefined' || typeof pwd == 'undefined' || typeof authDB == 'undefined' || username == '' || pwd == '' || authDB == '' || isMaster.setName == null || isMaster.hosts.length == 1) {
        var d = myDbHealthMetrics(Instant);
        Rating += d[1];
        finalOut.myDbHealthMetrics.systemStats.push(d[0].systemStats);
        finalOut.myDbHealthMetrics.instanceLevelStats.push(d[0].instanceLevelStats);
        finalOut.myDbHealthMetrics.mongoMetrics.push(d[0].mongoMetrics);
        finalOut.myDbHealthMetrics.Recommendations.push(d[0].Recommendations);
        data0 = unusedIndex();
        if (data0.length > 0) {
            data0.sort(function (r, l) {
                return l.collStSize - r.collStSize;
            }).forEach(function (d) {
                d.collStrSize = sizeConversion(d.collStSize);
                if (d.days >= 60 && finalOut.myDbUnusedIndex.length <= 50 && d.idxType != 'unique' && d.idxType != 'ttl')
                    finalOut.myDbUnusedIndex.push(d);
            })
        }
        finalOut.myDbOpCounters.push(myDbOpCounters());
        if (isMaster.setName) {
            finalOut.warning = 'Invalid Credentials for Replica set. So the stats were take for ' + isMaster.me + ' Server Only'
        }
    } else {
        var validMembers = [];
        var data0 = [];
        if (isMaster.setName) {
            if (typeof skipIps == 'undefined') {
                var skipIps = [];
            }
            var status = db.adminCommand({ replSetGetStatus: 1 });
            var conf = rs.conf();
            validMembers.push(isMaster.primary);
            for (var j = 0; j < status.members.length; j++) {
                if (!skipIps.includes(status.members[j].name.split(':')[0]) && !conf.members[j].hidden && status.members[j].state == 2) {
                    validMembers.push(status.members[j].name);
                }
            }
            if (validMembers.length != 1) {
                for (var i = 0; i < validMembers.length; i++) {
                    db = connect((validMembers[i] + "/" + authDB), username, pwd);
                    slave();
                    var d = myDbHealthMetrics(Instant);
                    finalOut.myDbHealthMetrics.systemStats.push(d[0].systemStats);
                    finalOut.myDbHealthMetrics.instanceLevelStats.push(d[0].instanceLevelStats);
                    finalOut.myDbHealthMetrics.mongoMetrics.push(d[0].mongoMetrics);
                    for (var j = 0; j < i; j++) {
                        if (validMembers[j].split(":")[0] == validMembers[i].split(":")[0]) {
                            delete d[0].Recommendations.sysRecommendations
                            delete d[0].Recommendations.instanceLevelRecommendations
                        }
                    }
                    finalOut.myDbHealthMetrics.Recommendations.push(d[0].Recommendations);
                    replRating += d[1];
                    finalOut.myDbOpCounters.push(myDbOpCounters());
                    try {
                        var data1 = unusedIndex();
                    } catch (err) {
                        print(err.message);
                        return;
                    }
                    if (data1.length === 0) {
                        break;
                    } else if (i == 0) {
                        var data0 = Array.from(data1);
                    } else {
                        var out = [];
                        for (var j = 0; j < data1.length; j++) {
                            var result = data0.find(function (data) {
                                return data.databaseName === data1[j].databaseName && data.collName === data1[j].collName && JSON.stringify(data.key) == JSON.stringify(data1[j].key) && data.indexType === data1[j].indexType;
                            });
                            if (result != undefined) {
                                out.push(result);
                            }
                        }
                        data0 = Array.from(out);
                        if (data0.length === 0) {
                            break;
                        }
                    }
                }
                db = connect((isMaster.me + "/" + authDB), username, pwd)
                slave();
            }
            Rating += replRating / validMembers.length;
            if (data0.length > 0) {
                data0.sort(function (r, l) {
                    return l.collStSize - r.collStSize;
                }).forEach(function (d) {
                    d.collStrSize = sizeConversion(d.collStSize);
                    if (d.days >= 60 && finalOut.myDbUnusedIndex.length <= 50 && d.idxType != 'unique' && d.idxType != 'ttl')
                        finalOut.myDbUnusedIndex.push(d);
                })
            }
        }
    }
    if (finalOut.myDbUnusedIndex.length < 100)
        Rating += 3;
    if (finalOut.myDbConsiderableCollections.emptyColl.length < 10) {
        Rating += 2;
    }
    finalOut.Rating = Rating + " %"
    return finalOut;
}
