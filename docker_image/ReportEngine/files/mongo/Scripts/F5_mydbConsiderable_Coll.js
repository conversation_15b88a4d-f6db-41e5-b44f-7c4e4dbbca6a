myDbConsiderableCollections = function () {
    var collNames = [];
    var limit = 5;
    var collObject = {};
    var considerableCollections = {
        topCollections: [],
        emptyColl: []
    };
    db.getMongo().getDBNames().forEach(function (dbname) {
        if (dbname != "admin" && dbname != "config" && dbname != "local") {
            dbStats = db.getSiblingDB(dbname).stats()
            db.getSiblingDB(dbname).getCollectionInfos().forEach(function (collInfo) {
                var cname = collInfo.name;
                var collType = collInfo.type;
                if (typeof collType == 'undefined' || collType == "collection") {
                    if (cname != "system.profile" && cname != "system.js" && cname != "system.namespaces" && cname != "system.indexes" && cname != "system.views") {
                        var collStats = db.getSiblingDB(dbname).runCommand({ collStats: cname });
                        if (collStats.size == 0) {
                            considerableCollections.emptyColl.push({
                                dbName: dbname,
                                collection: cname
                            })
                            return
                        }
                        var totalStorageUnusedSize = 0;
                        var totalStorageSize = collStats['storageSize'] + collStats['totalIndexSize'];
                        Object.keys(collStats.indexDetails).forEach(function (key) {
                            var freeIndexSize = collStats['indexDetails'][key]['block-manager']['file bytes available for reuse'];
                            totalStorageUnusedSize += freeIndexSize;
                        })
                        totalStorageUnusedSize += collStats['wiredTiger']['block-manager']['file bytes available for reuse'];
                        try {
                            var firstDocument = db.getSiblingDB(dbname).getCollection(cname).findOne()._id.getTimestamp()
                        } catch (e) {
                            var firstDocument = null;
                        };
                        collObject = {};
                        collObject.collection = cname,
                            collObject.dbName = dbname,
                            collObject.dbSize = sizeConversion(dbStats.dataSize),
                            collObject.dbStorageSize = sizeConversion(dbStats.storageSize),
                            collObject.collectionSize = collStats.size,
                            collObject.collectionStorageSize = collStats.storageSize,
                            collObject.collectionIndexCount = collStats.nindexes,
                            collObject.collectionIndexSize = sizeConversion(collStats.totalIndexSize),
                            collObject.fragmentation = sizeConversion(totalStorageUnusedSize),
                            collObject.fragmentationRatio = ((totalStorageUnusedSize * 100.0) / totalStorageSize).toFixed(2) + "%"
                        collObject.archivalRecommendation = false
                        if (firstDocument != null) {
                            collObject.firstDocumentDate = firstDocument.toISOString().split('T')[0]
                            if (Math.floor(ISODate().getTime() / 1000.0) - Math.floor(firstDocument.getTime() / 1000.0) > 8766 * 3600 * 2) {
                                collObject.archivalRecommendation = true
                            }
                        }
                        collNames.push(collObject)
                    }
                }
            })
        }
    })

    collNames.sort(function (r, l) {
        return l.collectionSize - r.collectionSize || l.collectionStorageSize - r.collectionStorageSize;
    }).forEach(function (d) {
        d.collectionSize = sizeConversion(d.collectionSize)
        d.collectionStorageSize = sizeConversion(d.collectionStorageSize)
        if (limit-- <= 0) {
            return;
        }
        considerableCollections.topCollections.push(d)
    })
    return considerableCollections;
}
