myDb_TTL_DuplicateIndexes = function () {
    var DuplicateIndexes = [];
    var TtlIndexes = [];
    db.getMongo().getDBNames().forEach(function (dbname) {
        if (dbname != "admin" && dbname != "config" && dbname != "local") {
            var dbStats = db.getSiblingDB(dbname).stats();
            db.getSiblingDB(dbname).getCollectionInfos().forEach(function (collInfo) {
                var cname = collInfo.name;
                var collType = collInfo.type;
                if (typeof collType == 'undefined' || collType == "collection") {
                    if (cname != "system.profile" && cname != "system.js" && cname != "system.namespaces" && cname != "system.indexes" && cname != "system.views") {
                        var indexes = [];
                        var collStats = db.getSiblingDB(dbname).getCollection(cname).stats();
                        db.getSiblingDB(dbname).getCollection(cname).getIndexes().forEach(function (idx) {
                            if (idx.hasOwnProperty('expireAfterSeconds')) {
                                TtlIndexes.push({
                                    DBName: dbname,
                                    CollName: cname,
                                    DBStrSize: sizeConversion(dbStats.storageSize),
                                    CollStrSize: sizeConversion(collStats.storageSize),
                                    IdxCount: collStats.nindexes,
                                    Ttl_Index: idx.key,
                                })
                            }
                            if (JSON.stringify(idx.key) != '{"_id":1}' && !idx.hasOwnProperty('partialFilterExpression') && !idx.hasOwnProperty('collation'))
                                indexes.push((((JSON.stringify(idx.key)).replace("{", "")).replace("}", "")).replace(/,/g, "|"));
                        });
                        for (var k1 = 0; k1 < indexes.length; k1++) {
                            var dupIndexes = [];
                            for (var k2 = 0; k2 < indexes.length; k2++) {
                                if (k1 != k2) {
                                    if (indexes[k2].startsWith(indexes[k1], 0)) {
                                        dupIndexes.push(JSON.parse('{' + indexes[k2].replace(/\|/g, ',') + '}'))
                                    }
                                }
                            }
                            if (dupIndexes.length > 0) {
                                var preIndex = '{' + indexes[k1].replace(/\|/g, ",") + '}'
                                DuplicateIndexes.push({
                                    DBName: dbname,
                                    CollName: cname,
                                    DBStrSize: sizeConversion(dbStats.storageSize),
                                    CollStrSize: sizeConversion(collStats.storageSize),
                                    IdxCnt: collStats.nindexes,
                                    DpIdx: JSON.parse(preIndex),
                                    AlIdxCnt: dupIndexes.length,
                                    AlIdx: dupIndexes
                                })
                            }
                        }
                    }
                }
            });
        }
    });
    var mydbIndexes = {};
    var Rating = 0
    if (TtlIndexes.length) {
        mydbIndexes.TtlIndexes = TtlIndexes;
        Rating += 3;
    }
    if (DuplicateIndexes.length) {
        mydbIndexes.DuplicateIndexes = DuplicateIndexes;
        DuplicateIndexes.length < 20 ? Rating += 3 : Rating += 2;
    }
    return [mydbIndexes, Rating];
}
