unusedIndex = function () {
    slave();
    const data1 = [];
    db.getMongo().getDBNames().forEach(function (dbname) {
        if (dbname != "admin" && dbname != "config" && dbname != "local") {
            db.getSiblingDB(dbname).getCollectionInfos().forEach(function (collInfo) {
                var cname = collInfo.name;
                var collType = collInfo.type;
                if ((collType == "collection" || collType == undefined) && cname != "system.profile" && cname != "system.js" && cname != "system.namespaces" && cname != "system.indexes" && cname != "system.views") {
                    var output = db.getSiblingDB(dbname)[cname].aggregate([{
                        $indexStats: {}
                    }, {
                        $project: {
                            dayssince: {
                                $trunc: {
                                    $divide: [{
                                        $subtract: [new Date(), '$accesses.since']
                                    }, 1000 * 60 * 60 * 24]
                                }
                            },
                            host: 1,
                            "accesses.ops": 1,
                            "name": 1,
                            "accesses.since": 1,
                            "key": 1
                        }
                    }]);
                    output.forEach(function (findUnused) {
                        if (findUnused.accesses.ops == 0 && findUnused.name != "_id_") {
                            var Index_Size = sizeConversion(db.getSiblingDB(dbname)[cname].stats().indexSizes[findUnused.name]);
                            var Collection_Compressed_Size = db.getSiblingDB(dbname)[cname].stats().storageSize;
                            var Index = db.getSiblingDB(dbname)[cname].getIndexes();
                            Index.forEach(function (findIndexname) {
                                if (findIndexname.name == findUnused.name) {
                                    var IndexType = "";
                                    if (findIndexname.partialFilterExpression != undefined) {
                                        IndexType = "partial";
                                    } else if (findIndexname.sparse == true) {
                                        IndexType = "sparse";
                                    } else if (findIndexname.unique == true) {
                                        IndexType = "unique";
                                    } else if (findIndexname.expireAfterSeconds) {
                                        IndexType = "ttl";
                                    } else if (findIndexname.textIndexVersion) {
                                        IndexType = "text";
                                    } else if (JSON.stringify(findIndexname.key).match("2dsphere")) {
                                        IndexType = "2dsphere";
                                    } else if (JSON.stringify(findIndexname.key).match("2d")) {
                                        IndexType = "2d Index";
                                    } else if (findIndexname.collation) {
                                        IndexType = "collation";
                                    } else if (findIndexname.hidden == true) {
                                        IndexType = "hidden";
                                    } else if (findIndexname.wildcardProjection || JSON.stringify(findIndexname.key).match('\\$\\*\\*')) {
                                        IndexType = "wildcard";
                                    } else if (JSON.stringify(Object.values(findIndexname.key)).match("hashed")) {
                                        IndexType = "hashed";
                                    } else if (JSON.stringify(Object.values(findIndexname.key)).match("geoHaystack")) {
                                        IndexType = "geoHaystack";
                                    }
                                    var sample = JSON.parse("{ " + "\"dbName\": \"" + dbname + "\", " + "\"collName\": \"" + cname + "\", " + "\"collStSize\": \"" + Collection_Compressed_Size + "\", " + "\"key\": " + JSON.stringify(findUnused.key) + ", " + "\"idxSize\": \"" + (Index_Size) + "\", " + "\"idxType\": \"" + (IndexType) + "\", " + "\"days\": " + (findUnused.dayssince) + ", " + "\"ops\": " + findUnused.accesses.ops + " }");
                                    data1.push(sample);
                                }
                            });
                        }
                    });
                }
            });
        }
    });
    return data1
}
