#!/bin/bash
function Create_URI() {
mongoConURI="mongo"
    if [[ -n "${port}" ]]; then
        mongoConURI="${mongoConURI} --port ${port} "
    fi

    if [[ -n "${username}" ]]; then
        mongoConURI="${mongoConURI} --username ${username} "
    fi

    if [[ -n "${pwd}" ]]; then
        mongoConURI="${mongoConURI} --password ${pwd} "
    fi

    if [[ -n "${authDB}" ]]; then
        mongoConURI="${mongoConURI} --authenticationDatabase ${authDB}"
    fi
    if [[ -n "${host}" ]]; then
        mongoConURI="${mongoConURI} --host ${host}"
    fi
}

function aboveCommandValidate() {
    if [[ "$1" != 0 ]]; then
        echo "$(date "+%F %T") Error: Issue with $2" >>"${errorLog}"
        exit 1
    fi
}

out="/usr/local/mydbops/healthReport/testStats.out"
errorLog="/usr/local/mydbops/healthReport/log/error.log"
mydbHealthTools="/usr/local/mydbops/healthReport/Scripts/"
dbSctiptFile="/usr/local/mydbops/healthReport/instanceLevelStats.js"

mongoConURI="mongo"
while [ "$1" != "" ]; do
    case $1 in
    -m | --cluster)
        shift
        Access=$1
        ;;
   -u | --username)
        shift
        username=$1
        ;;
    -p | --passwd)
        shift
        pwd=$1
        ;;
    -a | --authenticationDatabase)
        shift
        authDB=$1
        ;;
    -o | --out)
        shift
        out=$1
        ;;
    -S | --skip)
        shift
        skipIp=$1
        ;;
    *)
        echo "illegal option $1"
        help
        exit 1
        ;;
    esac
    shift
done

count=0
echo "[" >"${out}"
for i in ${Access//,/ }; do
    sshName=$(echo "${i}" | awk -F'|' '{print $1}')
    host=$(echo "${i}" | awk -F'|' '{print $2}')
    port=$(echo "${i}" | awk -F'|' '{print $3}')
    ssh "${sshName}" ls -lrth "${dbSctiptFile}" &>>"${errorLog}"
    aboveCommandValidate "$?" "SSH Connectivity $sshName"
    Create_URI
    ssh "${sshName}" "${mongoConURI} --quiet  --eval \"load('${dbSctiptFile}');instanceLevelStats()\""  >>"${out}"  
    aboveCommandValidate "$?" "Instantlevel Statts $i"
    echo "," >>"${out}"
    ((count++))
done
sed -i '$ s/,/]/' "$out"

if [[ "${count}" == 1 ]]; then
    echo "${mongoConURI} --quiet --eval \"load('${mydbHealthTools}');mydbHealthReport('','','','${out}')\"" >>"${errorLog}"
    Json=$(${mongoConURI} --quiet --eval "load('${mydbHealthTools}');mydbHealthReport('','','','${out}')")
    aboveCommandValidate "$?" "Collecting Mongo Stats"
    echo "$Json" |tee -a "${errorLog}"
    exit 1
fi

if [[ -n "${skipIp}" ]]; then
    skip='['
    for i in ${skipIp//,/ }; do
        skip+="'${i}',"
    done
fi
skipIpList=$(echo "$skip"|sed  's/,$/]/')
echo "${mongoConURI} --quiet --eval \"load('${mydbHealthTools}');mydbHealthReport('${username}','${pwd}','${authDB}','${out}',${skipIpList})\"" >>"${errorLog}"
Json=$(${mongoConURI} --quiet --eval "load('${mydbHealthTools}');mydbHealthReport('${username}','${pwd}','${authDB}','${out}',${skipIpList})")
aboveCommandValidate "$?" "Collecting Mongo Stats"
echo "$Json" |tee -a "${errorLog}"
