instanceLevelStats =  function (){
    var count = 0;
    var hostInfo = db.hostInfo();
    var mongoConf = db.adminCommand({ getCmdLineOpts: 1 });
    var ServerStat = db.serverStatus();
    var instanceLevelStats = {};
    try { 
        instanceLevelStats.host = ServerStat.repl.me
    } catch(e){
        instanceLevelStats.host = ServerStat.host
    }
    instanceLevelStats.numaEnabled = hostInfo.system.numaEnabled;
    instanceLevelStats.maxOpenFiles = hostInfo.extra.maxOpenFiles
    try {
        instanceLevelStats.mongoUser = cat('/proc/' + ServerStat.pid + '/status').match(/Uid:\s*(\S*)/i)[1] == 0 ? 'root' : hostInfo.os.name == 'Ubuntu' ? 'mongodb' : 'mongod';
    } catch (e) {
        instanceLevelStats.mongoUser = "N/A";
    }
    instanceLevelStats.dirtyRatio = cat("/proc/sys/vm/dirty_ratio").replace(/(\r|\n)/gm, "");
    instanceLevelStats.dirtyBackgroundRatio = cat("/proc/sys/vm/dirty_background_ratio").replace(/(\r|\n)/gm, "");
    instanceLevelStats.swappiness = cat("/proc/sys/vm/swappiness").replace(/(\r|\n)/gm, "");

    instanceLevelStats.logRotate = 'disabled'
    if (mongoConf.parsed.processManagement.pidFilePath) {
        ls('/etc/logrotate.d').forEach(function (file) {
            if (file.startsWith('/etc/logrotate.d/mongo')) {
                listFiles(mongoConf.parsed.systemLog.path.match("\/.*\/")[0]).forEach(function (file) {
                    if (!file.isDirectory && file.baseName.startsWith('mongo')) {
                        count++
                    }
                })
                if (count > 4) {
                    instanceLevelStats.logRotate = 'Enabled'
                    instanceLevelStats.logRetention = count;
                }
                return
            }
        })
    }
    listFiles("/sys/kernel/mm/").forEach(function (file) {
        if (file.isDirectory == true) {
            if (file.baseName == "transparent_hugepage") {
                hugePage = cat("/sys/kernel/mm/transparent_hugepage/enabled").replace(/(\r|\n)/gm, "").split(" ");
                defrag = cat("/sys/kernel/mm/transparent_hugepage/defrag").replace(/(\r|\n)/gm, "").split(" ");
                return;
            } else if (file.baseName == "redhat_transparent_hugepage") {
                hugePage = cat("/sys/kernel/mm/redhat_transparent_hugepage/enabled").replace(/(\r|\n)/gm, "").split(" ");
                defrag = cat("/sys/kernel/mm/redhat_transparent_hugepage/defrag").replace(/(\r|\n)/gm, "").split(" ");
                return;
            }
        }
    });

    if (hugePage) {
        hugePage.forEach(function (x) {
            if (x.match(/\[.*?\]/)) {
                instanceLevelStats.transparentHugepage = x.replace(/[\[\]']+/g, '');
            }
        });
    }

    if (defrag) {
        defrag.forEach(function (x) {
            if (x.match(/\[.*?\]/)) {
                instanceLevelStats.defrag = x.replace(/[\[\]']+/g, '');
            }
        });
    }

    try {
        function diskCheck(partition, type) {
            var check = false;
            if (type == false) {
                hostInfo.extra.mountInfo.forEach(function (disk) {
                    if (disk.mountPoint == partition) {
                        instanceLevelStats.diskVolumeName = disk.source;
                        instanceLevelStats.diskMountPoint = disk.mountPoint;
                        instanceLevelStats.Partition = dataPartition;
                        instanceLevelStats.diskOptions = disk.options;
                        instanceLevelStats.diskFtype = disk.type;
                        check = true;
                        return;
                    }
                });
            } else {
                cat("/proc/mounts").split("\n").forEach(function (disk) {
                    if (partition == disk.split(" ")[1]) {
                        instanceLevelStats.diskVolumeName = disk.split(" ")[0];
                        instanceLevelStats.diskMountPoint = disk.split(" ")[1];
                        instanceLevelStats.partition = dataPartition;
                        instanceLevelStats.diskOptions = disk.split(" ")[3];
                        diskStats.diskFtype = disk.split(" ")[2];
                        check = true;
                        return;
                    }
                });
            }

            if (check == true && partition != "/") {
                return true;
            } else {
                return;
            }
        }

        if (mongoConf.parsed.storage.dbPath) {
            dataPartition = mongoConf.parsed.storage.dbPath;
            if (hostInfo.extra.mountInfo) {
                manualInfo = false;
            } else {
                manualInfo = true;
            }

            var len = dataPartition.split("/").length;
            for (var i = len; i > 0; i--) {
                if (i != len && i != 1) {
                    split = dataPartition.split("/").slice(0, -1).join("/");
                    out = diskCheck(split, manualInfo);
                    if (out == true) {
                        break;
                    }
                } else if (i == 1 && len == 1) {
                    out = diskCheck(dataPartition, manualInfo);
                    if (out == true) {
                        break;
                    } else {
                        diskCheck("/", manualInfo);
                    }
                } else if (i == 1) {
                    diskCheck("/", manualInfo);
                } else {
                    out = diskCheck(dataPartition, manualInfo);
                    if (out == true) {
                        break;
                    }
                }
            }
        } else {
            instanceLevelStats.diskInfo = "N/A";
        }
    } catch (err) {
        instanceLevelStats.diskInfo = "N/A";
    }
    return instanceLevelStats
}
