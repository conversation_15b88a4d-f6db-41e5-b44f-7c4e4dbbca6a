<!DOCTYPE html>
<html>
<head>

<style>
@import url(http://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300ita‌​lic,400italic,500,500italic,700,700italic,900italic,900);
* 
{
  font-family: Roboto;
}

.footer {
  width: 100%;
  height: auto;
  border-top: 1px solid #44bd32;
  font-size: 14px;
}

.footer_split {
  width: 100%;
  height: auto;
  margin-top: 8px;
}

.footer_split_left {
  width: 33%;
  float: left;
}

.footer_split_middle {
  width: 33%;
  float: left;
}

.footer_split_right {
  width: 33%;
  float: right;
}

.page_no {
  float: right;
}

.location_icon {
  width: 8%;
  float: left;
}

.address {
  width: 92%;
  float: left;
}

.tel_email_icons {
  width: 7%;
  float: left;
}

.tel_email {
  width: 93%;
  float: left;
}

.web_icon {
  width: 7%;
  float: left;
}
.web_page {
  float: right;
  width: 93%;
}

.currrent_page
{
  float: right;
  padding-right: 10px;
}

    </style>
  <script>
    function subst() {
      var vars = {};
      var query_strings_from_url = document.location.search.substring(1).split('&');
      for (var query_string in query_strings_from_url) {
        if (query_strings_from_url.hasOwnProperty(query_string)) {
          var temp_var = query_strings_from_url[query_string].split('=', 2);
          vars[temp_var[0]] = decodeURI(temp_var[1]);
        }
      }
      var css_selector_classes = ['page', 'topage',];
      for (var css_class in css_selector_classes) {
        if (css_selector_classes.hasOwnProperty(css_class)) {
          var element = document.getElementsByClassName(css_selector_classes[css_class]);
          for (var j = 0; j < element.length; ++j) {
            element[j].textContent = vars[css_selector_classes[css_class]];
          }
        }
      }
    }
  </script>
</head>
<body onload="subst()">
  <br><br>
  <div class="footer">
    <div class="footer_split">
      <div class="footer_split_left">
        <div class="tel_email_icons">
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAONSURBVHgBzZjdcdpAEMd3T9h58oxKoIPIpIAIA5nJU+wKMBWYVABUEKjAuAKcp8wEsKgAqwRK4DEx0m12z5IjhITBQUz+D0YfJ91Pu3u7ewb4z4R5N9yRY1tn4CLBctLwZ3AkKciBKZ1ZHqIagVJefVppw5G0YSH3h1M+OVUeAZQJaImANl9eBk/6fPbZX0DBUltgFqGicyIY8C279A5v4QhSW2Cqs6q/CC3dlXMgdI/hOkzDAJEfWHQlMPGg+thxJZbgCK4zFrJOVScBU03CiGSVJVw3ggKloj+u/KKmFsMsswZO6/M2IPnsOqc+rnShSCBjHdbqBBbbBgcBtaKnOo2fjgNFAXEgLeS3FMDWSWaffB809OSYFHahKCBN8D06dbcNluBHBU055vy0gKKAFIJvziz6uG2w5KI4+McSU0UBrZS+54mWkmtcz7GzBta9SkfuS06StAAFyQDJykJZQSBxpDa+3OQhDV05jhMmFKS/pSOkXnTlJj0oKIFvLCj6BYXqBcgkPykRnPwuppXr5CBjQW0SIxRd09aKKwMZK1kAnfTAoKT7cZzVJs4lHAPooeYPxUqyktKFVKxEoA0w90m3kgKgAG02aERfo6NOesUxcJ/Lxwyimpa3Ig8KNK379y+TarXpOqSrqB1xSoTeoaEyW1ietBWtqnbNc5rJe+I6WfprUAd0X26TfzF12grUN8jpgdgyZUujxyWkLLVw9aSrWX3SWq+loDupznvwFiBRY3I+JMRm3oRJKHOBJwxAD+IWpuZ9aHJL0+dDOzFhLnx0P18SH+IScY30QuzKala/ZPoj9ZwqZMIQoKcUvOfsblYqEt0R0ZA7hNs8+J2ANqwgUL+5vc1yTdpasbhdmTTm3Tx40rqV3Pe9CpSe7DWTm7pnYYdbXofddZW1yUzDc3wNwyfdk3fuBJQFJW55qM2H8A9KW0s+dGegCMq2QuwjB3p0qR8o/rKcPnzHd5Y5TkcSp7xtH6h9HpaJp/XH67iNZbVPtHpMF+O9tOIVSFiWwxC1v5eFktoIYsnu3MLs848JlzcKJcvs92xZiWP+2DcDxeIEyi/BThJME969Fl9ZMOZxOIBMHATqmhQ1YzAJUs1eRq3v0laLdsKjNEz03GElFlOEN9yjrG+p2HJEuOBWuSw91fOldZhCgGIZq2m4ZLAvMcA6Hwyydi6FAaXgbLMJVZwsuVivFNznpYo/T0sKpuK+NmMAAAAASUVORK5CYII=" width=16 height=16><br>
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAG+SURBVHgB7ZbNWcJAEIZnlx9vPpQAHUBoIDHLwZtWIFQgVGCsAKwAqQBuHiCECkhKSAmcVXacwQTyAPIrjwfzXrLJTma+bzbJBiAl5b8j4oHtVR+Exg4NC3BBqGCopXZcK+jxuYwnpEbn0sUZBCgKLTvxeTY5AWtKP961NbkNQjgD861czF6JLqAwE5eXRuXGHVpbCBiyoGxe+so1mnAiNa/6yDm4OOfk3OsxGwJGtWAyl2ghYi9S2rZdo8tO4EA4Vo0rHkbPlEB4oZwVzr1XADOxgtBVfl2DbrByWo56Li+9G9eowx7sUfUu4XqGqO+HatqknLNt8XJXsrEdvMbd4CWh4O5P3TD75YLyjLYQ2GfXgMidrLgqGOyqsVMAk+hGi0TM4m6oYdmMY3icuybXGprsmo6tkfItvndf/iwcCHWjY3rlQUYLj96RIkgS4RkOsNtFYYJdZ7Axsf3w0LwHC2AiRyU1pMISnqiws5xk1zW/A0eydwm2MapNnU+pS9+vK4Y0pid8enRx5qgOJIm7AWdyUgd+k1RAKuDPBST/B/gzW6DtF+HCLLbmCLm6yN/61cQFi89wLp4hJSUl4gvFYtO0AUndpAAAAABJRU5ErkJggg==" width=16 height=16>
        </div>
	<div class="tel_email">
          <a href="https://wa.me/916382664042" target="_blank">+916382664042</a><br><EMAIL>
        </div>
      </div>
      <div class="footer_split_middle">
        <div class="location_icon"><img src="data:image/png;base64,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" width=16 height=16></div>
        <div class="address">202,2nd Floor, Vanguard Rise,5th cross, HAL Old Airport Rd, Konena Agrahara,<br>Bangalore, Karnataka - 560017</div>
      </div>
    </div>
    <div class="footer_split_right">
      <div class="page_no">
        <div class="web_icon"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIVSURBVHgB1VTNcdNAFH5vpfjGICqwUkE2pgBkLB04xSU4FYQOYioIqQBSAc6JGWIhp4FEqSCiAjTDLfHu49t15JGVmMmBA7wZaXffz7fvd4n+deJtgvRCJ6TUAbOMRTheaUuJfWnu7IfFu7J6FmDyRUdhpI7FyoSFKmGKjJKhkwWWC2aqRShi4tlSAXhY1m179QjsRVCwkVcA2WVmDwajyn1s5ZDg7Q54AOVQuEgKHW0FdJ4xyeVFej0JTJCIiAdq5POsXLiwl0uK8/TqPVu6Ca06fjLk5KuOw566xc2nCLWmQN54geHLtsEDPwL/3KUDKTgia4f+sraHQQ95I/rswUBIvsZXUYfE8g09FMnpwmbGrMZdPUrnr6+zb1o351E++NnNj48EvBSy1jnO8sHtY8B8IH86P1dX0V+mNSCqU73N9/vNGVfW20LGsu69xKVJpGzhrGg0H3xExfqEVvCAio6U0JkIbTQu+H0YjaF32pzdmo+uDt0arpHFzoiVm4QfDgQj56Zkr9s24MdYSu+Lopf4TzAxu/QUOS9R3U9u72Y5/b5fdHUcbzTXvk3SYnCSwaYt3yiKCewUHmhU7SQMMcfoRf9INPlC88P72PyiRVbgYiPJPWw2Iuh64JIeGDVVTAfCUrNwdH9n/eOw01MFLqmRnxhNfebAuo/D1ufLNWxgeAqFPSROr5SlssLnLt/NqP1/9BtvLwNP0X9NcAAAAABJRU5ErkJggg==" width=16 height=16></div>
        <div class="web_page">
          &nbsp;&nbsp;&nbsp;www.mydbops.com
          <br> 
          <div class="currrent_page"><span class="page"></span> of <span class="topage"></span>
          </div>
        </div>
      </div>
    </div>

  </div>

</body>

</html>