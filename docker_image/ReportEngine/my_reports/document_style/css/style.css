@import url(http://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300ita‌​lic,400italic,500,500italic,700,700italic,900italic,900);
* 
{
  font-family: Roboto;
  font-size: 14pt;
  color: #000000;
  font-weight: 300;
}


h1
{
  font-size: 27pt !important;
}

h2
{
  font-size: 22pt !important;
  font-weight: 600;
  padding-bottom: 15pt !important; 
  margin-block-start: 0em;
  margin-block-end: 40pt !important;
}

h2 span
{
  font-size: 22pt !important;
  font-weight: 300;
  text-transform: capitalize;
}

h3
{
  font-size: 20pt !important;
  text-transform: uppercase;
  font-weight: 400;
  /* padding-bottom: 20pt !important; */
  margin-block-start: 0em;
  margin-block-end: 30pt !important;
  margin-block-end: 1em;
}

h4
{
  font-size: 20pt !important;
  font-weight: 400;
  margin-block-start: 0em;
  margin-block-end: 20pt !important;
  margin-block-end: 1em;
}

h5
{
  font-size: 18pt !important;
  font-weight: 400;
  margin-block-end: 1em;
}

h6
{
  font-size: 15.5pt !important;
  font-weight: 400;
  margin-block-end: 1em;
}

.first_page 
{
  page-break-after: always;
}

.home_report {
position: absolute;
margin: auto;
top: 230px;
right: 0;
bottom: 0;
left: 0;
}

.create_date
{
position: absolute;
margin: auto;
top: 150px;
left: 0;
right: 0;
}

.file_title
{
  text-align: center;
  font-size: 42pt !important;
}
.file_title_report_type
{
  text-align: center;
  font-size: 32pt !important;
}
.file_title_client
{
  padding-top: 20px;
  text-align: center;
  font-size: 27pt !important;
}

.file_title_create_date
{
  text-align: center;
  font-size: 24pt !important;
}

/* .title_node
{
  border-bottom:1px sold #44bd32 ;
} 
*/


.footer {
width: 100%;
height: auto;
font-family: montserrat;
/* background-color: #44bd32; */
border-top: 1px solid #44bd32;
font-size: 14px;
}

.footer_split {
width: 100%;
height: auto;
margin-top: 8px;
}

.footer_split_left {
width: 30%;
float: left;
}

.footer_split_middle {
width: 30%;
float: left;
}

.footer_split_right {
width: 40%;
float: left;
}
  


a 
{
  color:#28952b;
  font-weight: bold;
  text-decoration:none;
}
p 
{
  text-align: justify;
  line-height: 30px;
}


.front {
height: 8cm;
}
.break 
{
  page-break-before:always;
}
.dbreak 
{
  page-break-inside: avoid;
}

.dbreak_auto 
{
  page-break-inside: auto;
}

img {
height: auto;
page-break-inside: avoid;
}

.img_shadow
{
  /* box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); */
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, .28), 0 0px 11px 0 rgba(0, 0, 0, 0.19);
}

.rimg {
width: 90%;
border-radius: 8px;
}
/* 
.unite {
width: 100%;
border-radius: 8px;
display: flex;
}

.unite div
{
  flex: 23%;
  padding: 5px;
}

.unite img
{
  padding: 5px;
}
 */
.unite
{
    width: 100%;
    text-align: center;
    vertical-align: middle;
}

.unite div
{
    width: 22%;
    padding: 10px;
    display: inline-block;
}

.unite img
{
  width: 100%;
}

/* 
body {counter-reset: h2}
h2 {counter-reset: h3}
h3 {counter-reset: h4}
h4 {counter-reset: h5}
h5 {counter-reset: h6}

h2:before {counter-increment: h2; content: counter(h2) ". "}
h3:before {counter-increment: h3; content: counter(h2) "." counter(h3) ". "}
h4:before {counter-increment: h4; content: counter(h2) "." counter(h3) "." counter(h4) ". "}
h5:before {counter-increment: h5; content: counter(h2) "." counter(h3) "." counter(h4) "." counter(h5) ". "}
h6:before {counter-increment: h6; content: counter(h2) "." counter(h3) "." counter(h4) "." counter(h5) "." counter(h6) ". "}

h2.nocount:before, h3.nocount:before, h4.nocount:before, h5.nocount:before, h6.nocount:before { content: ""; counter-increment: none }  
*/

/* 
.clear_header {counter-reset: h2;}
h2 {counter-reset: h3;}
h3 {counter-reset: h4;}
h4 {counter-reset: h5;}
h2:before {counter-increment: h2;content: counter(h2)". ";}
h3:before {counter-increment: h3;content: counter(h2)"." counter(h3)". ";}
h4:before {counter-increment: h4;content: counter(h2)"." counter(h3)"." counter(h4)". ";}
h5:before {counter-increment: h5;content: counter(h2)"." counter(h3)"." counter(h4)"." counter(h5)". ";} */

.separate
{
    width: 100%;
}
.separate img
{
    width: 90%;
}

.table_index h2,
.table_index h3,
.table_index h4,
.table_index h5,
.table_index h6 {
  font-weight: bold;
}

.table_index h2 span {
  font-weight: bold;
  color: #38761D;
}

.table_index a {
  font-weight: bold;
}

.table_index span {
  font-weight: bold;
}
 

table 
{
margin: auto;
border-collapse: collapse;
width: 100%;
margin-block-start: 0pt;
margin-block-end: 20pt;
max-width: 100%;
}

.table table .role_table td:nth-child(11){
  min-width: 250px !important;
  max-width: 250px !important;
}

.table table .role_table {
  width: 900px !important;
}

.role_table td
{
  margin: 0px;
  max-width: 900px;
  min-width: fit-content;
}

.fixed table 
{
  margin: auto;
  max-width: 1101px !important;
  table-layout: fixed !important;
}

.table table
{
  margin: auto;
  max-width: 100%;
}

.table table th
{
  /* 
  max-width: 125px; 
  */
  font-weight: 400;
}

tr
{
  border-bottom-width: 1px;
  border-bottom-style: dashed;
  border-bottom-color: #e4e6ef;
  border-radius: 2px;
  page-break-inside: avoid;
}

th 
{
  padding: 20px 30px;
  page-break-inside: avoid;
  border: none;
}

th, td 
{
  min-width: 85px;
  /* max-width: 150px; */
  text-align: left;
  border-collapse: collapse;
  padding: 10px;
  /* display: -webkit-box;  */
  word-break: break-word; 
}


.sum tr:last-child 
{
  background-color: rgb(172, 172, 172);
  color: #000;
  font-weight: bold;
}

.text_center
{
  text-align: center !important;
}


.hide
{
  display: none;
}

.header_green_text th
{
  color: #35A339;
}

.mongo_vertical_header_160,.mongo_vertical_header_180 tr:first-child {
  color: #35A339;
}

.mongo_vertical_header_160 th
{
  width: 160px !important;
  max-width: 160px !important;
  padding: 10px !important;
}

.mongo_vertical_header_180 th
{
  width: 185px !important;
  max-width: 185px !important;
  padding: 10px !important;
}

.mongo_vertical_header_160 td
{
  padding: 10px 2px 10px 2px !important;
}

.mongo_vertical_header_180 td
{
  padding: 10px 2px 10px 2px !important;
}


.header_green_text tr:nth-child(odd) 
{
  background-color: #f9faf8; /*#f5f5f5;*/
}

.header_green_text td table
{
  border: none;
}

.header_green_text td th, .header_green_text td td
{
  font-size: 16px;
}

.break_before_table
{
  display: block;
}

.break_before_table table
{
  page-break-before: always;
}

.table_width50
{
  margin: auto;
}

.table_width501 table
{
  width: 50% !important;
  float: left !important;
  position: relative !important;
  left: 0 !important;
}


.index_header
{
  margin: 0px;
  margin-block-start: 0px !important;
  padding: 0px !important;
  margin-block-end: 0px !important;
  color: #4c4c4c;
}


.index_header h3
{
  font-weight: 500;
}

.table_index h3
{
  padding-left: 30px !important;
}

.table_index h4
{
  padding-left: 60px !important;
}

.table_index h5
{
  padding-left: 90px !important;
}

.table_index h6
{
  padding-left: 125px !important;
}

/* Apply colors to specific headings on the index page */

/* NODE-Demo_database_node */
.table_index h2 a {
  color: #38761D; /* Heading color */
}

/* Target span elements inside h2.index_header within .table_index */
 table_index h2.index_header span {
  color: #38761D;
}

/* Server Information */
.table_index h3:nth-of-type(1) a {
  color: #B45F06; /* Subheading color */
}

/* System Information, CPU Information, Memory Information, Network Information */
.table_index h4:nth-of-type(1) a,
.table_index h4:nth-of-type(2) a,
.table_index h4:nth-of-type(3) a,
.table_index h4:nth-of-type(4) a {
  color: #45BB35; /* Sub-subheading color */
  font-weight: inherit;
  font-size: inherit;
}

/* Database Statistics */
.table_index h3:nth-of-type(2) a {
  color: #B45F06; /* Subheading color */
}

/* Top 10 Fragmented Tables, Open Host Users, Super Privilege Users, Active Tables By Write Operation, Active Tables By Read Operation */
.table_index h4:nth-of-type(5) a,
.table_index h4:nth-of-type(6) a,
.table_index h4:nth-of-type(7) a,
.table_index h4:nth-of-type(8) a,
.table_index h4:nth-of-type(9) a {
  color: #45BB35; /* Sub-subheading color */
  font-weight: inherit;
  font-size: inherit;
}

/* PMM GRAPHS */
.table_index h3:nth-of-type(3) a {
  color: #B45F06; /* Subheading color */
}

/* System Information, MySQL Statistics, MySQL Critical Statistics */
.table_index h4:nth-of-type(10) a,
.table_index h4:nth-of-type(11) a,
.table_index h4:nth-of-type(12) a {
  color: #45BB35; /* Sub-subheading color */
  font-weight: inherit;
  font-size: inherit;
}

/* MySQL Connections, MySQL Client Thread Activity, InnoDB Buffer Pool Dirty Data, InnoDB Log File Usage Hourly, Top Command Counters */
.table_index h5:nth-of-type(1) a,
.table_index h5:nth-of-type(2) a,
.table_index h5:nth-of-type(3) a,
.table_index h5:nth-of-type(4) a,
.table_index h5:nth-of-type(5) a {
  color: #000000; /* Sub-sub-subheading color (black) */
  font-weight: inherit;
  font-size: inherit;
}

/* QUERY ANALYTICS */
.table_index h3:nth-of-type(4) a {
  color: #B45F06; /* Subheading color */
}

/* Top Slow Queries By Execution Count, Top Slow Queries By Execution Time, Top Slow Queries By Disk Temp Table, Top Slow Queries By Full Table Scan */
.table_index h4:nth-of-type(13) a,
.table_index h4:nth-of-type(14) a,
.table_index h4:nth-of-type(15) a,
.table_index h4:nth-of-type(16) a {
  color: #45BB35; /* Sub-subheading color */
  font-weight: inherit;
  font-size: inherit;
}

body
{
  color: #2e2e2e;
}

.table_index tr
{
  border-bottom-width: 1px;
}

.table_index td
{
  padding: px;
  font-size: 18px !important;
}

.table_index a
{
  cursor: pointer;
}

.table_index h2
{
  padding: 7px;
  font-size: 22px !important;
}

.table_index h3
{
  padding: 7px;
  font-size: 19px !important;
}

.table_index h4
{
  padding: 7px;
  font-size: 18px !important;
  font-weight: 400;
}

.table_index h5
{
  padding: 7px;
  font-size: 17px !important;
  font-weight: 400;
}

.table_index h6
{
  padding: 7px;
  font-size: 16.2px !important;
  font-weight: 400;
}

.table_index span
{
  font-size: 22px !important;
  font-weight: 500 !important;
}

.description
{
  padding: 0px 5px 10px 5px;
  font-weight: 300;
  line-height: 28px;
}

.htov_tr
{
  height: 70px;
  background-color:white !important;
}

.htov_td_div
{
  width: 100%;
  border-bottom: 1px dashed #78be6e;
}

.htov_table th
{
  min-width: 150px;
  font-weight: 400;
  color: #000;
  background-color: #45b549;
  border: 10px solid white;
}


.htov_table tr
{
  border: none;
}

.htov_table tr:nth-child(odd) {
  background-color: white;
}

.data_separator
{
  background-color: white !important; 
  /* height:100px; */
}

.data_separator td
{
  background-color: #fff;
  color: #35A339;
  font-weight: 700 !important;
  padding-bottom: 20px;
}

.td_query
{
  font-family: 'Courier New', Courier, monospace;
  background-color: #dfdfdf;
  color: black;
}

.description_box
{
  border: 2px solid black;
  /* border-left: 1px solid rgb(6, 141, 15); */
  padding: 25px;
}

.description_box h5
{
  font-weight: 400 !important;
}

.thank_you{
  text-align: center;
  font-size: 80px;
  font-weight: 500;
  padding-top: 330px;
}

strong{
  font-weight: bold !important;
}

ul{
  line-height:25px;
}

li {
  margin-bottom: 10px;
  text-align: justify;
}

li:last-child {
 margin-bottom: 0px;
 text-align: justify;
}

.recommendation_tilte{
  font-weight: 400;
  line-height: 20px;
}

