@import url(http://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300ita‌​lic,400italic,500,500italic,700,700italic,900italic,900);
* 
{
  font-family: Roboto;
  font-size: 10pt;
}

h1
{
  font-size: 25pt !important;
}

h2
{
  font-size: 20pt !important;
  font-weight: 600;
  padding-bottom: 30pt;
  margin-block-start: 0em;
  margin-block-end: 40pt;
}

h2 span
{
  font-size: 20pt !important;
  font-weight: 300;
  text-transform: capitalize;
}

h3
{
  font-size: 18pt !important;
  text-transform: uppercase;
  font-weight: 400;
  padding-bottom: 20pt;
  margin-block-start: 0em;
  margin-block-end: 30pt;
}

h4
{
  font-size: 18pt !important;
  font-weight: 300;
  margin-block-start: 0em;
  margin-block-end: 20pt;
}

h5
{
  font-size: 16pt !important;
  font-weight: 300;
}

.first_page 
{
  page-break-after: always;
}


.home_report {
position: absolute;
margin: auto;
top: 230px;
right: 0;
bottom: 0;
left: 0;
}

.create_date
{
position: absolute;
margin: auto;
top: 150px;
left: 0;
right: 0;
}

.file_title
{
text-align: center;
}

.title_node
{
border-bottom:1px sold #44bd32 ;  
}


.footer {
width: 100%;
height: auto;
font-family: montserrat;
/* background-color: #44bd32; */
border-top: 1px solid #44bd32;
font-size: 10px;
}

.footer_split {
width: 100%;
height: auto;
margin-top: 8px;
}

.footer_split_left {
width: 30%;
float: left;
}

.footer_split_middle {
width: 30%;
float: left;
}

.footer_split_right {
width: 40%;
float: left;
}
  


a 
{
  color: teal;
  font-weight: bold;
  text-decoration:none;
}
p 
{
  text-align: justify;
}


.front {
height: 8cm;
}
.break {
page-break-before:always;
}
.dbreak {
page-break-inside: avoid;
}
img {
height: auto;
/* box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); */
page-break-inside: avoid;
}
.rimg {
width: 90%;
border-radius: 8px;
}
/* 
.unite {
width: 100%;
border-radius: 8px;
display: flex;
}

.unite div
{
  flex: 23%;
  padding: 5px;
}

.unite img
{
  padding: 5px;
}
 */
.unite
{
    width: 100%;
    text-align: center;
    vertical-align: middle;
}

.unite div
{
    width: 22%;
    padding: 10px;
    display: inline-block;
}

.unite img
{
  width: 100%;
}


body {counter-reset: h2}
h2 {counter-reset: h3}
h3 {counter-reset: h4}
h4 {counter-reset: h5}
h5 {counter-reset: h6}

h2:before {counter-increment: h2; content: counter(h2) ". "}
h3:before {counter-increment: h3; content: counter(h2) "." counter(h3) ". "}
h4:before {counter-increment: h4; content: counter(h2) "." counter(h3) "." counter(h4) ". "}
h5:before {counter-increment: h5; content: counter(h2) "." counter(h3) "." counter(h4) "." counter(h5) ". "}
h6:before {counter-increment: h6; content: counter(h2) "." counter(h3) "." counter(h4) "." counter(h5) "." counter(h6) ". "}

h2.nocount:before, h3.nocount:before, h4.nocount:before, h5.nocount:before, h6.nocount:before { content: ""; counter-increment: none } 


.separate
{
    width: 100%;
}
.separate img
{
    width: 100%;
}



  
table 
{
border-collapse: collapse;
width: 100%;
margin-block-start: 0pt;
margin-block-end: 20pt;
}

tr
{
  border-bottom-width: 1px;
  border-bottom-style: dashed;
  border-bottom-color: #e4e6ef;
  border-radius: 2px;
  page-break-inside: avoid;
}

th 
{
  padding: 20px 30px;
  font-size: 14px;
  font-weight: bolder;
  page-break-inside: avoid;
  background-color:#fff;
  color: #3b3b3c;
  border: none;
}

th, td 
{
  text-align: left;
  padding: 8px;
  border-collapse: collapse;
}

td
{
  color: #585656;
}

.static_table
{
  width: inherit;
}

.sum tr:last-child 
{
  background-color: dimgrey;
  color: #000;
  font-weight: bold;
}

.alternate tr:nth-child(odd) 
{
  background-color: #f9faf8; /*#f5f5f5;*/
}
  
.text_center
{
  text-align: center !important;
}

.without_border th
{
  border: none;
}

.round_header th
{
  border-radius: 16px;
}

.header_green table
{
  left: 10px;
  position: relative;
}

.header_green th
{
  background-color:#44bd32;
  background-color: #dddddd;
  height: 50px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #44bd32;
 
  font-size: 15 !important;
}

.header_text_white th
{
  color: white;
}

/* class for table header */

.header_green_border table
{
  left: 10px;
  position: relative;
}

.header_green_border th
{
  background-color: white;
  border: 1px solid #44bd32 !important;
}

.header_round_border_less_green_bg th
{
  border: 20px solid;
  border-radius: 8px;
  border-color: white;
  background: #44bd32;
  color: white;
  text-align: center;
  padding: 10px;
}

.header_green_bg_border_less_type_2 th
{
  background-color: #44bc32;
  /* border-left: 4px solid #44bd32 !important; */
  border: 40px solid;
  /* border-bottom: 0; */
  border-color: white;
  border-left: 4px solid #44bd32;
  margin-left: 19px;
  color: white;
  border-radius: 50px;
  text-align: center;
}

.header_green_bg_border_less_type_3 th
{
  background-color: #44bc32;
  /* border-left: 4px solid #44bd32 !important; */
  border: 40px solid;
  /* border-bottom: 0; */
  border-color: white;
  border-left: 4px solid #44bd32;
  margin-left: 19px;
  color: white;
  border-radius: 50px;
  text-align: center;
  left: 3px;
  position: relative;
}

.header_green_bg_border_less_type_3 th::before
{
  content: " ";
  position: relative;
}

.hide
{
  display: none;
}

.header_green_text th
{
  color: #35A339;
}

.header_green_text tr:nth-child(odd) 
{
  background-color: #f9faf8; /*#f5f5f5;*/
}

