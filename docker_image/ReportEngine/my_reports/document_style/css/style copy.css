@import url(http://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300ita‌​lic,400italic,500,500italic,700,700italic,900italic,900);
* 
{
  font-family: Roboto;
  font-size: 12pt;
  color: #575656;
}


h1
{
  font-size: 25pt !important;
}

h2
{
  font-size: 20pt !important;
  font-weight: 600;
  padding-bottom: 15pt !important; 
  margin-block-start: 0em;
  margin-block-end: 40pt !important;
}

h2 span
{
  font-size: 20pt !important;
  font-weight: 300;
  text-transform: capitalize;
}

h3
{
  font-size: 18pt !important;
  text-transform: uppercase;
  font-weight: 400;
  /* padding-bottom: 20pt !important; */
  margin-block-start: 0em;
  margin-block-end: 30pt !important;
}

h4
{
  font-size: 18pt !important;
  font-weight: 300;
  margin-block-start: 0em;
  margin-block-end: 20pt !important;
}

h5
{
  font-size: 16pt !important;
  font-weight: 300;
}

.first_page 
{
  page-break-after: always;
}


.home_report {
position: absolute;
margin: auto;
top: 230px;
right: 0;
bottom: 0;
left: 0;
}

.create_date
{
position: absolute;
margin: auto;
top: 150px;
left: 0;
right: 0;
}

.file_title
{
  text-align: center;
  font-size: 40pt !important;
}
.file_title_report_type
{
  text-align: center;
  font-size: 30pt !important;
}
.file_title_client
{
  padding-top: 20px;
  text-align: center;
  font-size: 25pt !important;
}

.file_title_create_date
{
  text-align: center;
  font-size: 22pt !important;
}

.title_node
{
/* border-bottom:1px sold #44bd32 ;   */
}


.footer {
width: 100%;
height: auto;
font-family: montserrat;
/* background-color: #44bd32; */
border-top: 1px solid #44bd32;
font-size: 10px;
}

.footer_split {
width: 100%;
height: auto;
margin-top: 8px;
}

.footer_split_left {
width: 30%;
float: left;
}

.footer_split_middle {
width: 30%;
float: left;
}

.footer_split_right {
width: 40%;
float: left;
}
  


a 
{
  color:#28952b;
  font-weight: bold;
  text-decoration:none;
}
p 
{
  text-align: justify;
}


.front {
height: 8cm;
}
.break 
{
  page-break-before:always;
}
.dbreak 
{
  page-break-inside: avoid;
}

.dbreak_auto 
{
  page-break-inside: auto;
}

img {
height: auto;
/* box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); */
page-break-inside: avoid;
}
.rimg {
width: 90%;
border-radius: 8px;
}
/* 
.unite {
width: 100%;
border-radius: 8px;
display: flex;
}

.unite div
{
  flex: 23%;
  padding: 5px;
}

.unite img
{
  padding: 5px;
}
 */
.unite
{
    width: 100%;
    text-align: center;
    vertical-align: middle;
}

.unite div
{
    width: 22%;
    padding: 10px;
    display: inline-block;
}

.unite img
{
  width: 100%;
}

/* 
body {counter-reset: h2}
h2 {counter-reset: h3}
h3 {counter-reset: h4}
h4 {counter-reset: h5}
h5 {counter-reset: h6}

h2:before {counter-increment: h2; content: counter(h2) ". "}
h3:before {counter-increment: h3; content: counter(h2) "." counter(h3) ". "}
h4:before {counter-increment: h4; content: counter(h2) "." counter(h3) "." counter(h4) ". "}
h5:before {counter-increment: h5; content: counter(h2) "." counter(h3) "." counter(h4) "." counter(h5) ". "}
h6:before {counter-increment: h6; content: counter(h2) "." counter(h3) "." counter(h4) "." counter(h5) "." counter(h6) ". "}

h2.nocount:before, h3.nocount:before, h4.nocount:before, h5.nocount:before, h6.nocount:before { content: ""; counter-increment: none }  
*/

/* 
.clear_header {counter-reset: h2;}
h2 {counter-reset: h3;}
h3 {counter-reset: h4;}
h4 {counter-reset: h5;}
h2:before {counter-increment: h2;content: counter(h2)". ";}
h3:before {counter-increment: h3;content: counter(h2)"." counter(h3)". ";}
h4:before {counter-increment: h4;content: counter(h2)"." counter(h3)"." counter(h4)". ";}
h5:before {counter-increment: h5;content: counter(h2)"." counter(h3)"." counter(h4)"." counter(h5)". ";} */

.separate
{
    width: 100%;
}
.separate img
{
    width: 100%;
}



  
table 
{
margin: auto;
border-collapse: collapse;
width: 100%;
margin-block-start: 0pt;
margin-block-end: 20pt;
max-width: 100%;
}

.table table
{
  margin: auto;
  max-width: 100%;
}

.table table th
{
  /* 
  max-width: 125px; 
  */
}

tr
{
  border-bottom-width: 1px;
  border-bottom-style: dashed;
  border-bottom-color: #e4e6ef;
  border-radius: 2px;
  page-break-inside: avoid;
}

th 
{
  padding: 20px 30px;
  page-break-inside: avoid;
  border: none;
}

th, td 
{
  min-width: 85px;
  /* max-width: 150px; */
  text-align: left;
  border-collapse: collapse;
  padding: 10px;
  /* display: -webkit-box;  */
  word-break: break-word; 
}


.sum tr:last-child 
{
  background-color: dimgrey;
  color: #000;
  font-weight: bold;
}

.text_center
{
  text-align: center !important;
}


.hide
{
  display: none;
}

.header_green_text th
{
  color: #35A339;
}

.header_green_text tr:nth-child(odd) 
{
  background-color: #f9faf8; /*#f5f5f5;*/
}

.header_green_text td table
{
  border: none;
}

.header_green_text td th, .header_green_text td td
{
  font-size: 14px;
}

.break_before_table
{
  display: block;
}

.break_before_table table
{
  page-break-before: always;
}

.table_width50
{
  margin: auto;
}

.table_width501 table
{
  width: 50% !important;
  float: left !important;
  position: relative !important;
  left: 0 !important;
}


.index_header
{
  margin: 0px;
  margin-block-start: 0px !important;
  padding: 0px !important;
  margin-block-end: 0px !important;
  color: #4c4c4c;
}

body
{
  color: #2e2e2e;
}

.table_index tr
{
  border-bottom-width: 1px;
}

.table_index td
{
  padding: px;
  font-size: 16px !important;
}

.table_index a
{
  cursor: pointer;
}

.table_index h2
{
  padding: 7px;
  font-size: 20px !important;
  font-weight: 500;
}

.table_index h3
{
  padding: 7px;
  font-size: 18px !important;
}

.table_index h4
{
  padding: 7px;
  font-size: 16px !important;
  font-weight: 400;
}


.table_index span
{
  font-size: 20px !important;
  font-weight: 500 !important;
}