
from datetime import datetime
import boto3
from boto3.session import Session
from util import response as resp
import util.Log as log
import base64
import ReportPath as rp
from constants import constant as my_const

def get_aws_client(type,aws_s3_access_key,aws_s3_secret_key,aws_s3_region_name):
    try:
        client = boto3.client(type,region_name=aws_s3_region_name,aws_access_key_id=aws_s3_access_key,aws_secret_access_key=aws_s3_secret_key)
        return client,None
    except Exception as aws_err:
        return None,resp.MyResponse("E","get_aws_client,   :",aws_err)

def get_signed_url(client,aws_s3_bucket_name,new_file_name):
    signed_url = client.generate_presigned_url('get_object',Params={'Bucket': aws_s3_bucket_name,'Key': new_file_name},ExpiresIn=(60*60*24*6))#(60*60*24*7)
    return signed_url

def upload_file_genrate_url(client_name,client,aws_s3_bucket_name,file_path,actual_file_name):
    try:
        date_str = datetime.now().strftime("%Y_%m_%d_%H_%M_%S_")
        new_file_name = client_name+"/"+date_str+"_"+actual_file_name
        client.upload_file(file_path , aws_s3_bucket_name,new_file_name)
        data = client.get_object(Bucket=aws_s3_bucket_name, Key=new_file_name)
        contents = data['Body'].read()
        log.print_log("S","upload_file, success : ")
    except Exception as e:
        return None,resp.MyResponse("E","upload_file, failed  :",e)
    return new_file_name,None

def get_actual_key_serverurl(url_base64):
    try:
        return base64.b64decode(base64.b64decode(url_base64)).decode("utf-8")
    except:
        return ""


def send_mail_by_sendgrid(elastic_email_host,email_api_key,signed_url,config,new_file_name,actual_file_name,email_from_email_id,all_old_signed_url):
    try:
        client_email = my_const.global_settings.get("client_email")
        email_content = my_const.global_settings.get("email_html_content")
        email_content = email_content.replace("<SIGNED_URL>",'<a href="'+signed_url+'"> '+actual_file_name+'</a>')

        if all_old_signed_url != None and all_old_signed_url != "":
            email_content = email_content.replace("<RECENTLY_GENERATED_REPORTS>","Recently Generated Reports : ")
        else:
            email_content = email_content.replace("<RECENTLY_GENERATED_REPORTS>","")

        email_content = email_content.replace("<OLD_SIGNED_URL>",all_old_signed_url)

        from sendgrid import SendGridAPIClient
        from sendgrid.helpers.mail import Mail

        message = Mail(
        from_email=email_from_email_id,
        to_emails=client_email.split(","),
        subject="Mydbops Health Report - "+actual_file_name,
        html_content=email_content)
        
        sg = SendGridAPIClient(email_api_key)
        response = sg.send(message)
        log.print_log("S",response.status_code)
        log.print_log("S","send_mail_by_elastic, Send Mail successfully "+str(response.status_code))
    except Exception as e:
        return resp.MyResponse("E","send_mail Exception when calling Sendgrid->emails_transactional_post : ",e)


def send_mail_by_aws_ses(client_name,aws_ses_access_key,aws_ses_secret_key,aws_ses_region_name,signed_url,config,new_file_name,actual_file_name,email_from_email_id,all_old_signed_url):
    try:
        client,err = get_aws_client("ses",aws_ses_access_key,aws_ses_secret_key,aws_ses_region_name)
        if err is not None:
            return err

        client_email = my_const.global_settings.get("client_email")
        email_content = my_const.global_settings.get("email_html_content")
        email_content = email_content.replace("<SIGNED_URL>",'<a href="'+signed_url+'"> '+actual_file_name+'</a>')

        if all_old_signed_url != None and all_old_signed_url != "":
            email_content = email_content.replace("<RECENTLY_GENERATED_REPORTS>","Recently Generated Reports : ")
        else:
            email_content = email_content.replace("<RECENTLY_GENERATED_REPORTS>","")

        email_content = email_content.replace("<OLD_SIGNED_URL>",all_old_signed_url)

        # from sendgrid import SendGridAPIClient
        # from sendgrid.helpers.mail import Mail

        # message = Mail(
        # from_email=email_from_email_id,
        # to_emails=client_email.split(","),
        # subject="Mydbops Health Report - "+actual_file_name,
        # html_content=email_content)
        
        # sg = SendGridAPIClient(email_api_key)
        # response = sg.send(message)

        response = client.send_email(
            Destination={
                'ToAddresses': client_email.split(","),
            },
            Message={
                'Body': {
                    'Html': {
                        'Charset': 'UTF-8',
                        'Data': email_content,
                    },
                    'Text': {
                        'Charset': 'UTF-8',
                        'Data': email_content,
                    },
                },
                'Subject': {
                    'Charset': 'UTF-8',
                    'Data': client_name.title()+" Health Report - "+actual_file_name,
                },
            },
            Source=email_from_email_id,
        )

        # log.print_log("S",response)
        log.print_log("S","send_mail_by_aws_ses, Send Mail successfully "+str(response))
    except Exception as e:
        return resp.MyResponse("E","send_mail Exception when calling SES->emails_transactional_post : ",e)


def upload_and_send_mail(client_name,file_path,actual_file_name,config,report_for,created_date):
    credentials_for_aws_and_email = my_const.global_settings.get('api_aws_email') 
    if credentials_for_aws_and_email is not None:
        aws_s3_bucket_name = credentials_for_aws_and_email.get("aws_s3_bucket_name")
        aws_s3_access_key = credentials_for_aws_and_email.get("aws_s3_access_key")
        aws_s3_secret_key = credentials_for_aws_and_email.get("aws_s3_secret_key")
        aws_s3_region_name = credentials_for_aws_and_email.get("aws_s3_region_name")

        elastic_email_host = credentials_for_aws_and_email.get("elastic_email_host")
        elastic_email_api_key = credentials_for_aws_and_email.get("elastic_email_api_key")
        elastic_email_from_email_id = credentials_for_aws_and_email.get("elastic_email_from_email_id")

        aws_ses_access_key = credentials_for_aws_and_email.get("aws_ses_access_key")
        aws_ses_secret_key = credentials_for_aws_and_email.get("aws_ses_secret_key")
        aws_ses_region_name = credentials_for_aws_and_email.get("aws_ses_region_name")
        aws_ses_from_email_id = credentials_for_aws_and_email.get("aws_ses_from_email_id")

        client,err = get_aws_client("s3",aws_s3_access_key,aws_s3_secret_key,aws_s3_region_name)
        if err is not None:
            return err
        if client != None:
            new_file_name,err = upload_file_genrate_url(client_name,client,aws_s3_bucket_name,file_path,actual_file_name)
            if err is not None:
                return err

            signed_url = get_signed_url(client,aws_s3_bucket_name,new_file_name)
            return_list,err_file = rp.update_report_files(new_file_name,report_for,created_date)
            if err_file is not None:
                return err_file
            all_old_signed_url = ""
            count = 1
            for return_item in return_list:
                for file in return_item:
                    old_signed_url = get_signed_url(client,aws_s3_bucket_name,return_item.get(file))
                    if old_signed_url != None and old_signed_url != "":
                        old_signed_url = '<a style="font-size:12px !important" href="'+old_signed_url+'"> '+str(return_item.get(file))+'</a>'
                        count = count + 1
                        if all_old_signed_url == "":
                            all_old_signed_url = old_signed_url
                        else:
                            all_old_signed_url = all_old_signed_url + " \n<br/>\n " + old_signed_url
            if my_const.disable_send_mail is False:
                email_provider = my_const.global_settings.get("email_provider")
                if email_provider is not None and email_provider=="sendgrid":
                    if signed_url != "" and elastic_email_host != None and elastic_email_api_key != None: # and elastic_email_from_email_id != None
                        err = send_mail_by_sendgrid(elastic_email_host,elastic_email_api_key,signed_url,config,new_file_name,actual_file_name,elastic_email_from_email_id,all_old_signed_url)
                        if err is not None:
                            return err
                    else:
                        return resp.MyResponse("E","*** sendgrid email details are empty ***")
                elif email_provider is not None and email_provider=="ses":
                    if signed_url != "" and aws_ses_access_key != None and aws_ses_secret_key != None and aws_ses_region_name != None and aws_ses_from_email_id != None:
                        err = send_mail_by_aws_ses(client_name,aws_ses_access_key,aws_ses_secret_key,aws_ses_region_name,signed_url,config,new_file_name,actual_file_name,aws_ses_from_email_id,all_old_signed_url)
                        if err is not None:
                            return err
                    else:
                        return resp.MyResponse("E","*** aws ses details are empty ***")
    return None

# update credentials set information='{"aws_s3_bucket_name": "mydbreports","aws_s3_access_key": "********************","aws_s3_secret_key": "4kPEaU/yBtFrpJjRHSPigqT8SJWU8rsGA8UsyQKr","aws_s3_region_name": "ap-south-1","elastic_email_host": "https://api.sendgrid.com/","elastic_email_from_email_id": "<EMAIL>","elastic_email_api_key": "*********************************************************************","tk_server": "*************","tk_user": "vetri","tk_pass": "kSaX42Xzz4xDrt","tk_port": "3330","tk_db": "reports","aws_ses_access_key": "********************","aws_ses_secret_key": "m3JzEy8GiEHvBzFUp7aFHa3bDVm+D3yfwfy2gIPD","aws_ses_region_name": "ap-south-1","aws_ses_from_email_id":"<EMAIL>"}';