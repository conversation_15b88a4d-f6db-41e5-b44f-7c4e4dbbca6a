FROM ubuntu:20.04
  
LABEL maintainer="Vetrivel Natarajan"

ENV DEBIAN_FRONTEND noninteractive

ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    apt-get -y update && \
    apt-get --no-install-recommends install -y python3\
    python3-pip\
    python3-tk\
    mysql-client\
    postgresql-client\
    less\
    wget\
    curl \
    gnupg \
    git\
    unzip\
    supervisor\
    openssh-client\
    build-essential\
    libssl-dev\
    libxrender-dev\
    libx11-dev\
    libxext-dev\
    libfontconfig1-dev\
    libfreetype6-dev\
    fontconfig && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* &&\
    wget "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" &&\
    unzip awscli-exe-linux-x86_64.zip &&\
    ./aws/install &&\
    pip install --no-cache-dir kaleido fabric requests mysql.connector pyyaml pandas plotly pdfkit boto3 json2html sendgrid clickhouse_driver decorator

RUN curl -fsSL https://pgp.mongodb.com/server-4.4.asc | gpg -o /usr/share/keyrings/mongodb-server-4.4.gpg --dearmor

RUN echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-4.4.gpg ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-4.4.list

RUN apt-get update && apt-get install -y mongodb-org-shell=4.4.20

RUN mkdir -p -m 755 /opt/Mydbops_ReportEngine_Source/ReportEngine/plugin/pgmetrics_1.14.1_linux_amd64/

RUN wget -c  https://github.com/rapidloop/pgmetrics/releases/download/v1.14.1/pgmetrics_1.14.1_linux_amd64.tar.gz && \
    tar xvf pgmetrics_1.14.1_linux_amd64.tar.gz &&\
    mv pgmetrics_1.14.1_linux_amd64/* /opt/Mydbops_ReportEngine_Source/ReportEngine/plugin/pgmetrics_1.14.1_linux_amd64/

RUN wget -c  https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz && \
    tar xvf wkhtmltox*.tar.xz &&\
    mv wkhtmltox/bin/wkhtmlto* /usr/bin

RUN mkdir -p -m 755 /opt/Mydbops_ReportEngine_Source/ /var/log/schedular/ /usr/Mydbops_ReportEngine_Backup

COPY ReportEngine /usr/Mydbops_ReportEngine_Backup/ReportEngine

COPY ReportEngine /opt/Mydbops_ReportEngine_Source/ReportEngine

COPY supervisor.conf /etc/supervisor.conf

WORKDIR /opt/Mydbops_ReportEngine_Source/ReportEngine/

CMD ["bash", "-c", "while true; do sleep 60; done"] 
