fabric
requests
mysql.connector
kaleido
pdfkit
pyyaml
pandas
plotly
pycryptodome

#apt-get install mysql-client
#wkhtmltopdf install from url
#https://bitbucket.org/snippets/FanisTGT/neM5nA
#apt-get install -y wkhtmltopdf

#docker to localnode
#sudo docker cp mydb_report:/ReportEngine/my_reports/Report_weekly.pdf rw.pdf
#scp -P 2345 ubuntu@***********:/home/<USER>/rw.pdf rwl.pdf 


#sudo docker cp RE/ mydb_report:/ReportEngine/
#scp -P 2345 -r ReportEngine/ ubuntu@***********:~/

#scp -P 2345 -r DockerReport/ ubuntu@***********:~/DockerReport/

docker run -itd --name report_engine -v /Users/<USER>/.ssh:/root/.ssh -v /Users/<USER>/Desktop/delete/config:/opt/Mydbops_ReportEngine_Source/ReportEngine/config -v /Users/<USER>/Desktop/delete/template:/opt/Mydbops_ReportEngine_Source/ReportEngine/template  mydbops_health_report



docker run -itd --name report_engine -v /root/.ssh:/root/.ssh -v /home/<USER>/Docker_Volume/config:/opt/Mydbops_ReportEngine_Source/ReportEngine/config -v /home/<USER>/Docker_Volume/template:/opt/Mydbops_ReportEngine_Source/ReportEngine/template  mydbops_health_report


"""
https://bitbucket.org/snippets/FanisTGT/neM5nA
https://www.google.com/search?q=The+switch+--header-html%2C+is+not+support+using+unpatched+qt%2C&oq=The+switch+--header-html%2C+is+not+support+using+unpatched+qt%2C&aqs=chrome..69i57.598j0j4&sourceid=chrome&ie=UTF-8


    1  apt-get update
    2  apt-get install mysql-client
    3  apt-get install python3
    4  apt-get install python3-pip
    5  wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz
    6  apt-get install wget
    7  wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz
    8  tar xvf wkhtmltox*.tar.xz
    9  sudo mv wkhtmltox/bin/wkhtmlto* /usr/bin
   10  mv wkhtmltox/bin/wkhtmlto* /usr/bin
   11  apt-get install -y openssl build-essential libssl-dev libxrender-dev git-core libx11-dev libxext-dev libfontconfig1-dev libfreetype6-dev fontconfig
   14  pip install kaleido
   16  pip install fabric
   17  pip install requests
   18  pip install mysql.connector
   19  pip install pyyaml
   20  pip install pandas
   21  pip install plotly
   27  pip install pdfkit
   28  history




    1  apt-get update
    2  apt-get install mysql-client
    3  apt-get install python3
    4  apt-get install python3-pip
    5  wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz
    6  apt-get install wget
    7  wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz
    8  tar xvf wkhtmltox*.tar.xz
    9  sudo mv wkhtmltox/bin/wkhtmlto* /usr/bin
   10  mv wkhtmltox/bin/wkhtmlto* /usr/bin
   11  apt-get install -y openssl build-essential libssl-dev libxrender-dev git-core libx11-dev libxext-dev libfontconfig1-dev libfreetype6-dev fontconfig
   12  pip install kaleido
   13  apt-get install -y wkhtmltopdf
   14  pip install kaleido
   15  pip install pdfkit
   16  pip install fabric
   17  pip install requests
   18  pip install mysql.connector
   19  pip install pyyaml
   20  pip install pandas
   21  pip install plotly
   22  pip install -Y pandas
   23  pip install -y pandas
   24  pip install pandas -y
   25  pip -y install pandas
   26  pip install pandas
   27  pip install pdfkit
   28  history

"""




"""
select date_format(min(cdate),'%d-%m-%y %H:%i:%s') 'first_alert', date_format(max(cdate),'%d-%m-%y %H:%i:%s') 'last_alert', host, service, count(*) "unique_count", sum(count) "total_count", message from alerts_grouped where client='vymo' group by client, host, service order by cdate;
df.pivot(index="Created_On", columns="Status", values="Ticket")
.my.cnf default file name 

vi .my.cnf

[client]
user='root'
password='Rootdb123'
host='**************'
port=3306
"""
"""
mysql --defaults-file=/Users/<USER>/.my.cnf
"""
"""
mysql_config_editor set --login-path=mydbops_login --user=reports --host='*************' --port 3330 -p

mysql_config_editor set --login-path=mydbops_login --user=root --host='**************' -p
Enter password: 



 mysql_config_editor print --all                                                          
[mydbops_login]
user = "root"
password = *****
host = "**************"

vetriveln.1102@mydbops ~ % my_print_defaults -s mydbops_login
--user=root
--password=Rootdb123
--host=**************


vetriveln.1102@mydbops ~ % mysql --login-path=mydbops_login


"""

"""

ssh mysql "mysql -e \"select ifnull(table_schema,'TOTAL') as 'Database', round((sum(data_length+index_length+data_free)/1024/1024/1024),2) as 'Size (GB)' from information_schema.tables where table_schema not in ('mysql','information_schema','performance_schema','sys') group by table_schema with rollup \""
Database	Size (GB)
test	0.00
TOTAL	0.00


"""


"""
LOCAL:

mysql -h ************* -u reports -pfetch_report -P 3330 -e "show databases;"  

mysql --defaults-file=~/.my.cnf -e "show databases;"

mysql --login-path=mydbops_login -e "show databases;"

SSH:
ssh bastion "mysql -h ************* -u reports -pfetch_report -P 3330 -e \"show databases;\"" 

ssh bastion "mysql --defaults-file=~/.my.cnf -e \"show databases;\"" 

ssh bastion "mysql --login-path=mydbops_login -e \"show databases;\"" 

"""

   
   cd Gitpush/
   docker stop report_engine && docker rm report_engine
   docker build -t mydbops_health_report .
   docker run -itd --name report_engine -v /root/.ssh:/root/.ssh -v /home/<USER>/Docker_Volume/config:/opt/Mydbops_ReportEngine_Source/ReportEngine/config -v /home/<USER>/Docker_Volume/template:/opt/Mydbops_ReportEngine_Source/ReportEngine/template  mydbops_health_report
   docker exec -it report_engine bash
   docker tag mydbops_health_report vetriveln/mydbops_health_report
   docker push vetriveln/mydbops_health_report
   docker exec -it report_engine bash
   



# http://fitnessforce.heimdall.mydbops.com/graph/d/l7S7ngxMkv2/mydb_health_report?
# from=1654003982742&to=1654608782742&orgId=1&viewPanel=307&var-interval=1m&var-host=fitnessforce_replica_mysql
# &var-service_name=fitnessforce_replica_mysql
# &var-service_id=%2Fservice_id%2Febfcddb7-b6de-4c59-80e1-700b6ad536db



# http://fitnessforce.heimdall.mydbops.com/graph/render/d-solo/l7S7ngxMkv2/mydb_health_report?
# width=1000&height=500&var-interval=1m&orgId=1&panelId=307&var-host=fitnessforce_replica_mysql&from=1654003982742&to=1654608782742&var-service_name=fitnessforce_replica_mysql



# Replace `<username>` and `<password>` with the username and password
# you want to use for your admin user credentials.
# sensuctl -n --username 'admin' --password 'admin' --namespace default --url 'http://**************:8080'

#  docker run -v /var/lib/sensu:/var/lib/sensu -d \
# --name sensu-agent sensu/sensu:latest \
# sensu-agent start --backend-url ws://**************:8081 --log-level debug --subscriptions system --api-host 0.0.0.0 --cache-dir /var/lib/sensu


